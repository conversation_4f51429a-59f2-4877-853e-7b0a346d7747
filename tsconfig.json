{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/pages/*": ["pages/*"], "@/services/*": ["services/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/assets/*": ["assets/*"], "@/hooks/*": ["hooks/*"], "@/store/*": ["store/*"]}, "declaration": true, "declarationMap": true, "sourceMap": true, "incremental": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": false, "noImplicitOverride": false, "noUncheckedIndexedAccess": false}, "include": ["src/**/*", "public/electron.js"], "exclude": ["node_modules", "build", "dist", "**/*.test.ts", "**/*.test.tsx"], "ts-node": {"compilerOptions": {"module": "commonjs"}}}