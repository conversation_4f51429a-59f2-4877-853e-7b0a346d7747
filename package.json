{"name": "elderly-meal-cashier", "version": "1.0.0", "description": "助老订餐收银系统 - 基于React18+TypeScript+Electron的跨平台收银系统", "main": "public/electron.js", "homepage": "./", "private": true, "author": {"name": "助老收银团队", "email": "<EMAIL>"}, "keywords": ["react", "typescript", "electron", "cashier", "elderly", "ai-recognition"], "dependencies": {"@capacitor/android": "^7.2.0", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/filesystem": "^7.1.1", "@capacitor/haptics": "^7.0.1", "@capacitor/keyboard": "^7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/push-notifications": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@reduxjs/toolkit": "^1.9.7", "@tensorflow/tfjs": "^4.12.0", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "antd": "^5.11.5", "axios": "^1.6.1", "dayjs": "^1.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.3", "react-router-dom": "^6.18.0", "react-scripts": "5.0.1", "typescript": "^5.2.2", "web-vitals": "^3.5.0"}, "devDependencies": {"@electron/rebuild": "^3.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^19.1.9", "electron-builder": "^24.7.0", "electron-devtools-installer": "^3.2.0", "electron-is-dev": "^2.0.0", "electron-updater": "^6.1.4", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "ts-jest": "^29.3.4", "wait-on": "^7.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:ci": "react-scripts test --coverage --watchAll=false --ci", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-build": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux", "cap:add:android": "npx cap add android", "cap:sync": "npx cap sync", "cap:open:android": "npx cap open android", "cap:build:android": "npm run build && npx cap sync android && npx cap open android", "postinstall": "electron-builder install-app-deps", "lint": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,scss}", "type-check": "tsc --noEmit", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.elderly.cashier", "productName": "助老订餐收银系统", "copyright": "Copyright © 2024 助老收银团队", "directories": {"output": "dist", "cache": "cache"}, "files": ["build/**/*", "public/electron.js", "public/preload.js", "public/splash.html", "public/*.ico", "public/*.png", "public/*.icns", "node_modules/**/*", "!node_modules/**/*.{md,txt,LICENSE}"], "extraResources": [{"from": "resources/", "to": "resources/", "filter": ["**/*"]}], "protocols": [{"name": "elderly-cashier", "schemes": ["elderly-cashier"]}], "publish": [{"provider": "github", "owner": "elderly-cashier", "repo": "cashier-releases"}], "win": {"icon": "public/icon.ico", "target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "publisherName": "助老收银团队", "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker"}, "mac": {"icon": "public/icon.icns", "category": "public.app-category.business", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "hardenedRuntime": true, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "linux": {"icon": "public/icon.png", "category": "Office", "target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "desktop": {"Name": "助老订餐收银系统", "Comment": "专为老年人设计的智能收银系统", "Categories": "Office;Finance;"}}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "助老订餐收银系统", "uninstallDisplayName": "助老订餐收银系统", "license": "resources/license.txt", "installerLanguages": ["zh_CN", "en_US"], "language": "2052", "include": "build/installer.nsh", "warningsAsErrors": false}, "dmg": {"title": "助老订餐收银系统安装包", "icon": "public/icon.icns", "background": "build/dmg-background.png", "window": {"width": 600, "height": 400}, "contents": [{"x": 180, "y": 220, "type": "file"}, {"x": 420, "y": 220, "type": "link", "path": "/Applications"}]}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}, "appImage": {"artifactName": "${productName}-${version}.${ext}"}, "compression": "maximum", "removePackageScripts": true, "buildVersion": "1.0.0"}, "repository": {"type": "git", "url": "https://github.com/elderly-cashier/cashier.git"}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "prettier --write"]}}