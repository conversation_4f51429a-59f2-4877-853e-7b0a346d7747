import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.elderly.cashier',
  appName: '助老订餐收银系统',
  webDir: 'build',
  server: {
    androidScheme: 'https'
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 3000,
      launchAutoHide: true,
      backgroundColor: "#667eea",
      androidSplashResourceName: "splash",
      androidScaleType: "CENTER_CROP",
      showSpinner: true,
      androidSpinnerStyle: "large",
      iosSpinnerStyle: "small",
      spinnerColor: "#ffffff",
      splashFullScreen: true,
      splashImmersive: true,
      layoutName: "launch_screen",
      useDialog: true,
    },
    StatusBar: {
      style: 'LIGHT',
      backgroundColor: '#667eea',
    },
    Keyboard: {
      resize: 'body',
      style: 'dark',
      resizeOnFullScreen: true,
    },
    Camera: {
      permissions: {
        camera: 'This app needs access to camera to scan food items',
      },
    },
    PushNotifications: {
      presentationOptions: ["badge", "sound", "alert"],
    },
    Haptics: {},
    Network: {},
    Filesystem: {
      permissions: {
        publicStorage: 'This app needs access to storage to save receipts and data',
      },
    },
  },
  android: {
    buildOptions: {
      keystorePath: undefined,
      keystorePassword: undefined,
      keystoreAlias: undefined,
      keystoreAliasPassword: undefined,
      releaseType: 'APK',
      signingType: 'apksigner'
    },
    webContentsDebuggingEnabled: true,
    allowMixedContent: true,
    appendUserAgent: 'ElderlyCashier/1.0.0',
    backgroundColor: '#ffffff',
  },
  ios: {
    scheme: 'ElderlyCashier',
    contentInset: 'automatic',
    scrollEnabled: true,
    backgroundColor: '#ffffff',
  }
};

export default config;
