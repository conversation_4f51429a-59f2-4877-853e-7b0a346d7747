# 助老订餐收银系统 - API接口文档

## 📋 目录
1. [接口概述](#接口概述)
2. [认证授权](#认证授权)
3. [通用规范](#通用规范)
4. [用户管理](#用户管理)
5. [商品管理](#商品管理)
6. [订单管理](#订单管理)
7. [会员管理](#会员管理)
8. [支付管理](#支付管理)
9. [AI识别](#ai识别)
10. [财务管理](#财务管理)
11. [系统管理](#系统管理)
12. [错误码说明](#错误码说明)

---

## 🔗 接口概述

### 基础信息
- **API版本**: v1.0
- **基础URL**: `https://api.elderly-cashier.com/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 请求头
```http
Content-Type: application/json
Authorization: Bearer <token>
X-Request-ID: <unique-request-id>
User-Agent: ElderCashier/1.0.0
```

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-12-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

---

## 🔐 认证授权

### 登录认证

#### POST /auth/login
用户登录认证

**请求参数**
```json
{
  "username": "string",     // 用户名
  "password": "string",     // 密码
  "deviceInfo": {           // 设备信息（可选）
    "deviceId": "string",
    "deviceType": "desktop|mobile",
    "os": "string",
    "version": "string"
  }
}
```

**响应示例**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here",
    "expiresIn": 7200,
    "user": {
      "id": "1",
      "username": "cashier01",
      "role": "cashier",
      "displayName": "收银员张三",
      "permissions": ["order:create", "product:read"],
      "lastLoginAt": "2024-12-15T10:30:00Z"
    }
  }
}
```

### 刷新令牌

#### POST /auth/refresh
刷新访问令牌

**请求参数**
```json
{
  "refreshToken": "refresh_token_here"
}
```

### 退出登录

#### POST /auth/logout
用户退出登录

**请求头**
```http
Authorization: Bearer <token>
```

**响应示例**
```json
{
  "code": 200,
  "message": "退出成功"
}
```

---

## 📝 通用规范

### 分页参数
```json
{
  "page": 1,           // 页码，从1开始
  "pageSize": 20,      // 每页数量，默认20，最大100
  "sortBy": "createdAt", // 排序字段
  "sortOrder": "desc"   // 排序方式：asc|desc
}
```

### 分页响应
```json
{
  "data": {
    "items": [],        // 数据列表
    "total": 100,       // 总数量
    "page": 1,          // 当前页码
    "pageSize": 20,     // 每页数量
    "totalPages": 5     // 总页数
  }
}
```

### 时间格式
- 统一使用 ISO 8601 格式：`2024-12-15T10:30:00Z`

### 货币格式
- 金额单位统一为分（整数）
- 例如：10.50元表示为1050

---

## 👤 用户管理

### 获取用户信息

#### GET /users/profile
获取当前用户信息

**响应示例**
```json
{
  "code": 200,
  "data": {
    "id": "1",
    "username": "cashier01",
    "displayName": "收银员张三",
    "role": "cashier",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "status": "active",
    "permissions": ["order:create", "product:read"],
    "createdAt": "2024-01-01T00:00:00Z",
    "lastLoginAt": "2024-12-15T10:30:00Z"
  }
}
```

### 修改密码

#### PUT /users/password
修改用户密码

**请求参数**
```json
{
  "oldPassword": "old_password",
  "newPassword": "new_password",
  "confirmPassword": "new_password"
}
```

### 获取用户列表（管理员）

#### GET /users
获取用户列表

**查询参数**
- `page`: 页码
- `pageSize`: 每页数量
- `role`: 角色筛选（admin|cashier）
- `status`: 状态筛选（active|inactive）
- `keyword`: 关键词搜索

**响应示例**
```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": "1",
        "username": "cashier01",
        "displayName": "收银员张三",
        "role": "cashier",
        "status": "active",
        "lastLoginAt": "2024-12-15T10:30:00Z"
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 20
  }
}
```

---

## 🛍️ 商品管理

### 获取商品列表

#### GET /products
获取商品列表

**查询参数**
- `page`: 页码
- `pageSize`: 每页数量
- `category`: 分类ID
- `keyword`: 商品名称关键词
- `status`: 状态（active|inactive）
- `priceMin`: 最低价格（分）
- `priceMax`: 最高价格（分）

**响应示例**
```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": "1",
        "name": "红烧肉",
        "price": 2800,           // 28.00元
        "originalPrice": 3000,   // 原价30.00元
        "category": "主食",
        "categoryId": "1",
        "description": "香嫩可口的红烧肉",
        "imageUrl": "https://cdn.example.com/product1.jpg",
        "status": "active",
        "stock": 50,
        "soldCount": 120,
        "tags": ["热销", "招牌"],
        "nutrition": {
          "calories": 350,
          "protein": 25,
          "carbs": 15,
          "fat": 20
        },
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-12-15T10:30:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20
  }
}
```

### 获取商品详情

#### GET /products/{id}
获取指定商品详情

**路径参数**
- `id`: 商品ID

**响应示例**
```json
{
  "code": 200,
  "data": {
    "id": "1",
    "name": "红烧肉",
    "price": 2800,
    "description": "香嫩可口的红烧肉，采用优质五花肉精心制作",
    "imageUrl": "https://cdn.example.com/product1.jpg",
    "images": [
      "https://cdn.example.com/product1_1.jpg",
      "https://cdn.example.com/product1_2.jpg"
    ],
    "category": "主食",
    "ingredients": ["五花肉", "生抽", "老抽", "冰糖"],
    "allergens": ["大豆"],
    "spicyLevel": 1,  // 辣度等级 0-5
    "preparationTime": 15, // 制作时间（分钟）
    "nutrition": {
      "calories": 350,
      "protein": 25,
      "carbs": 15,
      "fat": 20,
      "sodium": 800
    }
  }
}
```

### 获取商品分类

#### GET /categories
获取商品分类列表

**响应示例**
```json
{
  "code": 200,
  "data": [
    {
      "id": "1",
      "name": "主食",
      "icon": "🍚",
      "sortOrder": 1,
      "productCount": 25,
      "status": "active"
    },
    {
      "id": "2",
      "name": "汤品",
      "icon": "🍲",
      "sortOrder": 2,
      "productCount": 15,
      "status": "active"
    }
  ]
}
```

### 搜索商品

#### GET /products/search
搜索商品

**查询参数**
- `q`: 搜索关键词
- `category`: 分类筛选
- `sortBy`: 排序方式（price|name|popular）
- `sortOrder`: 排序顺序（asc|desc）

**响应示例**
```json
{
  "code": 200,
  "data": {
    "items": [],
    "suggestions": ["红烧肉", "红烧鱼", "红烧排骨"],
    "total": 5
  }
}
```

---

## 📋 订单管理

### 创建订单

#### POST /orders
创建新订单

**请求参数**
```json
{
  "items": [
    {
      "productId": "1",
      "quantity": 2,
      "price": 2800,       // 商品单价（分）
      "note": "不要辣"      // 备注（可选）
    }
  ],
  "memberId": "123",         // 会员ID（可选）
  "discounts": [             // 优惠信息（可选）
    {
      "type": "coupon",
      "id": "coupon_123",
      "amount": 500
    }
  ],
  "note": "打包带走",        // 订单备注
  "diningType": "takeaway"   // 就餐方式：dine_in|takeaway
}
```

**响应示例**
```json
{
  "code": 200,
  "data": {
    "id": "order_123456",
    "orderNo": "ORD20241215001",
    "status": "pending",
    "totalAmount": 5600,     // 总金额（分）
    "discountAmount": 500,   // 优惠金额
    "payableAmount": 5100,   // 应付金额
    "items": [
      {
        "productId": "1",
        "productName": "红烧肉",
        "quantity": 2,
        "price": 2800,
        "amount": 5600,
        "note": "不要辣"
      }
    ],
    "member": {
      "id": "123",
      "name": "张三",
      "phone": "13800138000"
    },
    "createdAt": "2024-12-15T10:30:00Z"
  }
}
```

### 获取订单列表

#### GET /orders
获取订单列表

**查询参数**
- `page`: 页码
- `pageSize`: 每页数量
- `status`: 订单状态（pending|paid|completed|cancelled）
- `dateFrom`: 开始日期
- `dateTo`: 结束日期
- `memberId`: 会员ID
- `orderNo`: 订单号

**响应示例**
```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": "order_123456",
        "orderNo": "ORD20241215001",
        "status": "completed",
        "totalAmount": 5100,
        "itemCount": 2,
        "memberName": "张三",
        "cashierName": "收银员李四",
        "createdAt": "2024-12-15T10:30:00Z",
        "completedAt": "2024-12-15T10:35:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20
  }
}
```

### 获取订单详情

#### GET /orders/{id}
获取指定订单详情

**响应示例**
```json
{
  "code": 200,
  "data": {
    "id": "order_123456",
    "orderNo": "ORD20241215001",
    "status": "completed",
    "totalAmount": 5600,
    "discountAmount": 500,
    "payableAmount": 5100,
    "paidAmount": 5100,
    "changeAmount": 0,
    "items": [
      {
        "productId": "1",
        "productName": "红烧肉",
        "quantity": 2,
        "price": 2800,
        "amount": 5600,
        "note": "不要辣"
      }
    ],
    "member": {
      "id": "123",
      "name": "张三",
      "phone": "13800138000",
      "pointsEarned": 51
    },
    "payments": [
      {
        "method": "cash",
        "amount": 5100,
        "receivedAmount": 6000,
        "changeAmount": 900,
        "paidAt": "2024-12-15T10:32:00Z"
      }
    ],
    "cashier": {
      "id": "1",
      "name": "收银员李四"
    },
    "createdAt": "2024-12-15T10:30:00Z",
    "completedAt": "2024-12-15T10:35:00Z"
  }
}
```

### 更新订单状态

#### PUT /orders/{id}/status
更新订单状态

**请求参数**
```json
{
  "status": "completed",
  "note": "订单完成"
}
```

### 取消订单

#### DELETE /orders/{id}
取消订单

**请求参数**
```json
{
  "reason": "客户要求取消",
  "note": "备注信息"
}
```

---

## 👥 会员管理

### 搜索会员

#### GET /members/search
搜索会员

**查询参数**
- `keyword`: 搜索关键词（手机号、姓名、会员卡号）
- `phone`: 手机号精确搜索
- `cardNo`: 会员卡号搜索

**响应示例**
```json
{
  "code": 200,
  "data": [
    {
      "id": "123",
      "name": "张三",
      "phone": "13800138000",
      "cardNo": "MB20240001",
      "level": "gold",
      "levelName": "金卡会员",
      "points": 1200,
      "balance": 5000,        // 储值余额（分）
      "avatar": "https://cdn.example.com/avatar.jpg",
      "birthDate": "1960-05-15",
      "gender": "male",
      "registerDate": "2024-01-01",
      "lastConsumeDate": "2024-12-10"
    }
  ]
}
```

### 获取会员详情

#### GET /members/{id}
获取会员详细信息

**响应示例**
```json
{
  "code": 200,
  "data": {
    "id": "123",
    "name": "张三",
    "phone": "13800138000",
    "cardNo": "MB20240001",
    "level": "gold",
    "levelName": "金卡会员",
    "points": 1200,
    "balance": 5000,
    "totalConsume": 25000,   // 累计消费（分）
    "consumeCount": 50,      // 消费次数
    "avatar": "https://cdn.example.com/avatar.jpg",
    "birthDate": "1960-05-15",
    "gender": "male",
    "address": "北京市朝阳区xxx街道",
    "emergencyContact": {
      "name": "张四",
      "phone": "13900139000",
      "relationship": "子女"
    },
    "preferences": {
      "spicyLevel": 1,
      "allergies": ["花生", "海鲜"],
      "favoriteCategories": ["主食", "汤品"]
    },
    "registerDate": "2024-01-01",
    "lastConsumeDate": "2024-12-10",
    "coupons": [
      {
        "id": "coupon_123",
        "name": "满50减10",
        "type": "discount",
        "value": 1000,
        "minAmount": 5000,
        "expiryDate": "2024-12-31"
      }
    ]
  }
}
```

### 获取会员积分记录

#### GET /members/{id}/points
获取会员积分变动记录

**查询参数**
- `page`: 页码
- `pageSize`: 每页数量
- `type`: 类型（earn|redeem|expire）

**响应示例**
```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": "point_123",
        "type": "earn",
        "amount": 50,
        "balance": 1200,
        "description": "消费获得积分",
        "orderId": "order_123456",
        "createdAt": "2024-12-15T10:35:00Z"
      }
    ],
    "total": 20,
    "page": 1,
    "pageSize": 10
  }
}
```

### 使用积分

#### POST /members/{id}/points/redeem
使用积分抵扣

**请求参数**
```json
{
  "points": 100,          // 使用积分数
  "orderId": "order_123", // 关联订单ID
  "note": "积分抵扣"
}
```

---

## 💰 支付管理

### 创建支付

#### POST /payments
创建支付订单

**请求参数**
```json
{
  "orderId": "order_123456",
  "amount": 5100,           // 支付金额（分）
  "method": "wechat",       // 支付方式：cash|wechat|alipay|card
  "receivedAmount": 6000,   // 收到金额（现金支付时）
  "note": "支付备注"
}
```

**响应示例**
```json
{
  "code": 200,
  "data": {
    "id": "pay_123456",
    "orderId": "order_123456",
    "method": "wechat",
    "amount": 5100,
    "status": "pending",
    "qrCode": "https://api.payment.com/qr/xxx", // 二维码URL
    "expireAt": "2024-12-15T10:45:00Z",        // 过期时间
    "createdAt": "2024-12-15T10:30:00Z"
  }
}
```

### 查询支付状态

#### GET /payments/{id}
查询支付状态

**响应示例**
```json
{
  "code": 200,
  "data": {
    "id": "pay_123456",
    "orderId": "order_123456",
    "method": "wechat",
    "amount": 5100,
    "status": "success",     // pending|success|failed|cancelled
    "transactionId": "wx_trans_123",
    "paidAt": "2024-12-15T10:32:00Z",
    "createdAt": "2024-12-15T10:30:00Z"
  }
}
```

### 现金支付确认

#### POST /payments/{id}/confirm
确认现金支付

**请求参数**
```json
{
  "receivedAmount": 6000,   // 收到金额（分）
  "changeAmount": 900,      // 找零金额（分）
  "note": "现金支付"
}
```

### 支付退款

#### POST /payments/{id}/refund
申请支付退款

**请求参数**
```json
{
  "amount": 5100,          // 退款金额（分）
  "reason": "客户要求退款",
  "note": "退款备注"
}
```

---

## 🤖 AI识别

### 开始识别

#### POST /ai/recognition/start
开始AI识别

**请求参数**
```json
{
  "imageData": "base64_encoded_image_data",
  "imageFormat": "jpeg",   // 图片格式：jpeg|png
  "sessionId": "session_123", // 会话ID（可选）
  "settings": {
    "confidence": 0.8,     // 置信度阈值
    "maxResults": 5        // 最大返回结果数
  }
}
```

**响应示例**
```json
{
  "code": 200,
  "data": {
    "sessionId": "session_123",
    "results": [
      {
        "productId": "1",
        "productName": "红烧肉",
        "confidence": 0.92,
        "bbox": {              // 识别区域坐标
          "x": 100,
          "y": 50,
          "width": 200,
          "height": 150
        },
        "price": 2800,
        "category": "主食"
      },
      {
        "productId": "2",
        "productName": "米饭",
        "confidence": 0.85,
        "bbox": {
          "x": 320,
          "y": 80,
          "width": 150,
          "height": 120
        },
        "price": 300,
        "category": "主食"
      }
    ],
    "processingTime": 1.2,   // 处理时间（秒）
    "imageId": "img_123456"
  }
}
```

### 获取识别历史

#### GET /ai/recognition/history
获取识别历史记录

**查询参数**
- `page`: 页码
- `pageSize`: 每页数量
- `dateFrom`: 开始日期
- `dateTo`: 结束日期

**响应示例**
```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": "recognition_123",
        "sessionId": "session_123",
        "imageId": "img_123456",
        "resultCount": 2,
        "processingTime": 1.2,
        "success": true,
        "createdAt": "2024-12-15T10:30:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20
  }
}
```

### 确认识别结果

#### POST /ai/recognition/{id}/confirm
确认识别结果并添加到订单

**请求参数**
```json
{
  "selectedResults": [
    {
      "productId": "1",
      "quantity": 1,
      "confirmed": true
    }
  ],
  "orderId": "order_123456"  // 目标订单ID（可选）
}
```

### 模型管理

#### GET /ai/models
获取可用模型列表

**响应示例**
```json
{
  "code": 200,
  "data": [
    {
      "id": "model_v1",
      "name": "食物识别模型 v1.0",
      "version": "1.0.0",
      "status": "active",
      "accuracy": 0.92,
      "supportedCategories": ["主食", "汤品", "小菜"],
      "lastUpdated": "2024-12-01T00:00:00Z"
    }
  ]
}
```

---

## 💼 财务管理

### 获取销售统计

#### GET /finance/sales/stats
获取销售统计数据

**查询参数**
- `period`: 统计周期（day|week|month|year）
- `dateFrom`: 开始日期
- `dateTo`: 结束日期
- `cashierId`: 收银员ID（可选）

**响应示例**
```json
{
  "code": 200,
  "data": {
    "summary": {
      "totalAmount": 120000,    // 总销售额（分）
      "orderCount": 240,        // 订单数量
      "customerCount": 180,     // 客户数量
      "avgOrderAmount": 5000,   // 平均订单金额
      "growth": {
        "amount": 0.15,         // 销售额增长率
        "orders": 0.08          // 订单量增长率
      }
    },
    "daily": [
      {
        "date": "2024-12-15",
        "amount": 15000,
        "orderCount": 30,
        "customerCount": 25
      }
    ],
    "byCategory": [
      {
        "category": "主食",
        "amount": 60000,
        "percentage": 0.5,
        "orderCount": 120
      }
    ],
    "byPaymentMethod": [
      {
        "method": "wechat",
        "amount": 70000,
        "percentage": 0.58,
        "orderCount": 140
      }
    ]
  }
}
```

### 获取收银对账

#### GET /finance/cashier/reconcile
获取收银员对账数据

**查询参数**
- `cashierId`: 收银员ID
- `date`: 对账日期（YYYY-MM-DD）

**响应示例**
```json
{
  "code": 200,
  "data": {
    "cashier": {
      "id": "1",
      "name": "收银员李四"
    },
    "date": "2024-12-15",
    "summary": {
      "totalAmount": 25000,     // 总收款（分）
      "orderCount": 50,         // 订单数量
      "startTime": "08:00:00",
      "endTime": "18:00:00"
    },
    "byPaymentMethod": [
      {
        "method": "cash",
        "amount": 10000,
        "orderCount": 20,
        "details": {
          "received": 12000,    // 收到现金
          "change": 2000        // 找零
        }
      },
      {
        "method": "wechat",
        "amount": 15000,
        "orderCount": 30
      }
    ],
    "discrepancy": {
      "cashAmount": 0,          // 现金差异
      "note": "无差异"
    }
  }
}
```

### 退款申请

#### POST /finance/refunds
创建退款申请

**请求参数**
```json
{
  "orderId": "order_123456",
  "amount": 5100,           // 退款金额（分）
  "reason": "客户要求退款",
  "items": [                // 退款商品（可选）
    {
      "productId": "1",
      "quantity": 1,
      "amount": 2800
    }
  ],
  "note": "退款备注"
}
```

**响应示例**
```json
{
  "code": 200,
  "data": {
    "id": "refund_123456",
    "orderId": "order_123456",
    "amount": 5100,
    "status": "pending",      // pending|approved|rejected|completed
    "reason": "客户要求退款",
    "applicantId": "1",
    "createdAt": "2024-12-15T10:30:00Z"
  }
}
```

---

## ⚙️ 系统管理

### 系统状态

#### GET /system/health
获取系统健康状态

**响应示例**
```json
{
  "code": 200,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": 86400,          // 运行时间（秒）
    "timestamp": "2024-12-15T10:30:00Z",
    "services": {
      "database": "healthy",
      "ai_service": "healthy",
      "payment_service": "healthy",
      "cache": "healthy"
    },
    "metrics": {
      "cpu_usage": 0.45,
      "memory_usage": 0.67,
      "disk_usage": 0.32,
      "active_connections": 25
    }
  }
}
```

### 系统配置

#### GET /system/config
获取系统配置

**响应示例**
```json
{
  "code": 200,
  "data": {
    "app": {
      "name": "助老订餐收银系统",
      "version": "1.0.0",
      "timezone": "Asia/Shanghai"
    },
    "features": {
      "elderlyMode": true,
      "aiRecognition": true,
      "memberSystem": true,
      "multiPayment": true
    },
    "limits": {
      "maxOrderItems": 50,
      "maxImageSize": 5242880,  // 5MB
      "sessionTimeout": 7200    // 2小时
    },
    "ui": {
      "theme": "elderly",
      "fontSize": "large",
      "contrast": "high"
    }
  }
}
```

### 操作日志

#### GET /system/logs
获取操作日志

**查询参数**
- `page`: 页码
- `pageSize`: 每页数量
- `level`: 日志级别（info|warn|error）
- `module`: 模块名称
- `userId`: 用户ID
- `dateFrom`: 开始日期
- `dateTo`: 结束日期

**响应示例**
```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": "log_123456",
        "level": "info",
        "module": "order",
        "action": "create_order",
        "message": "创建订单",
        "userId": "1",
        "userName": "收银员李四",
        "ip": "*************",
        "userAgent": "ElderCashier/1.0.0",
        "details": {
          "orderId": "order_123456",
          "amount": 5100
        },
        "createdAt": "2024-12-15T10:30:00Z"
      }
    ],
    "total": 1000,
    "page": 1,
    "pageSize": 20
  }
}
```

### 数据备份

#### POST /system/backup
创建数据备份

**请求参数**
```json
{
  "type": "full",           // full|incremental
  "includeImages": true,    // 是否包含图片
  "compression": true,      // 是否压缩
  "note": "手动备份"
}
```

**响应示例**
```json
{
  "code": 200,
  "data": {
    "id": "backup_123456",
    "type": "full",
    "status": "processing",   // processing|completed|failed
    "fileSize": 0,
    "downloadUrl": null,
    "createdAt": "2024-12-15T10:30:00Z"
  }
}
```

---

## ❌ 错误码说明

### HTTP状态码
- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `409`: 资源冲突
- `422`: 请求参数验证失败
- `429`: 请求频率限制
- `500`: 服务器内部错误
- `503`: 服务不可用

### 业务错误码

#### 认证相关 (1000-1099)
- `1001`: 用户名或密码错误
- `1002`: Token无效或已过期
- `1003`: 账号已被锁定
- `1004`: 权限不足
- `1005`: 登录设备数量超限

#### 参数验证 (1100-1199)
- `1101`: 必填参数缺失
- `1102`: 参数格式错误
- `1103`: 参数值超出范围
- `1104`: 参数值重复
- `1105`: 图片格式不支持

#### 业务逻辑 (1200-1299)
- `1201`: 商品不存在或已下架
- `1202`: 库存不足
- `1203`: 订单状态不允许该操作
- `1204`: 会员不存在
- `1205`: 余额不足
- `1206`: 积分不足
- `1207`: 优惠券不可用

#### AI识别 (1300-1399)
- `1301`: 图片识别失败
- `1302`: 识别结果置信度过低
- `1303`: AI服务不可用
- `1304`: 图片尺寸超限
- `1305`: 识别超时

#### 支付相关 (1400-1499)
- `1401`: 支付失败
- `1402`: 支付方式不支持
- `1403`: 支付金额不匹配
- `1404`: 支付已超时
- `1405`: 重复支付

#### 系统错误 (1500-1599)
- `1501`: 数据库连接失败
- `1502`: 文件上传失败
- `1503`: 服务暂时不可用
- `1504`: 请求频率过高
- `1505`: 系统维护中

### 错误响应格式
```json
{
  "code": 1001,
  "message": "用户名或密码错误",
  "details": {
    "field": "password",
    "reason": "密码不正确"
  },
  "timestamp": "2024-12-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

---

## 📞 技术支持

### 联系方式
- **API文档**: https://docs.elderly-cashier.com/api
- **技术支持**: <EMAIL>
- **问题反馈**: https://github.com/elderly-cashier/cashier/issues

### SDK支持
- **JavaScript SDK**: https://www.npmjs.com/package/elderly-cashier-sdk
- **Python SDK**: https://pypi.org/project/elderly-cashier/

### 更新日志
- **v1.0.0** (2024-12-15): 初始版本发布
- 完整的收银功能API
- AI识别接口
- 会员管理系统
- 支付集成

---

*API文档版本：v1.0 | 更新时间：2024年12月15日*

*如有疑问，请联系我们的技术支持团队！* 