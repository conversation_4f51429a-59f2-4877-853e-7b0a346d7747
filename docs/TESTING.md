# 助老订餐收银系统 - 测试文档

## 测试框架概述

本项目采用多层次的测试策略，确保系统质量和用户体验：

### 1. 单元测试 (Unit Tests)
- **框架**: Jest + React Testing Library
- **覆盖范围**: 组件、服务、工具函数
- **目标覆盖率**: ≥80%

### 2. 集成测试 (Integration Tests)
- **框架**: Jest + MSW (Mock Service Worker)
- **覆盖范围**: API集成、Redux状态管理
- **重点**: 数据流和组件交互

### 3. 端到端测试 (E2E Tests)
- **框架**: Playwright
- **覆盖范围**: 完整用户流程
- **重点**: 适老化可用性、跨浏览器兼容性

### 4. 性能测试 (Performance Tests)
- **工具**: Lighthouse CI
- **指标**: FCP、LCP、CLS、TBT
- **标准**: 适老化性能要求

## 测试命令

```bash
# 运行所有单元测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行CI测试（无交互模式）
npm run test:ci

# 生成测试报告
node scripts/test-report.js
```

## 测试文件结构

```
src/
├── __tests__/              # 通用测试
│   ├── utils.test.ts       # 工具函数测试
│   └── App.test.tsx        # 应用主组件测试
├── components/
│   └── **/__tests__/       # 组件测试
├── services/
│   └── **/__tests__/       # 服务测试
├── store/
│   └── **/__tests__/       # 状态管理测试
├── setupTests.ts           # 测试环境配置
└── __mocks__/              # Mock文件
    ├── server.ts           # MSW服务器
    ├── handlers.ts         # API处理器
    └── fileMock.js         # 文件模拟

tests/
├── e2e/                    # E2E测试
│   └── basic.spec.ts       # 基础功能测试
└── performance/            # 性能测试
    └── lighthouse.config.js # Lighthouse配置
```

## 测试配置文件

### Jest配置 (jest.config.js)
- 测试环境: jsdom
- 覆盖率阈值: 80%
- 模块映射: 支持路径别名和静态资源
- 转换配置: TypeScript和JSX支持

### Playwright配置 (playwright.config.ts)
- 多浏览器支持: Chrome、Firefox、Safari
- 移动端测试: 模拟移动设备
- 自动启动开发服务器
- 失败时截图和录制

## 适老化测试重点

### 1. 可访问性测试
- 键盘导航支持
- 屏幕阅读器兼容性
- 颜色对比度检查
- 焦点管理

### 2. 界面适老化
- 大字体显示
- 高对比度主题
- 大按钮尺寸
- 简化操作流程

### 3. 性能要求
- 首屏加载时间 < 2秒
- 交互响应时间 < 300ms
- 内存使用优化
- 网络请求优化

## 测试最佳实践

### 1. 单元测试
```typescript
// 好的测试示例
describe('ProductCard组件', () => {
  test('应该正确显示商品信息', () => {
    const mockProduct = { /* mock data */ };
    render(<ProductCard product={mockProduct} />);
    
    expect(screen.getByText(mockProduct.name)).toBeInTheDocument();
    expect(screen.getByText(`¥${mockProduct.price}`)).toBeInTheDocument();
  });
});
```

### 2. 集成测试
```typescript
// API集成测试
test('应该能够获取商品列表', async () => {
  const products = await productService.getProducts();
  expect(products).toHaveProperty('products');
  expect(Array.isArray(products.products)).toBe(true);
});
```

### 3. E2E测试
```typescript
// 用户流程测试
test('用户应该能够完成购买流程', async ({ page }) => {
  await page.goto('/');
  await page.click('[data-testid="product-card"]');
  await page.click('[data-testid="add-to-cart"]');
  await page.click('[data-testid="checkout"]');
  
  await expect(page.locator('[data-testid="order-success"]')).toBeVisible();
});
```

## 测试数据管理

### Mock数据
- 使用MSW模拟API响应
- 创建可重用的测试数据工厂
- 保持测试数据的一致性

### 测试环境
- 隔离的测试数据库
- 可重置的状态管理
- 独立的测试环境配置

## 持续集成

### GitHub Actions配置
```yaml
- name: Run Tests
  run: |
    npm run test:ci
    npm run test:coverage
    
- name: Upload Coverage
  uses: codecov/codecov-action@v1
```

### 质量门禁
- 测试覆盖率 ≥ 80%
- 所有测试必须通过
- 性能指标达标
- 无严重可访问性问题

## 测试报告

测试完成后会生成以下报告：
- **覆盖率报告**: `coverage/lcov-report/index.html`
- **测试报告**: `test-reports/test-report-*.html`
- **性能报告**: Lighthouse CI报告

## 故障排除

### 常见问题
1. **测试超时**: 增加测试超时时间或优化异步操作
2. **Mock失效**: 检查MSW处理器配置
3. **组件渲染错误**: 确保所有依赖都已正确模拟
4. **E2E测试不稳定**: 添加适当的等待条件

### 调试技巧
- 使用`screen.debug()`查看渲染结果
- 使用`--verbose`标志获取详细输出
- 在E2E测试中使用`page.pause()`进行调试

## 贡献指南

### 添加新测试
1. 为新功能编写对应的单元测试
2. 更新集成测试覆盖新的API
3. 添加E2E测试验证用户流程
4. 确保测试覆盖率不降低

### 测试命名规范
- 描述性的测试名称
- 使用"应该"开头的中文描述
- 分组相关的测试用例

通过完善的测试体系，我们确保助老订餐收银系统的质量和可靠性，为老年用户提供稳定、易用的服务体验。 