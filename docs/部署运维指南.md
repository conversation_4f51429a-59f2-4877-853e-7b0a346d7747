# 助老订餐收银系统 - 部署运维指南

## 📋 目录
1. [系统要求](#系统要求)
2. [部署方式](#部署方式)
3. [安装部署](#安装部署)
4. [系统配置](#系统配置)
5. [数据库配置](#数据库配置)
6. [服务管理](#服务管理)
7. [监控告警](#监控告警)
8. [备份恢复](#备份恢复)
9. [性能优化](#性能优化)
10. [故障排除](#故障排除)
11. [更新升级](#更新升级)
12. [安全配置](#安全配置)

---

## 💻 系统要求

### 硬件要求

#### 最低配置
- **CPU**: Intel Core i3 或 AMD 同等性能
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 100Mbps 带宽
- **摄像头**: USB 2.0 摄像头（AI识别功能）

#### 推荐配置
- **CPU**: Intel Core i5 或 AMD Ryzen 5
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 1Gbps 带宽
- **摄像头**: 1080p USB 3.0 摄像头
- **打印机**: 热敏打印机（小票打印）

#### 生产环境推荐
- **CPU**: Intel Core i7 或 AMD Ryzen 7
- **内存**: 16GB RAM
- **存储**: 100GB SSD + 500GB HDD（数据存储）
- **网络**: 千兆网络
- **UPS**: 不间断电源

### 软件要求

#### Windows 平台
- **操作系统**: Windows 10/11 (64位)
- **运行时**: .NET Framework 4.8+
- **浏览器**: Chrome 90+, Edge 90+
- **数据库**: SQLite 3.35+ (内置) 或 PostgreSQL 12+

#### Android 平台
- **操作系统**: Android 8.0+ (API Level 26)
- **RAM**: 最少 3GB
- **存储**: 最少 2GB 可用空间
- **摄像头**: 后置摄像头（AI识别）

#### macOS 平台（可选）
- **操作系统**: macOS 10.15+
- **处理器**: Intel 或 Apple Silicon
- **内存**: 4GB RAM+
- **存储**: 10GB 可用空间

---

## 🚀 部署方式

### 1. 单机部署
适用于小型商户，所有服务运行在一台设备上。

```
┌─────────────────────────────────┐
│         Windows PC/平板          │
├─────────────────────────────────┤
│  Electron App (前端)            │
│  Local API Server (后端)        │
│  SQLite Database (数据库)       │
│  File Storage (文件存储)        │
└─────────────────────────────────┘
```

### 2. 客户端-服务器架构
适用于多台收银机的场景。

```
┌─────────────────┐    ┌─────────────────┐
│   收银机 #1      │    │   收银机 #2      │
│  Electron App   │    │  Electron App   │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     │
         ┌───────────▼───────────┐
         │      中央服务器        │
         │  API Server          │
         │  PostgreSQL DB       │
         │  File Storage        │
         └─────────────────────┘
```

### 3. 混合云部署
结合本地部署和云服务的优势。

```
┌─────────────────┐    ┌─────────────────┐
│   本地收银机     │    │    云端服务      │
│  Electron App   │◄──►│  数据同步服务    │
│  Local Cache    │    │  报表分析       │
│  Offline Mode   │    │  远程监控       │
└─────────────────┘    └─────────────────┘
```

---

## 📦 安装部署

### Windows 桌面端部署

#### 方式一：使用安装包（推荐）
1. **下载安装包**
   ```bash
   # 下载最新版本
   wget https://releases.elderly-cashier.com/v1.0.0/setup.exe
   ```

2. **运行安装程序**
   - 右键点击 setup.exe，选择"以管理员身份运行"
   - 按照安装向导完成安装
   - 选择安装路径（默认：C:\Program Files\助老订餐收银系统）

3. **验证安装**
   ```bash
   # 检查安装目录
   dir "C:\Program Files\助老订餐收银系统"
   
   # 启动应用测试
   "C:\Program Files\助老订餐收银系统\助老订餐收银系统.exe"
   ```

#### 方式二：源码部署
1. **环境准备**
   ```bash
   # 安装 Node.js 18+
   node --version
   npm --version
   
   # 安装 Git
   git --version
   ```

2. **克隆代码**
   ```bash
   git clone https://github.com/elderly-cashier/cashier.git
   cd cashier
   ```

3. **安装依赖**
   ```bash
   npm install --legacy-peer-deps
   ```

4. **构建应用**
   ```bash
   # 开发环境运行
   npm run electron-dev
   
   # 生产环境构建
   npm run dist:win
   ```

### Android 移动端部署

#### 方式一：APK 安装
1. **下载 APK**
   ```bash
   wget https://releases.elderly-cashier.com/v1.0.0/app-release.apk
   ```

2. **安装应用**
   - 在设备上启用"未知来源安装"
   - 传输 APK 到设备
   - 点击安装

#### 方式二：源码构建
1. **环境准备**
   ```bash
   # 安装 Android Studio
   # 配置 Android SDK
   
   # 安装 Capacitor CLI
   npm install -g @capacitor/cli
   ```

2. **构建 Android 应用**
   ```bash
   # 构建 Web 应用
   npm run build
   
   # 同步到 Android
   npx cap sync android
   
   # 打开 Android Studio
   npx cap open android
   ```

### 服务器端部署

#### 使用 Docker（推荐）
1. **创建 Docker 配置**
   ```dockerfile
   # Dockerfile
   FROM node:18-alpine
   
   WORKDIR /app
   COPY package*.json ./
   RUN npm install --production
   
   COPY . .
   EXPOSE 3000
   
   CMD ["npm", "start"]
   ```

2. **构建和运行**
   ```bash
   # 构建镜像
   docker build -t elderly-cashier:v1.0.0 .
   
   # 运行容器
   docker run -d \
     --name elderly-cashier \
     -p 3000:3000 \
     -v /data:/app/data \
     elderly-cashier:v1.0.0
   ```

#### 使用 Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
    depends_on:
      - postgres
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: elderly_cashier
      POSTGRES_USER: cashier
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app

volumes:
  postgres_data:
```

---

## ⚙️ 系统配置

### 应用配置文件

#### config/app.json
```json
{
  "app": {
    "name": "助老订餐收银系统",
    "version": "1.0.0",
    "environment": "production",
    "debug": false
  },
  "server": {
    "port": 3000,
    "host": "0.0.0.0",
    "cors": {
      "enabled": true,
      "origins": ["http://localhost:3000"]
    }
  },
  "database": {
    "type": "sqlite",
    "path": "./data/cashier.db",
    "backup": {
      "enabled": true,
      "interval": "24h",
      "retention": "30d"
    }
  },
  "ai": {
    "enabled": true,
    "model": "mobilenet_v2",
    "confidence": 0.8,
    "timeout": 5000
  },
  "features": {
    "elderlyMode": true,
    "voicePrompts": true,
    "largeButtons": true,
    "highContrast": true
  }
}
```

#### config/database.json
```json
{
  "development": {
    "dialect": "sqlite",
    "storage": "./data/cashier-dev.db"
  },
  "production": {
    "dialect": "postgres",
    "host": "localhost",
    "port": 5432,
    "database": "elderly_cashier",
    "username": "cashier",
    "password": "secure_password",
    "pool": {
      "max": 10,
      "min": 2,
      "acquire": 30000,
      "idle": 10000
    }
  }
}
```

### 环境变量配置

#### .env.production
```bash
# 应用配置
NODE_ENV=production
APP_PORT=3000
APP_HOST=0.0.0.0

# 数据库配置
DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5432
DB_NAME=elderly_cashier
DB_USER=cashier
DB_PASS=secure_password

# 安全配置
JWT_SECRET=your-jwt-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here

# AI配置
AI_MODEL_PATH=./models/food-recognition.tflite
AI_CONFIDENCE_THRESHOLD=0.8

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=10

# 外部服务
PAYMENT_API_URL=https://api.payment.com
PAYMENT_API_KEY=your-payment-api-key
```

### Nginx 配置

#### nginx.conf
```nginx
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 负载均衡
    upstream elderly_cashier {
        server app:3000;
    }

    # HTTP 重定向到 HTTPS
    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    # HTTPS 服务器
    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        # 静态文件
        location /static/ {
            alias /app/public/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # API 代理
        location /api/ {
            proxy_pass http://elderly_cashier;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 主应用
        location / {
            proxy_pass http://elderly_cashier;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

---

## 🗄️ 数据库配置

### SQLite 配置（单机部署）

#### 初始化脚本
```sql
-- init.sql
PRAGMA foreign_keys = ON;
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = memory;

-- 创建表结构
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'cashier',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(50),
    image_url VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认数据
INSERT OR IGNORE INTO users (username, password_hash, role) VALUES
('admin', '$2b$10$hash_here', 'admin'),
('cashier', '$2b$10$hash_here', 'cashier');
```

### PostgreSQL 配置（生产环境）

#### 数据库创建
```sql
-- 创建数据库
CREATE DATABASE elderly_cashier;
CREATE USER cashier WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE elderly_cashier TO cashier;

-- 连接到数据库
\c elderly_cashier;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 配置
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET work_mem = '4MB';
SELECT pg_reload_conf();
```

#### 备份脚本
```bash
#!/bin/bash
# backup.sh

DB_NAME="elderly_cashier"
DB_USER="cashier"
BACKUP_DIR="/backup/postgres"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
pg_dump -h localhost -U $DB_USER -d $DB_NAME \
    --verbose --clean --no-owner --no-privileges \
    --format=custom \
    --file="$BACKUP_DIR/cashier_$DATE.backup"

# 压缩旧备份
find $BACKUP_DIR -name "*.backup" -mtime +7 -exec gzip {} \;

# 清理超过30天的备份
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "备份完成: $BACKUP_DIR/cashier_$DATE.backup"
```

---

## 🔧 服务管理

### Systemd 服务配置

#### elderly-cashier.service
```ini
[Unit]
Description=助老订餐收银系统
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=cashier
Group=cashier
WorkingDirectory=/opt/elderly-cashier
ExecStart=/usr/bin/node server.js
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=elderly-cashier
Environment=NODE_ENV=production
EnvironmentFile=/opt/elderly-cashier/.env

[Install]
WantedBy=multi-user.target
```

#### 服务管理命令
```bash
# 启用服务
sudo systemctl enable elderly-cashier.service

# 启动服务
sudo systemctl start elderly-cashier.service

# 停止服务
sudo systemctl stop elderly-cashier.service

# 重启服务
sudo systemctl restart elderly-cashier.service

# 查看状态
sudo systemctl status elderly-cashier.service

# 查看日志
sudo journalctl -u elderly-cashier.service -f
```

### PM2 进程管理

#### ecosystem.config.js
```javascript
module.exports = {
  apps: [{
    name: 'elderly-cashier',
    script: 'server.js',
    cwd: '/opt/elderly-cashier',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

#### PM2 命令
```bash
# 启动应用
pm2 start ecosystem.config.js

# 重启应用
pm2 restart elderly-cashier

# 停止应用
pm2 stop elderly-cashier

# 查看状态
pm2 status

# 查看日志
pm2 logs elderly-cashier

# 监控
pm2 monit
```

---

## 📊 监控告警

### 监控指标

#### 系统监控
```bash
# CPU 使用率
top -p $(pgrep -f elderly-cashier)

# 内存使用
ps -o pid,ppid,cmd,comm,pcpu,pmem -p $(pgrep -f elderly-cashier)

# 磁盘空间
df -h /opt/elderly-cashier

# 网络连接
netstat -tulpn | grep :3000
```

#### 应用监控
```javascript
// 健康检查端点
app.get('/health', (req, res) => {
  const health = {
    timestamp: new Date().toISOString(),
    status: 'healthy',
    version: process.env.npm_package_version,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    database: 'connected', // 检查数据库连接
    ai_service: 'ready'    // 检查AI服务状态
  };
  
  res.json(health);
});
```

### 日志配置

#### logrotate 配置
```bash
# /etc/logrotate.d/elderly-cashier
/opt/elderly-cashier/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 cashier cashier
    postrotate
        systemctl reload elderly-cashier.service > /dev/null 2>&1 || true
    endscript
}
```

### 告警配置

#### 告警脚本
```bash
#!/bin/bash
# alert.sh

SERVICE_NAME="elderly-cashier"
EMAIL="<EMAIL>"
WEBHOOK_URL="https://hooks.slack.com/your-webhook"

# 检查服务状态
if ! systemctl is-active --quiet $SERVICE_NAME; then
    MESSAGE="🚨 服务异常: $SERVICE_NAME 已停止运行"
    
    # 发送邮件
    echo "$MESSAGE" | mail -s "服务告警" $EMAIL
    
    # 发送 Slack 通知
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"$MESSAGE\"}" \
        $WEBHOOK_URL
    
    # 自动重启尝试
    systemctl restart $SERVICE_NAME
fi

# 检查磁盘空间
DISK_USAGE=$(df /opt/elderly-cashier | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    MESSAGE="⚠️ 磁盘空间警告: 使用率 ${DISK_USAGE}%"
    echo "$MESSAGE" | mail -s "磁盘空间告警" $EMAIL
fi
```

---

## 💾 备份恢复

### 自动备份脚本

#### backup.sh
```bash
#!/bin/bash

# 配置
BACKUP_DIR="/backup/elderly-cashier"
APP_DIR="/opt/elderly-cashier"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# 创建备份目录
mkdir -p $BACKUP_DIR/{database,config,logs,uploads}

echo "开始备份: $DATE"

# 1. 数据库备份
if [ -f "$APP_DIR/data/cashier.db" ]; then
    echo "备份数据库..."
    cp "$APP_DIR/data/cashier.db" "$BACKUP_DIR/database/cashier_$DATE.db"
    gzip "$BACKUP_DIR/database/cashier_$DATE.db"
fi

# 2. 配置文件备份
echo "备份配置文件..."
tar -czf "$BACKUP_DIR/config/config_$DATE.tar.gz" \
    -C "$APP_DIR" \
    config/ .env* ecosystem.config.js

# 3. 上传文件备份
if [ -d "$APP_DIR/uploads" ]; then
    echo "备份上传文件..."
    tar -czf "$BACKUP_DIR/uploads/uploads_$DATE.tar.gz" \
        -C "$APP_DIR" uploads/
fi

# 4. 日志备份
echo "备份日志文件..."
tar -czf "$BACKUP_DIR/logs/logs_$DATE.tar.gz" \
    -C "$APP_DIR" logs/

# 5. 清理旧备份
echo "清理旧备份..."
find $BACKUP_DIR -name "*.gz" -mtime +$RETENTION_DAYS -delete

# 6. 创建恢复脚本
cat > "$BACKUP_DIR/restore_$DATE.sh" << EOF
#!/bin/bash
# 恢复脚本 - $DATE

BACKUP_DATE="$DATE"
RESTORE_DIR="/opt/elderly-cashier"

echo "恢复数据库..."
gunzip -c database/cashier_\$BACKUP_DATE.db.gz > \$RESTORE_DIR/data/cashier.db

echo "恢复配置文件..."
tar -xzf config/config_\$BACKUP_DATE.tar.gz -C \$RESTORE_DIR

echo "恢复上传文件..."
tar -xzf uploads/uploads_\$BACKUP_DATE.tar.gz -C \$RESTORE_DIR

echo "重启服务..."
systemctl restart elderly-cashier

echo "恢复完成!"
EOF

chmod +x "$BACKUP_DIR/restore_$DATE.sh"

echo "备份完成: $BACKUP_DIR"
echo "恢复脚本: $BACKUP_DIR/restore_$DATE.sh"
```

### 定时备份

#### crontab 配置
```bash
# 编辑 crontab
crontab -e

# 添加备份任务
# 每天凌晨2点执行备份
0 2 * * * /opt/elderly-cashier/scripts/backup.sh >> /var/log/backup.log 2>&1

# 每周日执行完整备份
0 1 * * 0 /opt/elderly-cashier/scripts/full-backup.sh >> /var/log/backup.log 2>&1
```

---

## ⚡ 性能优化

### 数据库优化

#### SQLite 优化
```sql
-- 优化设置
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = memory;
PRAGMA mmap_size = 268435456; -- 256MB

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_orders_date ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);

-- 定期维护
PRAGMA optimize;
VACUUM;
ANALYZE;
```

#### PostgreSQL 优化
```sql
-- 调整配置参数
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET max_connections = 100;
ALTER SYSTEM SET random_page_cost = 1.1;

-- 重载配置
SELECT pg_reload_conf();

-- 创建部分索引
CREATE INDEX CONCURRENTLY idx_orders_status_date 
ON orders(status, created_at) 
WHERE status = 'completed';
```

### 应用优化

#### Node.js 优化
```javascript
// server.js 优化配置
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
  // 主进程
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }
  
  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died`);
    cluster.fork();
  });
} else {
  // 工作进程
  const app = require('./app');
  
  // 启用压缩
  app.use(require('compression')());
  
  // 缓存配置
  app.use(require('memory-cache').middleware({
    timeout: 600000, // 10分钟
    headers: ['content-type']
  }));
  
  app.listen(process.env.PORT || 3000);
}
```

### 前端优化

#### React 性能优化
```javascript
// 懒加载组件
const ProductGrid = React.lazy(() => import('./components/ProductGrid'));
const OrderHistory = React.lazy(() => import('./components/OrderHistory'));

// 使用 React.memo
const ProductCard = React.memo(({ product, onAdd }) => {
  return (
    <div className="product-card">
      {/* 组件内容 */}
    </div>
  );
});

// 优化列表渲染
const VirtualizedList = React.memo(({ items }) => {
  return (
    <FixedSizeList
      height={600}
      itemCount={items.length}
      itemSize={120}
      itemData={items}
    >
      {ProductRow}
    </FixedSizeList>
  );
});
```

---

## 🔍 故障排除

### 常见问题诊断

#### 应用启动失败
```bash
# 检查端口占用
netstat -tulpn | grep :3000
lsof -i :3000

# 检查权限
ls -la /opt/elderly-cashier
sudo chown -R cashier:cashier /opt/elderly-cashier

# 检查依赖
npm list --depth=0
npm audit fix

# 查看详细错误
NODE_ENV=development npm start
```

#### 数据库连接问题
```bash
# SQLite 检查
sqlite3 /opt/elderly-cashier/data/cashier.db ".tables"
sqlite3 /opt/elderly-cashier/data/cashier.db "PRAGMA integrity_check;"

# PostgreSQL 检查
psql -h localhost -U cashier -d elderly_cashier -c "\dt"
pg_isready -h localhost -p 5432
```

#### AI 识别不工作
```bash
# 检查摄像头
lsusb | grep -i camera
v4l2-ctl --list-devices

# 检查模型文件
ls -la /opt/elderly-cashier/models/
file /opt/elderly-cashier/models/food-recognition.tflite

# 测试 TensorFlow.js
node -e "console.log(require('@tensorflow/tfjs').version)"
```

### 性能问题诊断

#### 内存泄漏检查
```bash
# 监控内存使用
while true; do
  ps -o pid,ppid,cmd,comm,pcpu,pmem -p $(pgrep -f elderly-cashier)
  sleep 10
done

# Node.js 内存分析
node --inspect server.js
```

#### 慢查询分析
```sql
-- PostgreSQL 慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- SQLite 查询计划
EXPLAIN QUERY PLAN SELECT * FROM products WHERE category = 'main';
```

### 网络问题诊断
```bash
# 检查网络连接
ping google.com
curl -I https://api.payment.com

# 检查DNS解析
nslookup api.payment.com
dig api.payment.com

# 检查防火墙
iptables -L
ufw status
```

---

## 🔄 更新升级

### 版本升级流程

#### 1. 准备工作
```bash
# 创建完整备份
/opt/elderly-cashier/scripts/backup.sh

# 检查当前版本
cat /opt/elderly-cashier/package.json | grep version

# 下载新版本
wget https://releases.elderly-cashier.com/v1.1.0/update.tar.gz
```

#### 2. 升级步骤
```bash
#!/bin/bash
# upgrade.sh

VERSION="1.1.0"
APP_DIR="/opt/elderly-cashier"
BACKUP_DIR="/backup/elderly-cashier/upgrade_$(date +%Y%m%d_%H%M%S)"

echo "开始升级到版本 $VERSION"

# 1. 停止服务
echo "停止服务..."
systemctl stop elderly-cashier

# 2. 备份当前版本
echo "备份当前版本..."
mkdir -p $BACKUP_DIR
cp -r $APP_DIR $BACKUP_DIR/

# 3. 解压新版本
echo "解压新版本..."
tar -xzf update.tar.gz -C /tmp/
rsync -av --exclude='data/' --exclude='logs/' --exclude='.env*' \
    /tmp/elderly-cashier-$VERSION/ $APP_DIR/

# 4. 更新依赖
echo "更新依赖..."
cd $APP_DIR
npm install --production

# 5. 数据库迁移
echo "执行数据库迁移..."
npm run migrate

# 6. 启动服务
echo "启动服务..."
systemctl start elderly-cashier

# 7. 验证升级
echo "验证升级..."
sleep 10
if systemctl is-active --quiet elderly-cashier; then
    echo "✅ 升级成功!"
    curl -f http://localhost:3000/health || echo "⚠️ 健康检查失败"
else
    echo "❌ 升级失败，回滚中..."
    systemctl stop elderly-cashier
    rsync -av $BACKUP_DIR/elderly-cashier/ $APP_DIR/
    systemctl start elderly-cashier
fi
```

#### 3. 回滚方案
```bash
#!/bin/bash
# rollback.sh

BACKUP_DATE="$1"
if [ -z "$BACKUP_DATE" ]; then
    echo "请指定备份日期，例如: ./rollback.sh 20241215_143022"
    exit 1
fi

BACKUP_DIR="/backup/elderly-cashier/upgrade_$BACKUP_DATE"
APP_DIR="/opt/elderly-cashier"

if [ ! -d "$BACKUP_DIR" ]; then
    echo "备份目录不存在: $BACKUP_DIR"
    exit 1
fi

echo "回滚到备份: $BACKUP_DATE"

# 停止服务
systemctl stop elderly-cashier

# 恢复备份
rsync -av --delete $BACKUP_DIR/elderly-cashier/ $APP_DIR/

# 启动服务
systemctl start elderly-cashier

echo "回滚完成"
```

---

## 🔐 安全配置

### SSL/TLS 配置

#### 证书生成
```bash
# 生成自签名证书（测试用）
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# 生成 CSR（生产用）
openssl req -new -newkey rsa:4096 -keyout private.key -out request.csr -nodes
```

#### 证书安装
```bash
# 复制证书到 nginx 目录
sudo cp cert.pem /etc/nginx/ssl/
sudo cp key.pem /etc/nginx/ssl/
sudo chmod 600 /etc/nginx/ssl/key.pem
```

### 防火墙配置

#### iptables 规则
```bash
# 清除现有规则
iptables -F

# 默认策略
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# 允许本地环回
iptables -A INPUT -i lo -j ACCEPT

# 允许已建立的连接
iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT

# 允许 SSH (根据需要修改端口)
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# 允许 HTTP/HTTPS
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# 允许应用端口 (仅内网)
iptables -A INPUT -p tcp -s ***********/16 --dport 3000 -j ACCEPT

# 保存规则
iptables-save > /etc/iptables/rules.v4
```

### 应用安全

#### 安全中间件
```javascript
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// 安全头
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 100个请求
  message: '请求过于频繁，请稍后再试'
});

app.use('/api/', limiter);
```

### 数据保护

#### 数据加密
```javascript
const crypto = require('crypto');

// 敏感数据加密
function encrypt(text) {
  const algorithm = 'aes-256-gcm';
  const key = Buffer.from(process.env.ENCRYPTION_KEY, 'hex');
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipher(algorithm, key);
  cipher.setIV(iv);
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  const authTag = cipher.getAuthTag();
  
  return {
    iv: iv.toString('hex'),
    encryptedData: encrypted,
    authTag: authTag.toString('hex')
  };
}
```

#### 数据库安全
```sql
-- 创建只读用户
CREATE USER readonly_user WITH PASSWORD 'read_only_password';
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;

-- 敏感字段加密
ALTER TABLE users ADD COLUMN phone_encrypted TEXT;
UPDATE users SET phone_encrypted = encrypt(phone, 'encryption_key');
ALTER TABLE users DROP COLUMN phone;
```

---

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **紧急支持电话**: +86-400-XXX-XXXX
- **文档中心**: https://docs.elderly-cashier.com
- **GitHub Issues**: https://github.com/elderly-cashier/cashier/issues

### 支持级别
- **L1 - 基础支持**: 安装、配置、使用问题
- **L2 - 技术支持**: 性能优化、集成问题
- **L3 - 专家支持**: 架构设计、定制开发

---

*部署运维指南版本：v1.0 | 更新时间：2024年12月*

*如需技术支持，请联系我们的运维团队！* 