# API接口文档

## 1. 接口总览

助老订餐收银系统API采用RESTful设计风格，支持JSON格式数据交换。

### 基础信息
- **Base URL**: `http://localhost:3000/api/v1`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: <PERSON><PERSON> (JWT)

### 通用响应格式
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 2. 认证接口

### 2.1 用户登录
```http
POST /auth/login
```

**请求参数：**
```json
{
  "username": "cashier01",
  "password": "password123",
  "storeId": "store001"
}
```

**响应数据：**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "refresh_token_here",
    "user": {
      "id": "user001",
      "username": "cashier01",
      "name": "张收银",
      "role": "cashier",
      "permissions": ["order:create", "payment:process"]
    },
    "expiresIn": 3600
  }
}
```

### 2.2 刷新Token
```http
POST /auth/refresh
```

## 3. 商品管理接口

### 3.1 获取商品列表
```http
GET /products?category={categoryId}&page={page}&size={size}&search={keyword}
```

**响应数据：**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "prod001",
        "name": "红烧肉",
        "price": 28.00,
        "image": "https://example.com/hongshaorou.jpg",
        "category": {
          "id": "cat001",
          "name": "热菜"
        },
        "description": "香甜软糯，老少皆宜",
        "isAvailable": true,
        "nutritionInfo": {
          "calories": 350,
          "protein": 25,
          "allergens": ["大豆"]
        }
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 150,
      "totalPages": 8
    }
  }
}
```

### 3.2 获取商品分类
```http
GET /products/categories
```

## 4. 购物车接口

### 4.1 添加商品到购物车
```http
POST /cart/items
```

**请求参数：**
```json
{
  "productId": "prod001",
  "quantity": 2,
  "specifications": {
    "size": "大份",
    "spiciness": "微辣"
  },
  "note": "少盐少油"
}
```

### 4.2 获取购物车内容
```http
GET /cart
```

**响应数据：**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "cart001",
        "product": {
          "id": "prod001",
          "name": "红烧肉",
          "price": 28.00,
          "image": "https://example.com/hongshaorou.jpg"
        },
        "quantity": 2,
        "specifications": {
          "size": "大份",
          "spiciness": "微辣"
        },
        "note": "少盐少油",
        "subtotal": 56.00
      }
    ],
    "summary": {
      "totalItems": 3,
      "totalAmount": 85.00,
      "discountAmount": 8.50,
      "finalAmount": 76.50
    }
  }
}
```

### 4.3 更新购物车商品
```http
PUT /cart/items/{itemId}
```

### 4.4 删除购物车商品
```http
DELETE /cart/items/{itemId}
```

### 4.5 清空购物车
```http
DELETE /cart
```

## 5. 会员管理接口

### 5.1 会员查询
```http
GET /members/search?phone={phone}&cardNo={cardNo}&name={name}
```

**响应数据：**
```json
{
  "success": true,
  "data": {
    "member": {
      "id": "member001",
      "cardNo": "888888888888",
      "name": "李大爷",
      "phone": "13800138000",
      "level": "gold",
      "balance": 256.80,
      "points": 1580,
      "discountRate": 0.88,
      "birthday": "1950-01-01",
      "preferences": ["清淡", "软烂"],
      "medicalInfo": {
        "allergies": ["海鲜"],
        "dietRestrictions": ["低盐", "低糖"]
      }
    }
  }
}
```

### 5.2 会员注册
```http
POST /members
```

**请求参数：**
```json
{
  "name": "王奶奶",
  "phone": "13900139000",
  "birthday": "1955-06-15",
  "preferences": ["清淡"],
  "medicalInfo": {
    "allergies": [],
    "dietRestrictions": ["低盐"]
  }
}
```

### 5.3 会员信息更新
```http
PUT /members/{memberId}
```

### 5.4 会员充值
```http
POST /members/{memberId}/recharge
```

**请求参数：**
```json
{
  "amount": 200.00,
  "paymentMethod": "cash",
  "operator": "cashier01"
}
```

## 6. 支付结算接口

### 6.1 创建支付订单
```http
POST /payments/create
```

**请求参数：**
```json
{
  "cartId": "cart001",
  "memberId": "member001",
  "paymentMethods": [
    {
      "type": "member_balance",
      "amount": 50.00
    },
    {
      "type": "cash",
      "amount": 26.50
    }
  ],
  "discounts": [
    {
      "type": "member_discount",
      "rate": 0.88,
      "amount": 8.50
    }
  ]
}
```

### 6.2 支付确认
```http
POST /payments/{paymentId}/confirm
```

### 6.3 支付状态查询
```http
GET /payments/{paymentId}/status
```

## 7. AI识别辅助接口

### 7.1 获取识别模型信息
```http
GET /ai/models
```

**响应数据：**
```json
{
  "success": true,
  "data": {
    "models": [
      {
        "id": "dish_recognition_v1",
        "name": "菜品识别模型",
        "version": "1.2.0",
        "size": "25.6MB",
        "accuracy": 0.91,
        "supportedCategories": ["热菜", "凉菜", "汤类", "主食"]
      }
    ]
  }
}
```

### 7.2 识别结果验证
```http
POST /ai/verify-result
```

**请求参数：**
```json
{
  "recognizedProducts": [
    {
      "productId": "prod001",
      "confidence": 0.92,
      "boundingBox": {
        "x": 100,
        "y": 150,
        "width": 200,
        "height": 180
      }
    }
  ],
  "operatorConfirm": true
}
```

## 8. 折扣管理接口

### 8.1 获取适用折扣
```http
GET /discounts/applicable?memberId={memberId}&totalAmount={amount}
```

**响应数据：**
```json
{
  "success": true,
  "data": {
    "discounts": [
      {
        "id": "discount001",
        "name": "会员折扣",
        "type": "member_discount",
        "rate": 0.88,
        "description": "金卡会员8.8折"
      },
      {
        "id": "discount002",
        "name": "满减优惠",
        "type": "amount_discount",
        "condition": "满100减10",
        "discountAmount": 10.00
      }
    ]
  }
}
```

### 8.2 计算折扣金额
```http
POST /discounts/calculate
```

## 9. 订单管理接口

### 9.1 创建订单
```http
POST /orders
```

**请求参数：**
```json
{
  "memberId": "member001",
  "items": [
    {
      "productId": "prod001",
      "quantity": 2,
      "price": 28.00,
      "specifications": {
        "size": "大份"
      }
    }
  ],
  "totalAmount": 76.50,
  "discountAmount": 8.50,
  "paymentMethod": "mixed",
  "diningMode": "eat_in",
  "tableNumber": "A08"
}
```

### 9.2 订单查询
```http
GET /orders?date={date}&status={status}&memberId={memberId}&page={page}
```

**响应数据：**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "order001",
        "orderNo": "20240101001",
        "member": {
          "name": "李大爷",
          "phone": "13800138000"
        },
        "items": [
          {
            "product": {
              "name": "红烧肉",
              "price": 28.00
            },
            "quantity": 2,
            "subtotal": 56.00
          }
        ],
        "summary": {
          "totalAmount": 85.00,
          "discountAmount": 8.50,
          "finalAmount": 76.50
        },
        "status": "completed",
        "paymentStatus": "paid",
        "createdAt": "2024-01-01T12:30:00.000Z"
      }
    ]
  }
}
```

### 9.3 订单详情
```http
GET /orders/{orderId}
```

### 9.4 订单状态更新
```http
PUT /orders/{orderId}/status
```

## 10. 退款管理接口

### 10.1 申请退款
```http
POST /refunds
```

**请求参数：**
```json
{
  "orderId": "order001",
  "items": [
    {
      "orderItemId": "item001",
      "quantity": 1,
      "reason": "菜品有误"
    }
  ],
  "refundAmount": 28.00,
  "operator": "cashier01",
  "note": "顾客要求退换"
}
```

### 10.2 退款审批
```http
PUT /refunds/{refundId}/approve
```

### 10.3 退款查询
```http
GET /refunds?date={date}&status={status}&page={page}
```

## 11. 财务对账接口

### 11.1 日结对账
```http
POST /finance/daily-settlement
```

**请求参数：**
```json
{
  "date": "2024-01-01",
  "cashierId": "cashier01",
  "cashAmount": 1250.00,
  "note": "正常营业"
}
```

**响应数据：**
```json
{
  "success": true,
  "data": {
    "settlement": {
      "id": "settle001",
      "date": "2024-01-01",
      "summary": {
        "orderCount": 45,
        "totalRevenue": 2850.00,
        "cashAmount": 1250.00,
        "cardAmount": 800.00,
        "memberBalanceAmount": 800.00,
        "discountAmount": 285.00,
        "refundAmount": 56.00
      },
      "paymentBreakdown": [
        {
          "method": "cash",
          "count": 20,
          "amount": 1250.00
        },
        {
          "method": "member_balance",
          "count": 15,
          "amount": 800.00
        }
      ]
    }
  }
}
```

### 11.2 开具发票
```http
POST /finance/invoices
```

### 11.3 财务报表
```http
GET /finance/reports?type={type}&startDate={start}&endDate={end}
```

## 12. 广告内容接口

### 12.1 获取广告列表
```http
GET /advertisements?position={position}&status=active
```

**响应数据：**
```json
{
  "success": true,
  "data": {
    "advertisements": [
      {
        "id": "ad001",
        "title": "今日特价菜品",
        "content": "红烧肉特价28元，限量供应！",
        "imageUrl": "https://example.com/ad001.jpg",
        "position": "main_screen",
        "displayDuration": 10,
        "startTime": "2024-01-01T00:00:00.000Z",
        "endTime": "2024-01-31T23:59:59.000Z"
      }
    ]
  }
}
```

### 12.2 广告点击统计
```http
POST /advertisements/{adId}/click
```

## 13. 系统配置接口

### 13.1 获取系统配置
```http
GET /config
```

### 13.2 更新系统配置
```http
PUT /config
```

## 14. 错误码说明

| 错误码 | 说明 | 处理建议 |
|-------|------|---------|
| 200 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 重新登录获取token |
| 403 | 权限不足 | 联系管理员分配权限 |
| 404 | 资源不存在 | 检查请求路径和参数 |
| 500 | 服务器内部错误 | 联系技术支持 |

## 15. 接口调用示例

### JavaScript (Axios)
```javascript
// 登录
const loginResponse = await axios.post('/api/v1/auth/login', {
  username: 'cashier01',
  password: 'password123',
  storeId: 'store001'
});

// 设置token
const token = loginResponse.data.data.token;
axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

// 获取商品列表
const products = await axios.get('/api/v1/products?category=cat001');

// 添加商品到购物车
await axios.post('/api/v1/cart/items', {
  productId: 'prod001',
  quantity: 2
});
```

该API文档涵盖了助老订餐收银系统的所有核心功能接口，为前端开发提供完整的接口规范。 