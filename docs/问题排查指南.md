# 助老订餐收银系统 - 问题排查指南

## 📋 目录
1. [使用说明](#使用说明)
2. [登录问题](#登录问题)
3. [系统性能问题](#系统性能问题)
4. [AI识别问题](#ai识别问题)
5. [支付问题](#支付问题)
6. [订单问题](#订单问题)
7. [网络连接问题](#网络连接问题)
8. [硬件设备问题](#硬件设备问题)
9. [数据问题](#数据问题)
10. [界面显示问题](#界面显示问题)
11. [紧急处理流程](#紧急处理流程)
12. [联系技术支持](#联系技术支持)

---

## 📖 使用说明

### 如何使用本指南

1. **快速定位**：根据问题类型在目录中快速找到对应章节
2. **按步骤操作**：严格按照排查步骤逐一执行
3. **记录信息**：记录每个步骤的结果，便于技术支持协助
4. **安全第一**：遇到不确定的操作，请先联系技术支持

### 紧急情况优先级

- 🔴 **紧急**：系统完全无法使用，影响正常营业
- 🟡 **重要**：部分功能异常，可临时绕过
- 🟢 **一般**：体验问题，不影响核心功能

### 问题分类

- **硬件问题**：设备故障、连接问题
- **软件问题**：程序错误、配置问题
- **网络问题**：连接异常、速度慢
- **数据问题**：数据丢失、同步异常
- **操作问题**：使用方法、流程错误

---

## 🔐 登录问题

### 问题1：无法登录系统 🔴

**症状描述**
- 输入用户名密码后提示错误
- 点击登录按钮无反应
- 登录界面无法打开

**排查步骤**

#### 步骤1：检查基础信息
```bash
# 检查用户名密码是否正确
- 确认大小写是否正确
- 确认是否有多余空格
- 尝试手动输入而非复制粘贴
```

#### 步骤2：检查网络连接
```bash
# Windows系统
ping google.com

# 检查本地服务
ping localhost
telnet localhost 3000
```

#### 步骤3：检查系统状态
```bash
# 检查应用是否运行
tasklist | findstr "助老订餐"

# 重启应用
- 关闭程序
- 等待30秒
- 重新启动
```

#### 步骤4：清除缓存
```bash
# 清除浏览器缓存
Ctrl + Shift + Delete

# 删除本地缓存文件
%APPDATA%\助老订餐收银系统\cache\
```

**解决方案**

✅ **如果密码错误**
1. 联系管理员重置密码
2. 使用备用管理员账号登录
3. 检查是否账号被锁定

✅ **如果系统无响应**
1. 重启应用程序
2. 重启计算机
3. 检查系统资源使用情况

✅ **如果网络问题**
1. 检查网线连接
2. 重启路由器
3. 联系网络管理员

### 问题2：登录后立即退出 🟡

**症状描述**
- 成功登录但马上返回登录界面
- 提示"会话已过期"

**排查步骤**

#### 步骤1：检查系统时间
```bash
# 确认系统时间是否正确
- 右键点击时钟查看时间设置
- 确保时区设置正确
- 与标准时间对比
```

#### 步骤2：检查权限
```bash
# 确认用户权限
- 联系管理员确认账号状态
- 检查是否有使用权限
- 确认账号类型是否正确
```

**解决方案**

✅ **时间不正确**
1. 调整系统时间
2. 设置自动同步时间
3. 重启应用

✅ **权限问题**
1. 联系管理员检查权限
2. 重新分配角色权限
3. 重新创建用户账号

---

## ⚡ 系统性能问题

### 问题3：系统运行缓慢 🟡

**症状描述**
- 界面响应慢
- 加载时间长
- 操作卡顿

**排查步骤**

#### 步骤1：检查系统资源
```bash
# 打开任务管理器 (Ctrl + Shift + Esc)
- 查看CPU使用率
- 查看内存使用情况
- 查看磁盘使用率
```

#### 步骤2：检查网络状态
```bash
# 测试网络速度
- 使用网络速度测试工具
- 检查是否有大量下载或上传
- 确认其他设备网络是否正常
```

#### 步骤3：检查应用状态
```bash
# 查看应用资源占用
- 在任务管理器中找到应用进程
- 查看CPU和内存占用
- 检查是否有错误日志
```

**解决方案**

✅ **内存不足**
1. 关闭不必要的程序
2. 重启电脑释放内存
3. 考虑升级内存

✅ **磁盘空间不足**
1. 清理临时文件
2. 删除不必要的文件
3. 清空回收站

✅ **网络慢**
1. 检查网络连接
2. 重启路由器
3. 联系网络服务商

### 问题4：应用崩溃或无响应 🔴

**症状描述**
- 应用突然关闭
- 界面冻结无法操作
- 出现错误对话框

**排查步骤**

#### 步骤1：收集错误信息
```bash
# 查看错误日志
- 记录错误信息截图
- 查看Windows事件查看器
- 记录崩溃时间和操作
```

#### 步骤2：检查系统稳定性
```bash
# 重启应用测试
- 重新启动应用
- 重复崩溃前的操作
- 观察是否重现问题
```

**解决方案**

✅ **应用错误**
1. 重启应用程序
2. 重启计算机
3. 重新安装应用

✅ **系统问题**
1. 检查Windows更新
2. 运行系统文件检查
3. 联系技术支持

---

## 🤖 AI识别问题

### 问题5：AI无法识别菜品 🟡

**症状描述**
- 摄像头有画面但识别不出菜品
- 识别结果不准确
- 识别速度很慢

**排查步骤**

#### 步骤1：检查摄像头
```bash
# 检查摄像头状态
- 确认摄像头指示灯是否亮起
- 检查USB连接是否牢固
- 在设备管理器中确认摄像头正常
```

#### 步骤2：检查识别环境
```bash
# 光线条件
- 确保光线充足且均匀
- 避免强光直射或阴影
- 调整摄像头角度

# 菜品摆放
- 菜品完整显示在画面中
- 避免多个菜品重叠
- 清理识别区域杂物
```

#### 步骤3：检查AI服务
```bash
# 在系统设置中检查AI功能状态
- 确认AI识别功能已启用
- 检查模型文件是否完整
- 查看识别历史记录
```

**解决方案**

✅ **摄像头问题**
1. 重新插拔USB连接
2. 重启计算机
3. 更新摄像头驱动

✅ **识别环境问题**
1. 改善光线条件
2. 调整摄像头位置
3. 清洁摄像头镜头

✅ **AI服务问题**
1. 重启AI服务
2. 重新下载模型文件
3. 联系技术支持

### 问题6：识别结果不准确 🟢

**症状描述**
- 识别成错误的菜品
- 识别置信度很低
- 经常识别失败

**排查步骤**

#### 步骤1：优化识别条件
```bash
# 改善拍摄环境
- 使用白色或纯色背景
- 确保菜品居中显示
- 保持菜品完整性
```

#### 步骤2：调整识别参数
```bash
# 在设置中调整
- 降低置信度阈值
- 增加识别时间
- 选择合适的识别模式
```

**解决方案**

✅ **环境优化**
1. 改善拍摄环境
2. 使用标准餐具
3. 保持菜品新鲜外观

✅ **参数调整**
1. 调整识别参数
2. 训练个性化模型
3. 使用手动输入作为补充

---

## 💰 支付问题

### 问题7：支付二维码无法显示 🔴

**症状描述**
- 点击微信/支付宝支付无反应
- 二维码显示错误或空白
- 支付页面打不开

**排查步骤**

#### 步骤1：检查网络连接
```bash
# 测试网络连通性
ping payment.com
curl -I https://api.payment.com/health
```

#### 步骤2：检查支付配置
```bash
# 检查支付设置
- 确认支付接口配置正确
- 检查API密钥是否有效
- 确认商户号状态正常
```

#### 步骤3：清除缓存重试
```bash
# 清除支付相关缓存
- 重启应用
- 清除浏览器缓存
- 刷新支付页面
```

**解决方案**

✅ **网络问题**
1. 检查网络连接
2. 联系网络管理员
3. 使用移动热点测试

✅ **配置问题**
1. 检查支付配置
2. 联系支付服务商
3. 重新配置支付参数

✅ **临时解决方案**
1. 使用现金支付
2. 记录订单信息
3. 稍后补充支付记录

### 问题8：支付完成但订单未更新 🟡

**症状描述**
- 顾客已完成支付
- 订单状态仍显示未支付
- 支付记录不一致

**排查步骤**

#### 步骤1：检查支付状态
```bash
# 查询支付接口状态
- 在支付历史中查找记录
- 检查第三方支付平台
- 确认交易是否成功
```

#### 步骤2：手动更新订单
```bash
# 在订单管理中
- 找到对应订单
- 手动更新支付状态
- 添加支付备注信息
```

**解决方案**

✅ **支付成功订单未更新**
1. 手动标记订单为已支付
2. 记录实际支付信息
3. 联系技术支持同步数据

✅ **支付失败但显示成功**
1. 确认实际支付状态
2. 如需退款联系支付平台
3. 更正订单状态

---

## 📋 订单问题

### 问题9：订单数据丢失 🔴

**症状描述**
- 刚创建的订单找不到
- 订单列表显示空白
- 历史订单无法加载

**排查步骤**

#### 步骤1：检查数据库连接
```bash
# 测试数据库状态
- 检查数据库服务是否运行
- 测试数据库连接
- 查看数据库错误日志
```

#### 步骤2：检查本地缓存
```bash
# 查看本地数据
- 检查本地数据库文件
- 查看临时订单缓存
- 确认数据同步状态
```

#### 步骤3：查找备份数据
```bash
# 查找数据备份
- 检查自动备份文件
- 查看数据库快照
- 确认最近备份时间
```

**解决方案**

✅ **数据库问题**
1. 重启数据库服务
2. 修复数据库文件
3. 从备份恢复数据

✅ **网络同步问题**
1. 检查网络连接
2. 手动触发数据同步
3. 等待自动同步完成

✅ **应急处理**
1. 使用纸质记录
2. 重新输入订单信息
3. 联系技术支持恢复

### 问题10：订单金额计算错误 🟡

**症状描述**
- 订单总额不正确
- 优惠金额计算错误
- 找零金额不对

**排查步骤**

#### 步骤1：检查商品价格
```bash
# 核对商品信息
- 确认商品价格正确
- 检查是否有临时调价
- 验证数量计算
```

#### 步骤2：检查优惠规则
```bash
# 验证优惠计算
- 确认会员等级和折扣
- 检查优惠券有效性
- 验证积分抵扣规则
```

**解决方案**

✅ **价格错误**
1. 更新商品价格
2. 重新计算订单金额
3. 手动调整订单总额

✅ **优惠计算错误**
1. 检查优惠规则设置
2. 重新应用优惠
3. 手动输入正确金额

---

## 🌐 网络连接问题

### 问题11：无法连接服务器 🔴

**症状描述**
- 显示"网络连接失败"
- 数据无法同步
- 在线功能不可用

**排查步骤**

#### 步骤1：基础网络检查
```bash
# 检查网络连接
ipconfig /all
ping *******
ping www.baidu.com
```

#### 步骤2：检查网络设置
```bash
# 网络配置检查
- 确认IP地址正确
- 检查DNS设置
- 验证网关配置
```

#### 步骤3：检查防火墙
```bash
# 防火墙设置
- 检查Windows防火墙
- 确认应用联网权限
- 检查企业防火墙规则
```

**解决方案**

✅ **网络中断**
1. 重启网络设备
2. 检查网线连接
3. 联系网络服务商

✅ **防火墙阻止**
1. 添加应用到白名单
2. 临时关闭防火墙测试
3. 配置防火墙规则

✅ **DNS问题**
1. 更换DNS服务器
2. 刷新DNS缓存
3. 使用IP地址连接

### 问题12：网络速度慢 🟡

**症状描述**
- 数据加载缓慢
- 图片显示延迟
- 操作响应慢

**排查步骤**

#### 步骤1：测试网络速度
```bash
# 使用网速测试工具
- 测试下载速度
- 测试上传速度
- 比较不同时间段速度
```

#### 步骤2：检查网络使用
```bash
# 查看网络占用
- 检查其他设备使用情况
- 关闭不必要的网络应用
- 优化网络带宽分配
```

**解决方案**

✅ **网络拥堵**
1. 限制其他设备使用
2. 错峰使用网络
3. 升级网络带宽

✅ **设备问题**
1. 重启路由器
2. 更新网卡驱动
3. 检查网线质量

---

## 🖥️ 硬件设备问题

### 问题13：摄像头无法使用 🟡

**症状描述**
- 摄像头无图像显示
- 设备管理器显示感叹号
- AI识别功能不可用

**排查步骤**

#### 步骤1：检查物理连接
```bash
# 硬件检查
- 确认USB连接牢固
- 尝试不同USB接口
- 检查摄像头指示灯
```

#### 步骤2：检查设备状态
```bash
# 在设备管理器中检查
- 查看摄像头设备状态
- 检查驱动程序版本
- 查看错误代码
```

#### 步骤3：测试摄像头功能
```bash
# 使用Windows相机应用测试
- 打开相机应用
- 测试图像清晰度
- 检查摄像头设置
```

**解决方案**

✅ **连接问题**
1. 重新插拔USB连接
2. 更换USB接口
3. 检查USB线缆

✅ **驱动问题**
1. 更新摄像头驱动
2. 重新安装驱动程序
3. 使用通用驱动

✅ **硬件故障**
1. 更换摄像头设备
2. 联系硬件供应商
3. 使用备用摄像头

### 问题14：打印机无法工作 🟡

**症状描述**
- 小票无法打印
- 打印机显示错误
- 打印作业卡在队列中

**排查步骤**

#### 步骤1：检查打印机状态
```bash
# 打印机检查
- 确认电源连接
- 检查纸张是否充足
- 查看错误指示灯
```

#### 步骤2：检查连接
```bash
# 连接检查
- 确认USB连接
- 测试串口连接
- 检查网络打印机IP
```

#### 步骤3：测试打印功能
```bash
# 打印测试
- 打印测试页
- 检查打印队列
- 查看打印机驱动状态
```

**解决方案**

✅ **硬件问题**
1. 更换打印纸
2. 清洁打印头
3. 重启打印机

✅ **连接问题**
1. 重新连接数据线
2. 重新安装驱动
3. 设置为默认打印机

✅ **软件问题**
1. 清除打印队列
2. 重新安装打印驱动
3. 使用通用打印驱动

---

## 💾 数据问题

### 问题15：数据同步失败 🟡

**症状描述**
- 数据无法上传到服务器
- 本地和服务器数据不一致
- 显示同步错误提示

**排查步骤**

#### 步骤1：检查同步状态
```bash
# 查看同步日志
- 检查最后同步时间
- 查看同步错误信息
- 确认数据量大小
```

#### 步骤2：检查网络连接
```bash
# 网络状态检查
- 测试服务器连通性
- 检查上传带宽
- 确认认证状态
```

#### 步骤3：手动触发同步
```bash
# 强制同步
- 在设置中手动同步
- 重启应用后自动同步
- 分批次同步数据
```

**解决方案**

✅ **网络问题**
1. 改善网络连接
2. 等待网络恢复
3. 使用有线网络

✅ **数据冲突**
1. 解决数据冲突
2. 选择保留版本
3. 合并数据变更

✅ **服务器问题**
1. 等待服务器恢复
2. 联系技术支持
3. 保存本地数据

### 问题16：数据备份失败 🟡

**症状描述**
- 自动备份不执行
- 备份文件损坏
- 无法恢复数据

**排查步骤**

#### 步骤1：检查备份设置
```bash
# 备份配置检查
- 确认备份计划设置
- 检查备份路径
- 验证备份权限
```

#### 步骤2：检查存储空间
```bash
# 磁盘空间检查
- 查看可用磁盘空间
- 检查备份文件大小
- 清理旧备份文件
```

**解决方案**

✅ **存储空间不足**
1. 清理磁盘空间
2. 更换备份位置
3. 压缩备份文件

✅ **备份配置错误**
1. 重新配置备份设置
2. 测试备份功能
3. 设置多重备份策略

---

## 🖼️ 界面显示问题

### 问题17：界面显示异常 🟢

**症状描述**
- 字体显示模糊
- 界面元素错位
- 颜色显示不正常

**排查步骤**

#### 步骤1：检查显示设置
```bash
# 显示配置检查
- 确认屏幕分辨率
- 检查缩放比例
- 调整字体大小
```

#### 步骤2：检查图形驱动
```bash
# 驱动程序检查
- 更新显卡驱动
- 检查硬件兼容性
- 重置显示设置
```

**解决方案**

✅ **显示设置问题**
1. 调整屏幕分辨率
2. 修改缩放比例
3. 重置显示设置

✅ **驱动问题**
1. 更新图形驱动
2. 重新安装驱动
3. 使用通用驱动

---

## 🚨 紧急处理流程

### 系统完全无法使用时的应急方案

#### 1. 立即行动 (5分钟内)
```bash
🔴 紧急措施：
1. 准备纸质订单记录表
2. 启用备用收银方式
3. 通知顾客临时使用现金支付
4. 记录故障发生时间和现象
```

#### 2. 基础排查 (10分钟内)
```bash
🔧 快速检查：
1. 重启应用程序
2. 检查网络连接
3. 确认硬件设备状态
4. 查看错误信息
```

#### 3. 联系技术支持 (15分钟内)
```bash
📞 技术支持热线：400-XXX-XXXX
📧 紧急邮箱：<EMAIL>

提供信息：
- 故障现象描述
- 错误信息截图
- 最后正常使用时间
- 当前系统状态
```

#### 4. 数据保护措施
```bash
💾 数据安全：
1. 停止所有操作避免数据损坏
2. 保存当前工作状态
3. 使用备用方式记录新订单
4. 等待技术支持指导
```

### 网络中断应急流程

#### 离线模式启用
```bash
📱 离线操作：
1. 系统自动切换离线模式
2. 使用本地数据库
3. 记录所有操作到本地
4. 网络恢复后自动同步
```

#### 手工记录流程
```bash
📝 纸质记录：
1. 使用备用订单表格
2. 详细记录商品和金额
3. 计算器辅助计算
4. 现金交易优先
```

---

## 📞 联系技术支持

### 技术支持联系方式

#### 🔴 紧急支持 (24小时)
- **电话**: 400-XXX-XXXX (按1转紧急支持)
- **微信**: 搜索"助老收银紧急支持"
- **QQ**: 123456789 (紧急支持群)

#### 🟡 一般支持 (工作时间 9:00-18:00)
- **电话**: 400-XXX-XXXX (按2转技术支持)
- **邮箱**: <EMAIL>
- **在线客服**: https://support.elderly-cashier.com

#### 🟢 自助服务 (24小时可用)
- **知识库**: https://kb.elderly-cashier.com
- **视频教程**: https://video.elderly-cashier.com
- **用户手册**: https://docs.elderly-cashier.com

### 准备以下信息便于快速解决问题

#### 系统信息
```bash
📋 基本信息：
- 系统版本号
- 操作系统版本
- 最后正常使用时间
- 故障发生频率
```

#### 错误详情
```bash
🔍 错误信息：
- 完整错误信息截图
- 错误发生的具体步骤
- 是否可以重现问题
- 临时解决方案尝试结果
```

#### 环境信息
```bash
🏪 使用环境：
- 餐厅类型和规模
- 日常订单量
- 使用的硬件设备
- 网络环境描述
```

### 技术支持等级

#### L1 - 基础支持
- **响应时间**: 30分钟内
- **解决问题**: 常见使用问题、基础故障
- **支持方式**: 电话、在线聊天

#### L2 - 技术支持  
- **响应时间**: 2小时内
- **解决问题**: 复杂技术问题、系统配置
- **支持方式**: 远程协助、邮件支持

#### L3 - 专家支持
- **响应时间**: 4小时内
- **解决问题**: 系统集成、定制需求
- **支持方式**: 现场支持、专家会议

### 服务保障

#### 服务时间
- **工作日**: 9:00-18:00 标准服务
- **节假日**: 10:00-16:00 值班服务  
- **紧急情况**: 24小时响应

#### 响应承诺
- **电话响应**: 3声铃响内接听
- **问题确认**: 15分钟内确认问题
- **解决方案**: 1小时内提供解决方案
- **重大故障**: 2小时内现场支持

---

## 📝 问题记录模板

### 故障报告模板
```
故障时间：____年__月__日 __:__
报告人员：_______________
联系方式：_______________

问题描述：
□ 系统无法启动
□ 登录失败  
□ 功能异常
□ 性能问题
□ 其他：___________

具体现象：
_________________________
_________________________

错误信息：
_________________________

已尝试解决方案：
□ 重启应用
□ 重启电脑
□ 检查网络
□ 其他：___________

紧急程度：
□ 🔴 紧急 - 无法营业
□ 🟡 重要 - 影响效率  
□ 🟢 一般 - 体验问题

备注：
_________________________
```

---

*问题排查指南版本：v1.0 | 更新时间：2024年12月15日*

*遇到问题请先参考本指南，无法解决时及时联系技术支持！* 