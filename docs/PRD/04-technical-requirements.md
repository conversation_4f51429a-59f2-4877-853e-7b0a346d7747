# 技术架构和非功能性需求

## 1. 系统架构概述

### 1.1 整体架构设计

助老订餐收银系统采用**前后端分离的分层架构**，支持跨平台部署，具备良好的可扩展性和维护性。

```
┌─────────────────────────────────────────────────────────────┐
│                    客户端层 (Client Layer)                     │
├─────────────────────┬───────────────────────────────────────┤
│   Windows 桌面端     │        Android 移动端                  │
│  (Electron + React) │   (React Native + TypeScript)         │
└─────────────────────┴───────────────────────────────────────┘
                              │
                              ▼ HTTP/HTTPS + WebSocket
┌─────────────────────────────────────────────────────────────┐
│                    API网关层 (API Gateway)                    │
│         负载均衡 • 限流控制 • 认证授权 • 日志记录              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    应用服务层 (Application Layer)             │
├─────────────────┬─────────────────┬─────────────────────────┤
│   收银服务模块   │    会员服务模块   │      支付服务模块        │
│   Order Service │  Member Service │    Payment Service      │
├─────────────────┼─────────────────┼─────────────────────────┤
│   商品服务模块   │    AI识别模块    │      系统管理模块        │
│ Product Service │   AI Service    │     Admin Service       │
└─────────────────┴─────────────────┴─────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据访问层 (Data Access Layer)             │
│              ORM (Sequelize) + 连接池管理                     │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层 (Data Storage Layer)            │
├─────────────────┬─────────────────┬─────────────────────────┤
│   MySQL 数据库   │     Redis 缓存   │      本地文件存储        │
│   (主要业务数据)  │   (会话/缓存)    │    (日志/图片/AI模型)     │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### 1.2 核心设计原则

- **模块化设计**：各功能模块独立开发、部署和维护
- **适老化优先**：所有技术方案都以老年用户体验为核心
- **本地化部署**：支持完全离线运行，不依赖外部网络
- **高可用性**：99.9%的系统可用性保证
- **安全可控**：数据加密存储，权限精细控制

## 2. 技术选型方案

### 2.1 前端技术栈

#### 核心开发方案
- **开发框架**：React 18.x + TypeScript 4.8+
- **状态管理**：Redux Toolkit + RTK Query
- **UI组件库**：Ant Design 5.x (适老化定制)
- **样式方案**：CSS Modules + PostCSS
- **路由管理**：React Router 6.x
- **构建工具**：Webpack 5.x + Babel

#### 跨平台套壳方案

**Windows桌面端**
- **套壳方案**：Electron 19.x
- **原生集成**：Node.js API (文件系统、硬件调用)
- **打包方式**：electron-builder
- **自动更新**：electron-updater
- **安装包大小**：约150MB (包含Node.js runtime)

**Android移动端**
- **套壳方案**：Capacitor 4.x (推荐) 或 Cordova
- **原生桥接**：Java/Kotlin插件开发
- **WebView引擎**：Chromium-based WebView
- **打包方式**：Android Studio Gradle
- **APK大小**：约50MB

**代码复用率**：85%+ (业务逻辑和UI组件完全复用)

#### TensorFlow.js 详细技术方案

**模型架构选择**
- **基础模型**：MobileNetV2 (预训练ImageNet)
- **自定义分类层**：Dense层 + Dropout + Softmax
- **输入尺寸**：224x224x3 (RGB图像)
- **输出类别**：100-300种常见菜品

**性能优化策略**
```javascript
// 模型加载优化
const model = await tf.loadLayersModel('/models/dish-recognition/model.json', {
  strict: false,
  weightPathPrefix: '/models/dish-recognition/'
});

// GPU加速配置
await tf.setBackend('webgl'); // 优先使用GPU
await tf.ready();

// 模型预热
const warmupTensor = tf.zeros([1, 224, 224, 3]);
model.predict(warmupTensor).dispose();
warmupTensor.dispose();
```

**实际性能指标**
- **模型大小**：15-25MB (量化后)
- **推理速度**：
  - 移动端：2-4秒/张
  - 桌面端：1-2秒/张 
- **内存占用**：200-400MB
- **准确率**：
  - Top-1: 85-92% (理想光线条件)
  - Top-3: 95-98%
- **支持格式**：JPEG, PNG, WebP

**技术实现流程**
```javascript
// 图像预处理
const preprocessImage = (imageElement) => {
  return tf.browser.fromPixels(imageElement)
    .resizeNearestNeighbor([224, 224])
    .toFloat()
    .div(255.0)
    .expandDims(0);
};

// 菜品识别
const recognizeDish = async (imageElement) => {
  const preprocessed = preprocessImage(imageElement);
  const predictions = model.predict(preprocessed);
  const probabilities = await predictions.data();
  
  // 获取Top-3预测结果
  const results = Array.from(probabilities)
    .map((prob, index) => ({ dishId: index, confidence: prob }))
    .sort((a, b) => b.confidence - a.confidence)
    .slice(0, 3);
    
  // 清理内存
  preprocessed.dispose();
  predictions.dispose();
  
  return results;
};
```

**局限性和应对方案**
1. **光线敏感**
   - 解决方案：图像增强预处理，多角度识别
2. **遮挡识别困难**
   - 解决方案：提示用户调整拍摄角度
3. **新菜品无法识别**
   - 解决方案：在线学习机制，人工标注反馈

**模型训练和更新**
- **训练数据**：每种菜品需要500-1000张标注图片
- **数据增强**：旋转、缩放、亮度调整、颜色变换
- **模型更新**：每月模型版本更新，支持增量学习
- **A/B测试**：新旧模型并行测试，确保效果提升

**与传统方案对比**
| 方案 | TensorFlow.js | 云端API | 条码扫描 |
|------|--------------|---------|----------|
| 响应速度 | 2-4秒 | 1-2秒 (需网络) | 0.5秒 |
| 离线支持 | ✅ 完全支持 | ❌ 需要网络 | ✅ 支持 |
| 数据安全 | ✅ 本地处理 | ❌ 上传云端 | ✅ 本地 |
| 识别准确率 | 85-92% | 95-98% | 99%+ |
| 部署成本 | 低 | 中高 | 低 |
| 维护复杂度 | 中 | 低 | 低 |

**推荐配置**
- **开发环境**：优先使用模拟数据，减少模型加载时间
- **生产环境**：模型文件CDN分发，启用浏览器缓存
- **降级方案**：TensorFlow.js识别失败时，回退到手动选择菜品

### 2.2 后端技术栈

#### 核心服务
- **运行环境**：Node.js 18 LTS
- **Web框架**：Express.js 4.x + Helmet (安全中间件)
- **开发语言**：TypeScript 4.8+
- **API风格**：RESTful API + GraphQL (查询优化)
- **实时通信**：Socket.IO 4.x
- **任务队列**：Bull Queue (Redis-based)

#### 数据库和存储
- **主数据库**：MySQL 8.0+ (InnoDB引擎)
- **缓存系统**：Redis 7.0+
- **文件存储**：本地文件系统 + 分布式存储(可选)
- **数据库ORM**：Sequelize 6.x
- **数据迁移**：Sequelize CLI

### 2.3 AI和算法技术

#### AI菜品识别
- **深度学习框架**：TensorFlow.js 4.x (本地推理)
- **模型格式**：TFJS Model (.json + .bin)
- **图像处理**：OpenCV.js 4.x
- **模型训练**：Python + TensorFlow 2.x (离线训练)
- **模型优化**：量化压缩、剪枝优化

**技术路线**：
```
数据采集 → 图像预处理 → 特征提取 → 分类识别 → 置信度评估 → 结果输出
    ↓          ↓          ↓          ↓          ↓          ↓
 摄像头输入   图像增强    CNN特征    Softmax    阈值过滤   JSON格式
```

### 2.4 开发和运维工具

#### 开发工具
- **版本控制**：Git + GitLab/GitHub
- **代码规范**：ESLint + Prettier + Husky
- **测试框架**：Jest + React Testing Library
- **API文档**：Swagger/OpenAPI 3.0
- **包管理**：npm/yarn workspace

#### 部署和监控
- **容器化**：Docker + Docker Compose
- **进程管理**：PM2 (Node.js应用)
- **日志管理**：Winston + Logrotate
- **监控告警**：自研监控 + 邮件/短信通知
- **备份策略**：定时数据库备份 + 增量备份

## 3. 系统性能要求

### 3.1 响应时间要求

| 功能模块 | 目标响应时间 | 最大响应时间 | 备注 |
|---------|-------------|-------------|------|
| 收银主页加载 | < 1秒 | < 2秒 | 首次启动可到3秒 |
| 菜品添加/删除 | < 0.5秒 | < 1秒 | 实时响应 |
| 支付处理 | < 2秒 | < 5秒 | 包含第三方调用 |
| AI菜品识别 | < 3秒 | < 5秒 | 本地推理 |
| 会员查询 | < 1秒 | < 2秒 | 缓存优化 |
| 订单查询 | < 1秒 | < 3秒 | 分页查询 |
| 数据同步 | < 5秒 | < 10秒 | 批量操作 |

### 3.2 并发性能要求

- **同时在线用户**：100个收银员并发
- **峰值TPS**：500次/秒 (订单提交)
- **数据库连接池**：最大50个连接
- **内存使用**：单实例 < 2GB
- **CPU使用率**：正常 < 70%，峰值 < 90%
- **磁盘I/O**：读取 < 100MB/s，写入 < 50MB/s

### 3.3 可用性和稳定性

- **系统可用性**：99.9% (年停机时间 < 8.76小时)
- **故障恢复时间**：< 5分钟 (RTO)
- **数据丢失容忍**：< 1分钟 (RPO)
- **故障检测时间**：< 1分钟
- **负载测试**：支持1.5倍峰值负载

## 4. 安全规范要求

### 4.1 数据安全

#### 数据加密
- **传输加密**：TLS 1.3 + HTTPS
- **存储加密**：AES-256数据库加密
- **敏感字段**：会员信息、支付数据单独加密
- **密钥管理**：分层密钥体系，定期轮换

#### 数据备份
- **备份频率**：每日全量 + 实时增量
- **备份保留**：本地7天 + 远程30天
- **备份验证**：每周自动恢复测试
- **异地备份**：支持多地备份 (可选)

### 4.2 访问控制

#### 身份认证
- **登录方式**：用户名密码 + 二步验证
- **会话管理**：JWT Token + 刷新机制
- **密码策略**：8位以上，包含字母数字特殊字符
- **锁定机制**：5次失败后锁定30分钟

#### 权限控制
- **角色体系**：超级管理员、门店管理员、收银员、查看员
- **功能权限**：基于RBAC的细粒度权限控制
- **数据权限**：基于门店和时间的数据隔离
- **操作审计**：所有敏感操作记录审计日志

### 4.3 网络安全

- **防火墙配置**：仅开放必要端口 (80, 443, 22)
- **API安全**：请求限流 + API Key验证
- **输入验证**：严格的参数校验，防止SQL注入和XSS
- **安全扫描**：定期漏洞扫描和安全评估

### 4.4 合规要求

- **数据保护**：符合个人信息保护法要求
- **财务合规**：支持税务系统对接
- **审计跟踪**：完整的操作链路记录
- **数据脱敏**：测试环境数据脱敏处理

## 5. 兼容性要求

### 5.1 操作系统兼容

#### Windows平台
- **支持版本**：Windows 10 1809+ / Windows 11
- **架构支持**：x64, ARM64 (未来支持)
- **内存要求**：最小4GB，推荐8GB
- **存储空间**：最小2GB，推荐5GB
- **硬件要求**：支持触屏，USB接口，网卡

#### Android平台
- **支持版本**：Android 8.0 (API 26)+
- **架构支持**：ARM64-v8a, armeabi-v7a
- **内存要求**：最小3GB，推荐4GB
- **存储空间**：最小1GB，推荐2GB
- **硬件要求**：摄像头，蓝牙，WiFi

### 5.2 浏览器兼容 (管理后台)

| 浏览器 | 最低版本 | 推荐版本 | 支持特性 |
|-------|---------|---------|----------|
| Chrome | 90+ | 最新版 | 完整支持 |
| Firefox | 88+ | 最新版 | 完整支持 |
| Safari | 14+ | 最新版 | 完整支持 |
| Edge | 90+ | 最新版 | 完整支持 |

### 5.3 设备集成兼容

- **打印机**：支持ESC/POS指令的热敏打印机
- **扫码设备**：USB/串口条码扫描器
- **称重设备**：RS232/USB电子秤
- **支付设备**：银联POS机，扫码盒子
- **显示设备**：客显屏，双屏显示

## 6. 可扩展性设计

### 6.1 架构可扩展性

#### 微服务架构
- **服务拆分**：按业务域拆分独立服务
- **API网关**：统一入口，版本管理，限流控制
- **服务发现**：基于Consul/Eureka的服务注册
- **配置中心**：集中配置管理，热更新支持

#### 数据库扩展
- **读写分离**：主从复制，读写分离
- **分库分表**：按门店/时间分片
- **缓存策略**：多级缓存，缓存穿透保护
- **数据归档**：历史数据自动归档

### 6.2 功能可扩展性

#### 插件架构
- **插件接口**：标准化插件开发接口
- **热插拔**：支持运行时插件加载/卸载
- **第三方集成**：ERP系统、财务系统对接
- **API开放**：面向第三方的开放API

#### 多租户支持
- **数据隔离**：租户级数据隔离
- **功能定制**：按租户定制功能模块
- **计费模式**：支持多种计费模式
- **SaaS化**：支持云部署模式

## 7. 部署架构要求

### 7.1 本地部署方案

#### 单机部署 (推荐)
```
┌─────────────────────────────────────┐
│           POS收银机/PC              │
├─────────────────────────────────────┤
│  前端应用 (Electron)                │
│  后端服务 (Node.js)                 │
│  数据库 (MySQL)                     │
│  缓存 (Redis)                       │
│  AI模型 (TensorFlow.js)             │
└─────────────────────────────────────┘
```

#### 集群部署 (可选)
```
┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│ 收银终端 1   │  │ 收银终端 2   │  │ 收银终端 N   │
└─────────────┘  └─────────────┘  └─────────────┘
        │                │                │
        └────────────────┼────────────────┘
                         │
                ┌─────────────────┐
                │    中央服务器    │
                │  负载均衡+数据库  │
                └─────────────────┘
```

### 7.2 云部署方案 (可选)

#### 容器化部署
- **容器编排**：Docker Compose / Kubernetes
- **镜像管理**：私有Docker Registry
- **配置管理**：ConfigMap + Secret
- **存储方案**：持久卷 + 对象存储

#### 混合云架构
- **本地计算**：AI推理本地执行
- **云端数据**：汇总数据云端存储
- **边缘同步**：增量数据同步
- **灾备恢复**：云端备份和恢复

## 8. 监控和运维要求

### 8.1 系统监控

#### 基础监控
- **系统指标**：CPU、内存、磁盘、网络
- **应用指标**：QPS、响应时间、错误率
- **业务指标**：订单量、支付成功率、用户活跃度
- **自定义指标**：AI识别准确率、设备状态

#### 日志管理
```
应用日志 → 日志收集 → 日志存储 → 日志分析 → 告警通知
   ↓          ↓          ↓          ↓          ↓
 文件输出   Filebeat   ElasticSearch  Kibana   邮件/短信
```

### 8.2 故障处理

#### 告警策略
- **告警级别**：紧急、重要、一般、提醒
- **告警渠道**：短信、邮件、钉钉、电话
- **告警收敛**：防止告警风暴，智能收敛
- **值班机制**：7x24小时值班，分级响应

#### 故障恢复
- **自动恢复**：服务自重启，健康检查
- **快速回滚**：版本回滚，配置回滚
- **故障转移**：主备切换，流量切换
- **应急方案**：离线模式，降级服务

## 9. 开发规范要求

### 9.1 代码规范

#### 编码标准
- **命名规范**：驼峰命名，语义化命名
- **注释规范**：JSDoc文档注释
- **目录结构**：模块化目录组织
- **依赖管理**：锁定版本，安全扫描

#### 质量控制
- **代码审查**：强制Code Review
- **测试覆盖率**：单元测试 > 80%，集成测试 > 60%
- **静态检查**：ESLint + SonarQube
- **性能测试**：定期压力测试

### 9.2 发布流程

#### CI/CD流程
```
代码提交 → 自动构建 → 自动测试 → 安全扫描 → 部署测试环境 → 人工验收 → 部署生产环境
    ↓          ↓          ↓          ↓            ↓            ↓            ↓
  Git Push   Docker Build  Jest/E2E  SAST/DAST   Staging     UAT Testing   Production
```

#### 版本管理
- **版本策略**：语义化版本 (Semantic Versioning)
- **分支策略**：Git Flow工作流
- **发布周期**：每月大版本，每周小版本
- **热修复**：紧急修复快速发布

## 10. 成本和资源预算

### 10.1 硬件成本预算

#### 单机部署 (每套)
| 项目 | 配置要求 | 预估成本 | 备注 |
|-----|---------|---------|------|
| 主机设备 | i5+8GB+256GB SSD | ¥4000-6000 | 工控机或品牌PC |
| 显示设备 | 15寸触摸屏 | ¥1500-2500 | 电容触摸 |
| 打印设备 | 58mm热敏打印机 | ¥300-500 | USB接口 |
| 扫码设备 | 二维码扫描器 | ¥200-400 | USB/串口 |
| 网络设备 | 千兆交换机+WiFi | ¥300-500 | 企业级 |
| **小计** |  | **¥6300-9900** | 单套完整方案 |

#### 云服务成本 (可选)
- **云服务器**：¥200-500/月 (2核4GB)
- **数据库**：¥300-800/月 (MySQL实例)
- **对象存储**：¥50-200/月 (100GB)
- **CDN加速**：¥100-300/月
- **总计**：¥650-1800/月

### 10.2 开发成本预估

#### 人力成本 (6个月开发周期)
| 角色 | 人数 | 月薪 | 总成本 | 备注 |
|-----|-----|------|-------|------|
| 项目经理 | 1 | ¥25000 | ¥150000 | 全程项目管理 |
| 前端工程师 | 2 | ¥20000 | ¥240000 | React/RN开发 |
| 后端工程师 | 2 | ¥22000 | ¥264000 | Node.js开发 |
| AI工程师 | 1 | ¥30000 | ¥180000 | 图像识别 |
| 测试工程师 | 1 | ¥15000 | ¥90000 | 质量保证 |
| UI设计师 | 1 | ¥18000 | ¥108000 | 适老化设计 |
| **总计** | **8人** |  | **¥1032000** | 6个月周期 |

#### 其他成本
- **第三方服务**：¥50000 (支付接口、短信服务等)
- **设备测试**：¥30000 (各种硬件设备)
- **培训成本**：¥20000 (用户培训、技术培训)
- **运维成本**：¥100000/年 (维护、支持)

## 11. 风险评估和应对

### 11.1 技术风险

#### 高风险项
1. **AI识别准确率不达标**
   - 风险描述：本地AI模型识别准确率低于90%
   - 应对措施：充分的数据训练，多模型融合，人工标注校正
   - 备选方案：云端AI识别 + 本地缓存

2. **跨平台兼容性问题**
   - 风险描述：Windows/Android适配困难
   - 应对措施：早期原型验证，充分的设备测试
   - 备选方案：分期发布，先Windows后Android

#### 中风险项
1. **性能不达标**
   - 应对措施：性能基准测试，代码优化，硬件升级
2. **第三方集成困难**
   - 应对措施：早期技术调研，备选方案设计

### 11.2 业务风险

1. **用户适应性风险**
   - 应对措施：充分的适老化设计，用户培训，分步骤引导
2. **硬件故障风险**
   - 应对措施：设备冗余，快速维修，远程诊断

### 11.3 项目风险

1. **开发延期风险**
   - 应对措施：合理项目计划，关键路径管理，风险缓冲
2. **需求变更风险**
   - 应对措施：需求冻结机制，变更影响评估

## 12. 总结

本技术架构方案充分考虑了助老订餐收银系统的特殊需求，在技术选型上优先考虑稳定性和易用性，在架构设计上注重模块化和可扩展性。通过本地化部署和离线运行能力，确保系统在各种网络环境下的稳定运行。

**核心技术亮点**：
- 跨平台统一架构，代码复用率高
- 本地AI推理，保障数据安全和响应速度
- 适老化优先设计，确保易用性
- 完善的监控和运维体系，保障系统稳定性
- 模块化插件架构，支持功能扩展

该技术方案为助老订餐收银系统的开发提供了完整的技术指导，确保项目能够按质按时交付。 