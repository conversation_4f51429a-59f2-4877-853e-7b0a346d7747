# 助老订餐收银系统 - 功能需求规范

## 1. 功能模块总览

本系统包含11个核心功能模块，每个模块都针对助老订餐场景进行了专门优化：

1. 收银主页模块
2. 购物车管理模块
3. 会员卡管理模块
4. 支付结算模块
5. AI扫菜识别模块
6. 折扣计算模块
7. 退款管理模块
8. 订单查询模块
9. 充值管理模块
10. 对账功能模块
11. 广告展示模块

## 2. 收银主页模块

### 2.1 模块概述
收银主页是系统的核心界面，提供订单信息展示、快捷操作和系统状态监控功能。

### 2.2 功能规范

#### 2.2.1 订单信息展示
**功能描述**：实时显示当前订单的详细信息

**输入要求**：
- 当前购物车数据
- 会员信息（如有）
- 折扣规则配置

**输出结果**：
- 菜品列表（名称、单价、数量、小计）
- 订单总计（原价、折扣、实付金额）
- 会员信息摘要
- 适用折扣信息

**界面要求**：
- 字体大小≥18px，支持放大至24px
- 高对比度配色（背景白色，文字黑色）
- 菜品列表采用卡片式布局
- 总计金额突出显示（红色，加粗）

#### 2.2.2 快捷结账功能
**功能描述**：提供一键快速结账操作

**触发条件**：
- 购物车中有商品
- 选择了支付方式
- 通过了支付验证

**业务流程**：
1. 验证订单完整性
2. 计算最终金额（含折扣）
3. 调用支付接口
4. 生成订单记录
5. 打印小票（可选）
6. 清空购物车

**异常处理**：
- 支付失败：显示错误信息，保留订单状态
- 网络异常：支持离线模式，同步恢复后上传
- 打印失败：记录日志，提供重新打印选项

#### 2.2.3 系统状态显示
**功能描述**：显示系统运行状态和关键信息

**显示内容**：
- 当前时间和日期
- 收银员信息
- 今日交易汇总
- 网络连接状态
- 设备状态指示

### 2.3 交互设计
- 支持键盘快捷键操作（F1-F12）
- 提供语音操作提示
- 支持触屏和鼠标双重操作模式
- 错误信息采用弹窗形式，字体清晰易读

## 3. 购物车管理模块

### 3.1 模块概述
购物车管理模块负责商品的添加、修改、删除和批量操作功能。

### 3.2 功能规范

#### 3.2.1 商品添加
**功能描述**：向购物车添加商品

**输入方式**：
- 商品编码输入
- 商品名称搜索
- AI扫菜识别
- 快捷按钮选择

**业务规则**：
- 同一商品自动合并数量
- 超出库存时给出提醒
- 支持称重商品和计件商品
- 自动计算小计金额

**数据验证**：
- 商品编码合法性检查
- 数量范围验证（0.1-999）
- 价格有效性验证

#### 3.2.2 数量修改
**功能描述**：修改购物车中商品的数量

**操作方式**：
- 数字键盘直接输入
- +/- 按钮增减
- 滑动条调整（移动端）

**业务逻辑**：
- 实时更新小计和总计
- 数量为0时自动删除商品
- 超出限制时弹出警告
- 支持小数点数量（称重商品）

#### 3.2.3 商品删除
**功能描述**：从购物车删除指定商品

**删除方式**：
- 单个商品删除
- 批量选择删除
- 清空购物车

**确认机制**：
- 单个删除：无需确认
- 批量删除：弹出确认对话框
- 清空购物车：二次确认机制

#### 3.2.4 购物车操作
**功能描述**：购物车的批量操作功能

**支持操作**：
- 全选/取消全选
- 批量删除选中商品
- 购物车内容导出
- 订单暂存和恢复

### 3.3 数据结构
```
购物车项目 {
  商品ID: string
  商品名称: string
  商品单价: number
  购买数量: number
  商品小计: number
  商品分类: string
  是否称重: boolean
  添加时间: datetime
}
```

## 4. 会员卡管理模块

### 4.1 模块概述
会员卡管理模块支持电子会员卡、身份识别、积分管理等会员服务功能。

### 4.2 功能规范

#### 4.2.1 会员身份识别
**功能描述**：通过多种方式识别会员身份

**识别方式**：
- 会员卡号输入
- 二维码扫描
- 人脸识别（可选）
- 手机号查询
- 身份证号查询

**识别流程**：
1. 选择识别方式
2. 输入/扫描识别信息
3. 系统验证会员信息
4. 显示会员详情
5. 应用会员权益

#### 4.2.2 会员信息展示
**功能描述**：显示会员的详细信息和权益

**显示内容**：
- 会员基本信息（姓名、等级、有效期）
- 账户余额和积分
- 享受折扣信息
- 消费历史摘要
- 会员权益说明

**界面设计**：
- 会员头像展示区域
- 信息分区清晰展示
- 重要信息突出显示
- 支持信息快速切换

#### 4.2.3 积分管理
**功能描述**：会员积分的获取、使用和查询

**积分规则**：
- 消费积分：1元=1积分
- 积分抵扣：100积分=1元
- 积分有效期：2年
- 生日双倍积分

**操作功能**：
- 自动积分计算
- 积分抵扣确认
- 积分历史查询
- 积分规则说明

#### 4.2.4 会员等级管理
**功能描述**：根据消费金额自动调整会员等级

**等级体系**：
- 普通会员：0-999元
- 银卡会员：1000-4999元（9.5折）
- 金卡会员：5000-9999元（9折）
- 钻石会员：10000元以上（8.5折）

**升级规则**：
- 基于累计消费金额
- 自动升级，不可降级
- 升级即时生效
- 系统自动提醒

### 4.3 数据安全
- 会员信息加密存储
- 敏感信息脱敏显示
- 操作日志记录
- 权限访问控制

## 5. 支付结算模块

### 5.1 模块概述
支付结算模块支持多种支付方式，确保支付安全和结算准确。

### 5.2 功能规范

#### 5.2.1 支付方式支持
**支持类型**：
- 现金支付
- 银行卡POS支付
- 微信支付
- 支付宝支付
- 储值卡支付
- 老年卡支付
- 组合支付

#### 5.2.2 现金支付
**功能描述**：处理现金收款和找零

**操作流程**：
1. 确认应付金额
2. 输入实收金额
3. 自动计算找零
4. 确认收款完成
5. 打印收款凭据

**找零规则**：
- 支持到分的精确找零
- 提供找零建议（减少零钱）
- 异常金额二次确认
- 找零记录保存

#### 5.2.3 电子支付
**功能描述**：处理各种电子支付方式

**微信/支付宝支付**：
- 显示收款二维码
- 支持扫码支付
- 实时状态监控
- 支付结果确认

**银行卡支付**：
- POS机集成对接
- 支付状态同步
- 交易凭证打印
- 失败重试机制

#### 5.2.4 储值卡支付
**功能描述**：使用预充值卡片进行支付

**业务流程**：
1. 读取卡片信息
2. 验证卡片有效性
3. 检查余额充足性
4. 执行扣款操作
5. 更新卡片余额
6. 生成交易记录

**异常处理**：
- 余额不足：提示充值或组合支付
- 卡片无效：显示错误信息
- 读卡失败：重试或切换支付方式

### 5.3 小票生成
**功能描述**：生成和打印交易小票

**小票内容**：
- 商户信息
- 交易时间和流水号
- 商品清单
- 优惠信息
- 支付方式和金额
- 会员信息（如有）
- 积分变动
- 二维码（用于查询）

**打印要求**：
- 字体清晰易读
- 重要信息突出显示
- 支持重复打印
- 纸张节约设计

## 6. AI扫菜识别模块

### 6.1 模块概述
AI扫菜识别模块通过图像识别技术自动识别菜品信息，提高收银效率。

### 6.2 功能规范

#### 6.2.1 图像采集
**功能描述**：通过摄像头实时采集菜品图像

**技术要求**：
- 连续视频流分析
- 720P以上分辨率
- 30fps帧率
- 自动对焦和曝光调节

**采集模式**：
- 实时识别模式
- 手动触发模式
- 批量识别模式
- 历史图像回放

#### 6.2.2 菜品识别
**功能描述**：识别图像中的菜品种类和属性

**识别能力**：
- 支持100+常见菜品
- 识别准确率≥90%
- 响应时间≤2秒
- 支持多菜品同时识别

**识别信息**：
- 菜品名称
- 预估重量/份数
- 建议价格
- 营养信息
- 过敏原提醒

#### 6.2.3 结果确认
**功能描述**：对识别结果进行确认和调整

**确认流程**：
1. 显示识别结果
2. 提供调整选项
3. 确认或修改信息
4. 添加到购物车
5. 学习反馈优化

**调整功能**：
- 菜品名称修正
- 数量/重量调整
- 价格手动修改
- 备注信息添加

### 6.3 本地化部署
**技术架构**：
- 本地AI模型部署
- 离线识别能力
- 模型定期更新
- 云端训练本地推理

**性能优化**：
- GPU加速推理
- 模型压缩优化
- 缓存机制
- 批处理优化

## 7. 折扣计算模块

### 7.1 模块概述
折扣计算模块根据各种优惠规则自动计算最优折扣方案。

### 7.2 功能规范

#### 7.2.1 年龄折扣
**功能描述**：根据用餐者年龄提供相应折扣

**折扣规则**：
- 60-69岁：9.5折
- 70-79岁：9折
- 80岁以上：8.5折
- 需要身份证明验证

**验证方式**：
- 身份证扫描
- 老年卡识别
- 手动输入确认
- 人脸年龄估算（辅助）

#### 7.2.2 会员折扣
**功能描述**：基于会员等级的折扣优惠

**折扣策略**：
- 普通会员：无折扣
- 银卡会员：9.5折
- 金卡会员：9折
- 钻石会员：8.5折

**叠加规则**：
- 会员折扣与年龄折扣可叠加
- 选择最优惠方案
- 特殊商品除外
- 活动期间特殊规则

#### 7.2.3 促销活动
**功能描述**：支持各种促销活动的折扣计算

**活动类型**：
- 满减活动（满100减10）
- 折扣活动（8.8折）
- 买赠活动（买二送一）
- 套餐优惠
- 限时特价

**计算逻辑**：
- 自动匹配适用活动
- 计算最优组合
- 显示优惠明细
- 排除互斥活动

#### 7.2.4 优惠券使用
**功能描述**：支持各种优惠券的使用和验证

**券种类型**：
- 代金券
- 折扣券
- 满减券
- 品类券
- 新用户券

**使用流程**：
1. 输入或扫描优惠券码
2. 验证优惠券有效性
3. 检查使用条件
4. 应用优惠额度
5. 标记券已使用

### 7.3 计算优先级
1. 商品原价
2. 会员折扣
3. 年龄折扣
4. 促销活动
5. 优惠券抵扣
6. 积分抵扣

## 8. 退款管理模块

### 8.1 模块概述
退款管理模块提供完整的退款处理流程和记录管理。

### 8.2 功能规范

#### 8.2.1 退款类型
**全额退款**：
- 适用场景：订单取消、商品质量问题
- 退款金额：订单全额
- 处理时效：即时处理

**部分退款**：
- 适用场景：部分商品退回
- 退款金额：按商品比例计算
- 处理方式：重新计算优惠

#### 8.2.2 退款流程
**操作步骤**：
1. 查找原始订单
2. 选择退款商品
3. 确认退款金额
4. 选择退款方式
5. 执行退款操作
6. 生成退款凭证
7. 更新库存和积分

**权限控制**：
- 普通收银员：限额内退款
- 主管确认：超限额退款
- 密码验证：关键操作
- 操作日志：完整记录

#### 8.2.3 退款方式
**现金退款**：
- 直接现金退还
- 登记退款记录
- 现金盘点调整

**原路返回**：
- 银行卡：POS退款
- 微信/支付宝：原路退回
- 储值卡：余额恢复

**储值卡充值**：
- 退款转入储值卡
- 提供充值凭证
- 余额实时更新

#### 8.2.4 退款凭证
**凭证内容**：
- 退款单号
- 原订单信息
- 退款商品清单
- 退款金额明细
- 退款方式
- 操作员信息
- 退款时间

### 8.3 异常处理
- 网络异常：离线记录，恢复后同步
- 支付异常：人工处理，特殊标记
- 系统异常：备份恢复，日志追踪

## 9. 订单查询模块

### 9.1 模块概述
订单查询模块提供灵活的订单检索和详情查看功能。

### 9.2 功能规范

#### 9.2.1 查询方式
**订单号查询**：
- 完整订单号输入
- 模糊订单号匹配
- 二维码扫描查询

**时间范围查询**：
- 今日订单
- 本周订单
- 本月订单
- 自定义时间段

**客户信息查询**：
- 会员号查询
- 手机号查询
- 姓名模糊查询

#### 9.2.2 查询结果
**列表显示**：
- 分页展示结果
- 关键信息摘要
- 支持排序筛选
- 快速操作按钮

**详情信息**：
- 完整订单信息
- 商品清单
- 支付信息
- 优惠明细
- 操作历史

#### 9.2.3 数据导出
**导出格式**：
- Excel格式
- CSV格式
- PDF格式

**导出内容**：
- 订单汇总
- 商品明细
- 支付统计
- 自定义字段

### 9.3 性能优化
- 索引优化查询速度
- 缓存热点数据
- 分页减少内存占用
- 异步加载提升响应

## 10. 充值管理模块

### 10.1 模块概述
充值管理模块为储值卡和会员账户提供充值服务。

### 10.2 功能规范

#### 10.2.1 充值方式
**现金充值**：
- 收取现金
- 确认充值金额
- 更新账户余额
- 提供充值凭证

**移动支付充值**：
- 扫码支付
- 实时到账
- 自动更新余额
- 电子凭证

#### 10.2.2 充值规则
**充值限制**：
- 单次充值限额：1000元
- 日充值限额：5000元
- 账户余额上限：10000元

**优惠政策**：
- 充100送5
- 充500送30
- 充1000送80
- 会员额外优惠

#### 10.2.3 充值记录
**记录内容**：
- 充值时间
- 充值金额
- 支付方式
- 优惠金额
- 操作员
- 余额变动

### 10.3 安全控制
- 操作权限验证
- 金额二次确认
- 异常操作警报
- 资金流水监控

## 11. 对账功能模块

### 11.1 模块概述
对账功能模块提供财务数据统计和对账报表生成功能。

### 11.2 功能规范

#### 11.2.1 日报表
**统计内容**：
- 交易笔数和金额
- 支付方式分类统计
- 退款统计
- 优惠统计
- 现金流水

**报表格式**：
- 汇总数据展示
- 明细数据导出
- 图表可视化
- 异常数据标记

#### 11.2.2 月报表
**报表内容**：
- 月度销售汇总
- 趋势分析
- 同比环比分析
- 客户分析
- 商品销售排行

#### 11.2.3 实时监控
**监控指标**：
- 实时交易金额
- 支付成功率
- 系统响应时间
- 异常交易统计

### 11.3 数据准确性
- 数据校验机制
- 异常数据检测
- 手工调账功能
- 审计日志记录

## 12. 广告展示模块

### 12.1 模块概述
广告展示模块基于后台管理策略展示相关广告内容。

### 12.2 功能规范

#### 12.2.1 广告类型
**静态广告**：
- 图片轮播
- 文字公告
- 促销信息

**动态广告**：
- 视频播放
- 动画效果
- 互动广告

#### 12.2.2 展示策略
**展示位置**：
- 主界面横幅
- 侧边栏广告
- 等待界面全屏
- 小票底部

**展示规则**：
- 时间段控制
- 客户群体定向
- 消费金额触发
- 商品关联推荐

#### 12.2.3 内容管理
**内容更新**：
- 远程内容推送
- 本地缓存机制
- 定时同步更新
- 离线内容保障

### 12.3 用户体验
- 不干扰主要操作
- 可跳过或关闭
- 内容相关性高
- 加载速度快

## 13. 系统集成规范

### 13.1 模块间交互
各功能模块通过统一的数据接口和事件机制进行交互，确保数据一致性和操作流畅性。

### 13.2 数据流转
- 实时数据同步
- 异步消息传递
- 事务一致性保证
- 错误恢复机制

### 13.3 异常处理
- 统一异常处理框架
- 用户友好错误提示
- 详细错误日志记录
- 自动恢复机制 