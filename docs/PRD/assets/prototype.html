<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>助老订餐收银系统 - 保真原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background-color: #FAFAFA;
            font-size: 18px;
            line-height: 1.5;
        }

        /* 系统状态栏 */
        .status-bar {
            height: 60px;
            background: linear-gradient(135deg, #1976D2, #1565C0);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 30px;
            font-size: 16px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 20px;
            font-size: 16px;
        }

        .network-status {
            color: #4CAF50;
        }

        /* 主容器 */
        .main-container {
            display: flex;
            height: calc(100vh - 180px);
            gap: 2px;
        }

        /* 订单列表区 */
        .order-section {
            width: 30%;
            background: white;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .order-header {
            font-size: 24px;
            font-weight: bold;
            color: #212121;
            margin-bottom: 20px;
            border-bottom: 2px solid #1976D2;
            padding-bottom: 10px;
        }

        .order-items {
            flex: 1;
            overflow-y: auto;
        }

        .order-item {
            background: #F5F5F5;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 12px;
            border-left: 4px solid #1976D2;
        }

        .item-name {
            font-size: 20px;
            font-weight: bold;
            color: #212121;
            margin-bottom: 8px;
        }

        .item-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #757575;
            font-size: 18px;
        }

        .item-price {
            color: #F44336;
            font-weight: bold;
            font-size: 20px;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quantity-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: #1976D2;
            color: white;
            border-radius: 4px;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quantity-btn:hover {
            background: #1565C0;
        }

        /* 会员信息 */
        .member-info {
            background: linear-gradient(135deg, #FFC107, #FF8F00);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .member-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .member-details {
            font-size: 16px;
            display: flex;
            justify-content: space-between;
        }

        /* 订单总计 */
        .order-summary {
            background: #E3F2FD;
            padding: 20px;
            border-radius: 8px;
            margin-top: auto;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 18px;
        }

        .summary-total {
            border-top: 2px solid #1976D2;
            padding-top: 10px;
            font-size: 24px;
            font-weight: bold;
            color: #F44336;
        }

        /* AI识别区域 */
        .ai-section {
            width: 40%;
            background: white;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        /* 菜品展示区 */
        .menu-section {
            width: 30%;
            background: white;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        /* 分类导航 */
        .category-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid #E0E0E0;
            padding-bottom: 15px;
        }

        .category-btn {
            padding: 10px 16px;
            border: 2px solid #E0E0E0;
            background: white;
            color: #757575;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s;
            flex: 1;
            min-width: 80px;
        }

        .category-btn.active {
            background: #1976D2;
            color: white;
            border-color: #1976D2;
        }

        .category-btn:hover {
            border-color: #1976D2;
            color: #1976D2;
        }

        /* 菜品网格 */
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 12px;
            max-height: 450px;
            overflow-y: auto;
            padding-right: 5px;
        }

        .menu-item {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .menu-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .menu-item-image {
            width: 100%;
            height: 80px;
            background: linear-gradient(135deg, #FF8A65, #FF7043);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .menu-item-info {
            padding: 8px;
            text-align: center;
        }

        .menu-item-name {
            font-size: 14px;
            font-weight: bold;
            color: #212121;
            margin-bottom: 4px;
        }

        .menu-item-price {
            font-size: 16px;
            font-weight: bold;
            color: #F44336;
            margin-bottom: 6px;
        }

        .add-btn {
            width: 100%;
            padding: 6px;
            border: none;
            background: #1976D2;
            color: white;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        /* AI识别区域重新设计 */
        .ai-header {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #1976D2;
            margin-bottom: 20px;
            border-bottom: 2px solid #1976D2;
            padding-bottom: 10px;
        }

        .ai-status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #E3F2FD;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 16px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
        }

        .camera-preview {
            width: 100%;
            height: 280px;
            background: linear-gradient(135deg, #BBDEFB, #90CAF9);
            border: 3px solid #1976D2;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #1976D2;
            margin-bottom: 20px;
            position: relative;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .camera-icon {
            font-size: 64px;
            margin-bottom: 15px;
        }

        .camera-status {
            font-size: 18px;
            text-align: center;
            font-weight: bold;
        }

        .camera-guide {
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 14px;
            color: #1565C0;
            text-align: center;
        }

        .recognition-result {
            background: #F5F5F5;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            border-left: 4px solid #4CAF50;
        }

        .result-header {
            font-size: 18px;
            font-weight: bold;
            color: #212121;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .result-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            padding: 12px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .result-icon {
            font-size: 32px;
        }

        .result-info {
            flex: 1;
        }

        .result-name {
            font-size: 18px;
            font-weight: bold;
            color: #212121;
            margin-bottom: 4px;
        }

        .result-confidence {
            font-size: 14px;
            color: #757575;
        }

        .confidence-bar {
            width: 100%;
            height: 6px;
            background: #E0E0E0;
            border-radius: 3px;
            margin-top: 4px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            border-radius: 3px;
        }

        .result-price {
            font-size: 16px;
            font-weight: bold;
            color: #F44336;
        }

        .ai-controls {
            display: flex;
            gap: 12px;
            margin-top: auto;
        }

        .ai-btn {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #1976D2;
            background: white;
            color: #1976D2;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }

        .ai-btn.primary {
            background: #1976D2;
            color: white;
        }

        .ai-btn.success {
            background: #4CAF50;
            border-color: #4CAF50;
            color: white;
        }

        .ai-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* 识别历史记录样式 */
        .recognition-history {
            background: #F5F5F5;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            max-height: 200px;
            overflow-y: auto;
        }

        .history-header {
            font-size: 18px;
            font-weight: bold;
            color: #212121;
            margin-bottom: 12px;
            border-bottom: 1px solid #E0E0E0;
            padding-bottom: 8px;
        }

        .history-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .history-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            animation: slideIn 0.3s ease-out;
        }

        .history-icon {
            font-size: 24px;
        }

        .history-info {
            flex: 1;
        }

        .history-name {
            font-size: 16px;
            font-weight: bold;
            color: #212121;
        }

        .history-time {
            font-size: 12px;
            color: #757575;
        }

        .history-price {
            font-size: 14px;
            font-weight: bold;
            color: #4CAF50;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* 自动识别状态样式 */
        .ai-section.auto-mode .camera-preview {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #C8E6C9, #A5D6A7);
        }

        .ai-section.auto-mode .status-dot {
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        /* 操作控制区 */
        .control-section {
            height: 120px;
            background: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
        }

        .payment-methods {
            display: flex;
            gap: 15px;
        }

        .payment-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            padding: 15px 20px;
            border: 2px solid #E0E0E0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 80px;
        }

        .payment-btn:hover {
            border-color: #1976D2;
            background: #E3F2FD;
        }

        .payment-btn.active {
            border-color: #1976D2;
            background: #1976D2;
            color: white;
        }

        .payment-icon {
            font-size: 24px;
        }

        .payment-text {
            font-size: 16px;
            font-weight: bold;
        }

        .main-controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .checkout-btn {
            background: linear-gradient(135deg, #4CAF50, #388E3C);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 20px 40px;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            transition: all 0.2s;
        }

        .checkout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.3);
        }

        .secondary-btn {
            padding: 15px 25px;
            border: 2px solid #1976D2;
            background: white;
            color: #1976D2;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }

        .secondary-btn:hover {
            background: #1976D2;
            color: white;
        }

        /* 响应式设计 */
        @media (max-width: 1366px) {
            .main-container {
                height: calc(100vh - 180px);
            }
            
            .order-section {
                width: 25%;
            }
            
            .ai-section {
                width: 40%;
            }
            
            .menu-section {
                width: 35%;
            }
            
            .menu-grid {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            }
        }

        @media (max-width: 1024px) {
            .main-container {
                flex-direction: column;
                height: auto;
            }
            
            .order-section {
                width: 100%;
                order: 3;
            }
            
            .ai-section {
                width: 100%;
                order: 1;
            }
            
            .menu-section {
                width: 100%;
                order: 2;
            }
            
            .camera-preview {
                height: 200px;
            }
            
            .menu-grid {
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
                max-height: 300px;
            }
        }

        @media (max-width: 768px) {
            .control-section {
                flex-direction: column;
                height: auto;
                gap: 15px;
                padding: 15px;
            }
            
            .payment-methods {
                order: 2;
                justify-content: center;
            }
            
            .main-controls {
                order: 1;
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .camera-preview {
                height: 180px;
            }
            
            .camera-icon {
                font-size: 48px;
            }
        }

        /* 动画效果 */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .scanning {
            animation: pulse 1.5s infinite;
        }

        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- 系统状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <span>🏪 助老订餐收银系统</span>
            <span>📅 2023年12月15日 14:30</span>
            <span>👤 收银员：张三</span>
        </div>
        <div class="status-right">
            <span class="network-status">🌐 网络正常</span>
            <span>🔔 无新通知</span>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 订单列表区 -->
        <div class="order-section">
            <div class="order-header">📋 当前订单</div>
            
            <div class="order-items">
                <div class="order-item fade-in">
                    <div class="item-name">🍗 宫保鸡丁</div>
                    <div class="item-details">
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="changeQuantity(this, -1)">-</button>
                            <span class="quantity">1</span>
                            <button class="quantity-btn" onclick="changeQuantity(this, 1)">+</button>
                        </div>
                        <div class="item-price">￥18.00</div>
                    </div>
                </div>

                <div class="order-item fade-in">
                    <div class="item-name">🍚 白米饭</div>
                    <div class="item-details">
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="changeQuantity(this, -1)">-</button>
                            <span class="quantity">1</span>
                            <button class="quantity-btn" onclick="changeQuantity(this, 1)">+</button>
                        </div>
                        <div class="item-price">￥3.00</div>
                    </div>
                </div>
            </div>

            <!-- 会员信息 -->
            <div class="member-info">
                <div class="member-name">👤 李老师（金卡会员）</div>
                <div class="member-details">
                    <span>💳 卡号：88888888</span>
                    <span>⭐ 积分：2580</span>
                </div>
            </div>

            <!-- 订单总计 -->
            <div class="order-summary">
                <div class="summary-row">
                    <span>原价：</span>
                    <span id="original-price">￥21.00</span>
                </div>
                <div class="summary-row">
                    <span>会员折扣：</span>
                    <span style="color: #4CAF50">-￥1.89</span>
                </div>
                <div class="summary-row summary-total">
                    <span>实付金额：</span>
                    <span id="final-price">￥19.11</span>
                </div>
            </div>
        </div>

        <!-- AI识别区域（独立40%宽度） -->
        <div class="ai-section">
            <div class="ai-header">🤖 AI智能扫菜识别</div>
            
            <!-- AI状态栏 -->
            <div class="ai-status-bar">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span id="ai-status">识别状态：连续识别中</span>
                </div>
                <div class="status-indicator">
                    <span>🌡️ 光线：良好</span>
                </div>
            </div>
            
            <!-- 摄像头预览区 -->
            <div class="camera-preview" id="camera-preview">
                <div class="camera-icon">📸</div>
                <div class="camera-status" id="camera-status">🔄 正在扫描...</div>
                <div class="camera-guide">💡 自动识别：检测到菜品将自动添加到订单</div>
            </div>

            <!-- 识别历史记录 -->
            <div class="recognition-history" id="recognition-history">
                <div class="history-header">📝 识别记录</div>
                <div class="history-list" id="history-list">
                    <div class="history-item">
                        <span class="history-icon">🍗</span>
                        <div class="history-info">
                            <div class="history-name">宫保鸡丁</div>
                            <div class="history-time">14:28 自动添加</div>
                        </div>
                        <div class="history-price">￥18.00</div>
                    </div>
                </div>
            </div>

            <!-- AI控制按钮 -->
            <div class="ai-controls">
                <button class="ai-btn success" id="toggle-recognition" onclick="toggleRecognition()">⏸️ 暂停识别</button>
                <button class="ai-btn" onclick="clearHistory()">🗑️ 清空记录</button>
                <button class="ai-btn" onclick="showAISettings()">⚙️ 设置</button>
            </div>
        </div>

        <!-- 菜品展示区 -->
        <div class="menu-section">
            <!-- 分类导航 -->
            <div class="category-nav">
                <button class="category-btn active" onclick="selectCategory(this, 'hot')">🔥 热菜</button>
                <button class="category-btn" onclick="selectCategory(this, 'cold')">🥗 凉菜</button>
                <button class="category-btn" onclick="selectCategory(this, 'soup')">🍲 汤类</button>
                <button class="category-btn" onclick="selectCategory(this, 'staple')">🍚 主食</button>
            </div>

            <!-- 菜品网格 -->
            <div class="menu-grid">
                <div class="menu-item" onclick="addToOrder('宫保鸡丁', 18.00, '🍗')">
                    <div class="menu-item-image">🍗</div>
                    <div class="menu-item-info">
                        <div class="menu-item-name">宫保鸡丁</div>
                        <div class="menu-item-price">￥18.00</div>
                        <button class="add-btn">+ 添加</button>
                    </div>
                </div>

                <div class="menu-item" onclick="addToOrder('红烧肉', 22.00, '🥩')">
                    <div class="menu-item-image">🥩</div>
                    <div class="menu-item-info">
                        <div class="menu-item-name">红烧肉</div>
                        <div class="menu-item-price">￥22.00</div>
                        <button class="add-btn">+ 添加</button>
                    </div>
                </div>

                <div class="menu-item" onclick="addToOrder('糖醋里脊', 20.00, '🍖')">
                    <div class="menu-item-image">🍖</div>
                    <div class="menu-item-info">
                        <div class="menu-item-name">糖醋里脊</div>
                        <div class="menu-item-price">￥20.00</div>
                        <button class="add-btn">+ 添加</button>
                    </div>
                </div>

                <div class="menu-item" onclick="addToOrder('麻婆豆腐', 15.00, '🍲')">
                    <div class="menu-item-image">🍲</div>
                    <div class="menu-item-info">
                        <div class="menu-item-name">麻婆豆腐</div>
                        <div class="menu-item-price">￥15.00</div>
                        <button class="add-btn">+ 添加</button>
                    </div>
                </div>

                <div class="menu-item" onclick="addToOrder('鱼香肉丝', 19.00, '🐟')">
                    <div class="menu-item-image">🐟</div>
                    <div class="menu-item-info">
                        <div class="menu-item-name">鱼香肉丝</div>
                        <div class="menu-item-price">￥19.00</div>
                        <button class="add-btn">+ 添加</button>
                    </div>
                </div>

                <div class="menu-item" onclick="addToOrder('蒜蓉菜心', 12.00, '🥬')">
                    <div class="menu-item-image">🥬</div>
                    <div class="menu-item-info">
                        <div class="menu-item-name">蒜蓉菜心</div>
                        <div class="menu-item-price">￥12.00</div>
                        <button class="add-btn">+ 添加</button>
                    </div>
                </div>

                <div class="menu-item" onclick="addToOrder('白米饭', 3.00, '🍚')">
                    <div class="menu-item-image">🍚</div>
                    <div class="menu-item-info">
                        <div class="menu-item-name">白米饭</div>
                        <div class="menu-item-price">￥3.00</div>
                        <button class="add-btn">+ 添加</button>
                    </div>
                </div>

                <div class="menu-item" onclick="addToOrder('紫菜蛋花汤', 8.00, '🍜')">
                    <div class="menu-item-image">🍜</div>
                    <div class="menu-item-info">
                        <div class="menu-item-name">紫菜蛋花汤</div>
                        <div class="menu-item-price">￥8.00</div>
                        <button class="add-btn">+ 添加</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作控制区 -->
    <div class="control-section">
        <div class="payment-methods">
            <div class="payment-btn active" onclick="selectPayment(this, 'cash')">
                <div class="payment-icon">💰</div>
                <div class="payment-text">现金</div>
            </div>
            <div class="payment-btn" onclick="selectPayment(this, 'wechat')">
                <div class="payment-icon">📱</div>
                <div class="payment-text">微信</div>
            </div>
            <div class="payment-btn" onclick="selectPayment(this, 'alipay')">
                <div class="payment-icon">📱</div>
                <div class="payment-text">支付宝</div>
            </div>
            <div class="payment-btn" onclick="selectPayment(this, 'card')">
                <div class="payment-icon">💳</div>
                <div class="payment-text">储值卡</div>
            </div>
        </div>

        <div class="main-controls">
            <button class="secondary-btn" onclick="showMemberDialog()">👤 会员</button>
            <button class="secondary-btn" onclick="showOrderHistory()">📋 订单</button>
            <button class="checkout-btn" onclick="checkout()">💳 结算 ￥19.11</button>
            <button class="secondary-btn" onclick="showSettings()">⚙️ 设置</button>
        </div>
    </div>

    <script>
        // 全局变量
        let orderItems = [
            { name: '宫保鸡丁', price: 18.00, quantity: 1, icon: '🍗' },
            { name: '白米饭', price: 3.00, quantity: 1, icon: '🍚' }
        ];
        let selectedPayment = 'cash';
        let isRecognizing = true; // 默认开启自动识别
        let recognitionInterval = null;
        let recognitionHistory = [];

        // 模拟菜品数据库
        const dishDatabase = [
            { name: '宫保鸡丁', price: 18.00, icon: '🍗' },
            { name: '红烧肉', price: 22.00, icon: '🥩' },
            { name: '糖醋里脊', price: 20.00, icon: '🍖' },
            { name: '麻婆豆腐', price: 15.00, icon: '🍲' },
            { name: '鱼香肉丝', price: 19.00, icon: '🐟' },
            { name: '蒜蓉菜心', price: 12.00, icon: '🥬' },
            { name: '白米饭', price: 3.00, icon: '🍚' },
            { name: '紫菜蛋花汤', price: 8.00, icon: '🍜' }
        ];

        // 开始自动识别
        function startAutoRecognition() {
            if (recognitionInterval) return;
            
            const aiSection = document.querySelector('.ai-section');
            aiSection.classList.add('auto-mode');
            
            recognitionInterval = setInterval(() => {
                if (isRecognizing) {
                    simulateRecognition();
                }
            }, 3000); // 每3秒尝试识别一次
            
            document.getElementById('ai-status').textContent = '识别状态：连续识别中';
            document.getElementById('camera-status').textContent = '🔄 正在扫描...';
        }

        // 停止自动识别
        function stopAutoRecognition() {
            if (recognitionInterval) {
                clearInterval(recognitionInterval);
                recognitionInterval = null;
            }
            
            const aiSection = document.querySelector('.ai-section');
            aiSection.classList.remove('auto-mode');
            
            document.getElementById('ai-status').textContent = '识别状态：已暂停';
            document.getElementById('camera-status').textContent = '已暂停，点击开始识别';
        }

        // 模拟AI识别过程
        function simulateRecognition() {
            // 随机选择一个菜品进行识别
            const randomDish = dishDatabase[Math.floor(Math.random() * dishDatabase.length)];
            const confidence = Math.floor(Math.random() * 30) + 70; // 70-99%的置信度
            
            // 只有置信度>=85%才自动添加
            if (confidence >= 85) {
                // 自动添加到订单
                addToOrder(randomDish.name, randomDish.price, randomDish.icon);
                
                // 添加到识别历史
                addToHistory(randomDish.name, randomDish.price, randomDish.icon, confidence);
                
                // 显示识别成功的视觉反馈
                showRecognitionFeedback(randomDish.name, true);
            } else {
                // 置信度不够，不添加
                showRecognitionFeedback(randomDish.name, false);
            }
        }

        // 显示识别反馈
        function showRecognitionFeedback(dishName, success) {
            const status = document.getElementById('camera-status');
            const preview = document.getElementById('camera-preview');
            
            if (success) {
                status.textContent = `✅ 识别成功：${dishName}，已自动添加`;
                preview.style.borderColor = '#4CAF50';
            } else {
                status.textContent = `⚠️ 识别中：${dishName}，置信度不足`;
                preview.style.borderColor = '#FFC107';
            }
            
            // 2秒后恢复默认状态
            setTimeout(() => {
                status.textContent = '🔄 正在扫描...';
                preview.style.borderColor = isRecognizing ? '#4CAF50' : '#1976D2';
            }, 2000);
        }

        // 添加到识别历史
        function addToHistory(name, price, icon, confidence) {
            const now = new Date();
            const timeStr = now.getHours().toString().padStart(2, '0') + ':' + 
                           now.getMinutes().toString().padStart(2, '0');
            
            const historyItem = {
                name, price, icon, confidence, time: timeStr
            };
            
            recognitionHistory.unshift(historyItem); // 添加到开头
            
            // 只保留最近10条记录
            if (recognitionHistory.length > 10) {
                recognitionHistory = recognitionHistory.slice(0, 10);
            }
            
            updateHistoryDisplay();
        }

        // 更新历史记录显示
        function updateHistoryDisplay() {
            const historyList = document.getElementById('history-list');
            historyList.innerHTML = '';
            
            recognitionHistory.forEach(item => {
                const historyElement = document.createElement('div');
                historyElement.className = 'history-item';
                historyElement.innerHTML = `
                    <span class="history-icon">${item.icon}</span>
                    <div class="history-info">
                        <div class="history-name">${item.name}</div>
                        <div class="history-time">${item.time} 自动添加 (${item.confidence}%)</div>
                    </div>
                    <div class="history-price">￥${item.price.toFixed(2)}</div>
                `;
                historyList.appendChild(historyElement);
            });
        }

        // AI识别功能
        function toggleRecognition() {
            if (isRecognizing) {
                isRecognizing = false;
                stopAutoRecognition();
                document.getElementById('toggle-recognition').textContent = '🔄 开始识别';
                document.getElementById('toggle-recognition').classList.remove('success');
            } else {
                isRecognizing = true;
                startAutoRecognition();
                document.getElementById('toggle-recognition').textContent = '⏸️ 暂停识别';
                document.getElementById('toggle-recognition').classList.add('success');
            }
        }

        function clearHistory() {
            recognitionHistory = [];
            updateHistoryDisplay();
        }

        function showAISettings() {
            alert('AI识别设置：\n- 识别模式：自动连续识别\n- 识别间隔：3秒\n- 自动添加阈值：85%\n- 历史记录：保留10条');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateOrderSummary();
            startAutoRecognition(); // 页面加载后自动开始识别
            console.log('助老订餐收银系统已加载 - AI自动识别模式已启动');
        });

        // 分类选择
        function selectCategory(button, category) {
            document.querySelectorAll('.category-btn').forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            // 这里可以添加切换菜品显示的逻辑
            console.log('选择分类:', category);
        }

        // 支付方式选择
        function selectPayment(button, method) {
            document.querySelectorAll('.payment-btn').forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            selectedPayment = method;
            console.log('选择支付方式:', method);
        }

        // 数量调整
        function changeQuantity(button, delta) {
            const item = button.closest('.order-item');
            const quantitySpan = item.querySelector('.quantity');
            let quantity = parseInt(quantitySpan.textContent) + delta;
            
            if (quantity <= 0) {
                // 删除商品
                item.remove();
                updateOrderSummary();
                return;
            }
            
            quantitySpan.textContent = quantity;
            updateOrderSummary();
        }

        // 添加商品到订单
        function addToOrder(name, price, icon) {
            const existingItem = orderItems.find(item => item.name === name);
            
            if (existingItem) {
                existingItem.quantity++;
            } else {
                orderItems.push({ name, price, quantity: 1, icon });
            }
            
            refreshOrderDisplay();
            updateOrderSummary();
            
            // 显示添加动画
            console.log(`添加商品: ${name} ￥${price}`);
        }

        // 刷新订单显示
        function refreshOrderDisplay() {
            const orderItemsContainer = document.querySelector('.order-items');
            orderItemsContainer.innerHTML = '';
            
            orderItems.forEach(item => {
                const itemElement = document.createElement('div');
                itemElement.className = 'order-item fade-in';
                itemElement.innerHTML = `
                    <div class="item-name">${item.icon} ${item.name}</div>
                    <div class="item-details">
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="changeQuantity(this, -1)">-</button>
                            <span class="quantity">${item.quantity}</span>
                            <button class="quantity-btn" onclick="changeQuantity(this, 1)">+</button>
                        </div>
                        <div class="item-price">￥${(item.price * item.quantity).toFixed(2)}</div>
                    </div>
                `;
                orderItemsContainer.appendChild(itemElement);
            });
        }

        // 更新订单总计
        function updateOrderSummary() {
            const total = orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discount = total * 0.1; // 会员9折
            const finalPrice = total - discount;
            
            document.getElementById('original-price').textContent = `￥${total.toFixed(2)}`;
            document.getElementById('final-price').textContent = `￥${finalPrice.toFixed(2)}`;
            document.querySelector('.checkout-btn').textContent = `💳 结算 ￥${finalPrice.toFixed(2)}`;
        }

        // 主要功能
        function checkout() {
            if (orderItems.length === 0) {
                alert('请先添加商品到订单');
                return;
            }
            
            const finalPrice = document.getElementById('final-price').textContent;
            if (confirm(`确认结算 ${finalPrice}？\n支付方式：${getPaymentName(selectedPayment)}`)) {
                alert('支付成功！');
                // 清空订单
                orderItems = [];
                refreshOrderDisplay();
                updateOrderSummary();
            }
        }

        function getPaymentName(method) {
            const names = {
                'cash': '现金',
                'wechat': '微信支付',
                'alipay': '支付宝',
                'card': '储值卡'
            };
            return names[method] || '未知';
        }

        function showMemberDialog() {
            alert('会员功能：\n- 身份证查询\n- 会员卡扫描\n- 手机号查询');
        }

        function showOrderHistory() {
            alert('订单查询功能：\n- 今日订单\n- 历史订单\n- 退款处理');
        }

        function showSettings() {
            alert('系统设置：\n- 显示设置\n- 声音设置\n- 网络设置\n- 系统信息');
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'F1':
                    e.preventDefault();
                    alert('帮助信息：\nF1-帮助 F2-搜索 F3-会员 F4-支付\nEnter-结算 Esc-取消');
                    break;
                case 'F2':
                    e.preventDefault();
                    const search = prompt('请输入商品名称：');
                    if (search) console.log('搜索:', search);
                    break;
                case 'F3':
                    e.preventDefault();
                    showMemberDialog();
                    break;
                case 'F4':
                    e.preventDefault();
                    alert('支付方式选择');
                    break;
                case 'Enter':
                    e.preventDefault();
                    checkout();
                    break;
                case 'Escape':
                    e.preventDefault();
                    if (confirm('确认取消当前操作？')) {
                        toggleRecognition();
                    }
                    break;
            }
        });
    </script>
</body>
</html> 