# 界面原型和设计稿说明

## 1. 主界面设计

### 1.1 收银台主界面（新布局 - AI识别区≥40%）
```
系统状态栏：时间 | 收银员：张三 | 网络：正常 | 通知：无
-------------------------------------------------------------------------
|     订单列表区域        |    AI扫菜识别区域     |   菜品展示区域   |
|        (30%)           |       (40%)          |     (30%)       |
|                        |                      |                 |
| 宫保鸡丁    ￥18.00     | 📸 AI智能识别        | [热菜] [凉菜]    |
| 数量：1 小计：￥18.00    |                      | [汤类] [主食]    |
|                        | ┌─────────────────┐  |                 |
| 白米饭      ￥3.00      | │                 │  | [图] [图] [图]  |
| 数量：1 小计：￥3.00     | │   摄像头实时画面  │  | 宫保  红烧  糖醋 |
|                        | │                 │  | 鸡丁  肉    里脊 |
| 会员：李老师（金卡）      | │  请将菜品放入    │  |                 |
| 积分：2580分            | │    识别区域      │  | [图] [图] [图]  |
|                        | │                 │  | 麻婆  鱼香  蒜蓉 |
| 原价：￥21.00           | └─────────────────┘  | 豆腐  肉丝  菜心 |
| 会员折扣：-￥1.89        |                      |                 |
| 实付：￥19.11           | 识别状态：准备就绪    | [更多菜品]       |
|                        |                      |                 |
|                        | [📷 开始识别]         | [手动添加]       |
-------------------------------------------------------------------------
|                          操作控制区域                               |
| [现金] [微信] [支付宝] [储值卡] | [结算￥19.11] [会员] [设置] [清空] |
-------------------------------------------------------------------------
```

### 1.2 AI识别状态界面
```
AI识别进行中：
-------------------------------------------------------------------------
|     订单列表区域        |    AI扫菜识别区域     |   识别结果区域   |
|        (30%)           |       (40%)          |     (30%)       |
|                        |                      |                 |
| 宫保鸡丁    ￥18.00     | 🔍 AI识别中...       | 📊 识别结果      |
| 数量：1 小计：￥18.00    |                      |                 |
|                        | ┌─────────────────┐  | ✅ 红烧肉        |
| 白米饭      ￥3.00      | │  🎯 识别框架     │  | 置信度：92%     |
| 数量：1 小计：￥3.00     | │                 │  | ￥28.00        |
|                        | │ [检测到的菜品]   │  | [✓ 确认添加]    |
| 会员：李老师（金卡）      | │                 │  |                 |
| 积分：2580分            | │  正在分析...     │  | ⚠️ 糖醋里脊      |
|                        | │                 │  | 置信度：68%     |
| 原价：￥21.00           | └─────────────────┘  | ￥22.00        |
| 会员折扣：-￥1.89        |                      | [? 待确认]      |
| 实付：￥19.11           | 识别进度: ████▒▒▒   |                 |
|                        | 75%                  | [🔄 重新识别]    |
|                        |                      |                 |
|                        | [⏸️ 暂停] [❌ 取消]    | [📝 手动选择]    |
-------------------------------------------------------------------------
|                          操作控制区域                               |
| [现金] [微信] [支付宝] [储值卡] | [结算￥19.11] [会员] [设置] [清空] |
-------------------------------------------------------------------------
```

### 1.3 支付界面设计
```
支付确认界面
-------------------------------------------------------------------------
|                         支付方式选择界面                           |
|                                                                   |
| ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐                   |
| │ [现金]   │ │ [银行卡] │ │ [微信]   │ │[支付宝] │                   |
| │   💰    │ │   💳    │ │   📱    │ │   📱    │                   |
| │ 大字体   │ │ 大字体   │ │ 大字体   │ │ 大字体   │                   |
| └─────────┘ └─────────┘ └─────────┘ └─────────┘                   |
|                                                                   |
| ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐                   |
| │ [储值卡] │ │ [老年卡] │ │ [积分]   │ │ [组合]   │                   |
| │   💾    │ │   🎫    │ │   ⭐    │ │   🔄    │                   |
| │ 大字体   │ │ 大字体   │ │ 大字体   │ │ 大字体   │                   |
| └─────────┘ └─────────┘ └─────────┘ └─────────┘                   |
|                                                                   |
|                应付金额：￥19.11（大字体显示）                      |
|              [确认支付]  [取消]  （超大按钮）                      |
-------------------------------------------------------------------------
```

## 2. 移动端适配设计

### 2.1 手机界面布局（AI识别优化）
```
助老订餐收银系统 - 移动端
┌───────────────────────────────────┐
│ 时间：14:30  收银员：张三  [设置]   │
├───────────────────────────────────┤
│           AI扫菜识别               │
│ ┌─────────────────────────────┐   │
│ │                             │   │
│ │      摄像头实时画面          │   │
│ │                             │   │
│ │    请将菜品放入识别区        │   │
│ └─────────────────────────────┘   │
│ [📷 开始识别] [📝 手动添加]       │
├───────────────────────────────────┤
│           订单信息                 │
│ ┌─────────────────────────────┐   │
│ │ 宫保鸡丁  ￥18.00  数量：1   │   │
│ │ 白米饭    ￥3.00   数量：1   │   │
│ │ 会员：李老师（金卡）          │   │
│ │ 实付：￥19.11               │   │
│ └─────────────────────────────┘   │
├───────────────────────────────────┤
│ [菜品分类]  [支付￥19.11]  [会员]   │
└───────────────────────────────────┘
```

### 2.2 平板界面布局（AI识别优化）
```
平板界面（横屏）- AI识别区域≥40%
┌─────────────────────────────────────────────────────────────────┐
│ 助老订餐收银系统    时间：14:30    收银员：张三    [设置]          │
├──────────────────┬──────────────────────────┬─────────────────────┤
│     订单列表       │      AI扫菜识别区域       │    菜品快速选择     │
│      (25%)        │         (45%)           │       (30%)        │
│                  │                          │                    │
│ 宫保鸡丁  ￥18.00  │ 📸 AI智能菜品识别        │ [热菜] [凉菜]       │
│ 数量：1           │                          │ [汤类] [主食]       │
│ 小计：￥18.00     │ ┌────────────────────┐   │                    │
│                  │ │                    │   │ ┌────┐ ┌────┐     │
│ 白米饭    ￥3.00  │ │   高清摄像头画面    │   │ │宫保 │ │红烧 │     │
│ 数量：1           │ │                    │   │ │鸡丁 │ │肉   │     │
│ 小计：￥3.00      │ │ 实时检测菜品轮廓    │   │ │￥18│ │￥22 │     │
│                  │ │                    │   │ └────┘ └────┘     │
│ 会员：李老师       │ │ 识别置信度：89%     │   │                    │
│ 金卡会员          │ └────────────────────┘   │ ┌────┐ ┌────┐     │
│ 积分：2580        │                          │ │糖醋 │ │麻婆 │     │
│                  │ 检测结果：红烧肉          │ │里脊 │ │豆腐 │     │
│ 原价：￥21.00     │ 置信度：89% ✓            │ │￥20│ │￥15 │     │
│ 优惠：￥1.89      │                          │ └────┘ └────┘     │
│ 实付：￥19.11     │ [✅ 确认添加] [🔄 重试]   │                    │
│                  │ [📝 手动选择] [❌ 取消]   │ [查看更多]          │
├──────────────────┴──────────────────────────┴─────────────────────┤
│ [现金] [微信] [支付宝] [储值卡] [老年卡]  [结算￥19.11] [会员管理] │
└─────────────────────────────────────────────────────────────────┘
```

## 3. AI识别专用组件设计

### 3.1 AI识别控制面板
```
AI识别控制面板：
┌─────────────────────────────────┐
│ 📸 AI菜品识别系统               │
│                                 │
│ 当前状态：● 准备就绪             │ ← 绿色指示灯
│ 识别模式：🎯 高精度模式           │
│ 光线条件：☀️ 良好                │
│                                 │
│ ┌─────────────────────────────┐ │
│ │        摄像头预览区域        │ │
│ │                             │ │
│ │     [将菜品放入此区域]       │ │
│ │                             │ │
│ │    💡 提示：保持光线充足     │ │
│ └─────────────────────────────┘ │
│                                 │
│ [🔍 开始识别] [⚙️ 设置] [❓ 帮助] │
└─────────────────────────────────┘
```

### 3.2 识别结果展示组件
```
识别结果展示：
┌─────────────────────────────────┐
│ 🎯 识别结果 (1.2秒)             │
│                                 │
│ ✅ 红烧肉                       │
│ 置信度：██████████ 92%          │ ← 绿色进度条
│ 标准价：￥28.00                  │
│ [➕ 添加到订单]                  │
│                                 │
│ ⚠️ 可能是：糖醋里脊              │
│ 置信度：██████▒▒▒▒ 68%          │ ← 橙色进度条
│ 标准价：￥22.00                  │
│ [🤔 不确定，手动选择]            │
│                                 │
│ [🔄 重新识别] [📝 手动输入]      │
└─────────────────────────────────┘
```

### 3.3 AI识别设置面板
```
AI识别设置：
┌─────────────────────────────────┐
│ ⚙️ AI识别设置                   │
│                                 │
│ 识别模式：                       │
│ ○ 快速模式 (1-2秒，准确率85%)    │
│ ● 精确模式 (2-4秒，准确率92%)    │ ← 默认选择
│ ○ 专业模式 (3-5秒，准确率95%)    │
│                                 │
│ 识别灵敏度：                     │
│ [低] ──●────── [高]             │ ← 滑块控制
│                                 │
│ 自动添加阈值：                   │
│ 置信度 ≥ [90]% 自动添加          │
│                                 │
│ ☑️ 启用连续识别模式              │
│ ☑️ 识别成功后语音提示            │
│ ☐ 识别失败自动重试              │
│                                 │
│ [💾 保存设置] [🔄 恢复默认]      │
└─────────────────────────────────┘
```

## 4. 状态设计优化

### 4.1 AI识别状态指示
```
识别状态指示器：
准备状态：● 绿色圆点 + "准备就绪"
识别中：🔄 旋转图标 + "识别中..."
成功状态：✅ 绿色勾号 + "识别成功"
失败状态：❌ 红色叉号 + "识别失败"
暂停状态：⏸️ 暂停图标 + "已暂停"
```

### 4.2 识别进度展示
```
识别进度条：
┌─────────────────────────────────┐
│ 🔍 正在分析菜品特征...           │
│ ████████▒▒▒▒▒▒▒▒ 45%           │
│                                 │
│ 预计剩余时间：1.2秒              │
└─────────────────────────────────┘
```

## 5. 布局响应式设计

### 5.1 超宽屏幕适配 (≥1920px)
```
超宽屏布局 - AI识别区域40%
┌─────────────────────────────────────────────────────────────────────────┐
│         订单列表区域           │      AI扫菜识别区域      │    菜品展示区域    │
│           (25%)               │        (40%)            │      (35%)        │
│                               │                         │                   │
│ 订单详情...                   │ AI识别界面（更大显示）   │ 菜品网格（更多列）  │
└─────────────────────────────────────────────────────────────────────────┘
```

### 5.2 小屏幕适配 (<1366px)
```
小屏幕布局 - 保持AI识别区域40%
┌─────────────────────────────────────────────────────┐
│     订单列表        │    AI识别区域    │   菜品区域   │
│      (25%)         │     (40%)       │    (35%)    │
│                    │                 │             │
│ 精简订单显示        │ 适配小尺寸       │ 减少列数    │
└─────────────────────────────────────────────────────┘
```

这个修改确保了AI扫菜识别区域在所有屏幕尺寸下都占据不少于40%的宽度，为AI识别功能提供了充足的展示空间。 