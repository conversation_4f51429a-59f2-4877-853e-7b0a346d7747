# 助老订餐收银系统 - UI/UX设计规范

## 1. 设计原则与理念

### 1.1 核心设计原则

#### 1.1.1 适老化优先
- **大字体设计**：所有文字≥18px，重要信息≥24px
- **高对比度**：确保视力不佳的老年用户能清晰识别
- **简化操作**：减少操作步骤，避免复杂的多级菜单
- **容错性强**：提供充足的撤销和纠错机制

#### 1.1.2 一致性原则
- **布局一致性**：统一的页面结构和组件位置
- **交互一致性**：相同功能采用相同的操作方式
- **视觉一致性**：统一的色彩、字体、图标系统

#### 1.1.3 可访问性设计
- **语音辅助**：关键操作提供语音提示
- **触屏友好**：按钮尺寸适合手指操作
- **键盘支持**：完整的键盘快捷键体系

### 1.2 设计目标
- 降低学习成本，老年用户5分钟内掌握基本操作
- 减少操作错误，错误率控制在5%以内
- 提升操作效率，单笔交易时间≤60秒
- 增强用户信心，提供清晰的操作反馈

## 2. 整体布局规范

### 2.1 主界面布局

#### 2.1.1 主界面三区域布局（AI识别优化版）
```
┌─────────────────────────────────────────────────┐
│  系统状态栏                                      │
├─────────────────┬───────────────────────────────┤
│                 │                               │
│   订单列表区     │        菜品展示区              │
│                 │                               │
│   (左侧30%)     │       (中央40%)              │
│                 │                               │
│                 │                               │
├─────────────────┴───────────────────────────────┤
│               操作控制区                         │
└─────────────────────────────────────────────────┘
```

#### 2.1.2 区域功能定义
**系统状态栏（高度：60px）**
- 当前时间和日期
- 收银员信息
- 网络状态
- 系统通知

**订单列表区（左侧30%）**
- 当前购物车商品
- 订单总计信息
- 会员信息（如有）
- 优惠信息

**菜品展示区（中央40%）**
- 菜品分类导航
- 菜品网格展示
- AI扫菜识别区域
- 快捷操作按钮

**操作控制区（高度：120px）**
- 支付方式选择
- 结算按钮
- 功能快捷键
- 系统设置入口

### 2.2 响应式设计

#### 2.2.1 屏幕尺寸适配
**大屏显示器（1920x1080及以上）**
- 采用三区域布局
- 菜品显示6x4网格
- 字体大小：标准18px

**中等屏幕（1366x768-1920x1080）**
- 保持三区域布局
- 菜品显示5x3网格
- 字体大小：放大至20px

**平板设备（768x1024）**
- 调整为上下布局
- 菜品显示4x3网格
- 字体大小：22px

**手机设备（375x667及以上）**
- 采用单页面布局
- 菜品显示2x4网格
- 字体大小：24px

#### 2.2.2 自适应规则
- 文字大小按屏幕尺寸等比缩放
- 按钮尺寸保持最小44x44px（Apple标准）
- 间距采用百分比布局
- 图片支持多分辨率适配

#### 响应式布局适配
| 屏幕尺寸 | 订单列表 | AI识别 | 菜品展示 | 布局方式 |
|---------|---------|--------|----------|----------|
| 超宽屏(≥1920px) | 25% | 40% | 35% | 三列水平 |
| 标准屏(1366-1919px) | 30% | 40% | 30% | 三列水平 |
| 小屏幕(1024-1365px) | 25% | 40% | 35% | 三列紧凑 |
| 平板横屏(768-1023px) | 25% | 45% | 30% | 三列垂直滚动 |
| 手机(≤767px) | 100% | 50%高度 | 100% | 垂直堆叠 |

#### AI识别区域详细设计
- **最小宽度保证**：不少于40%屏幕宽度
- **摄像头预览**：占识别区域80%空间
- **控制按钮**：占识别区域20%空间
- **实时状态**：顶部状态条显示
- **识别结果**：右侧悬浮显示或底部展示

## 3. 视觉设计规范

### 3.1 色彩系统

#### 3.1.1 主色调定义
**主色（Primary）**
- 色值：#1976D2（蓝色）
- 用途：主要按钮、链接、强调元素
- 辅助色：#BBDEFB（浅蓝）、#0D47A1（深蓝）

**辅助色（Secondary）**
- 色值：#FFC107（橙黄）
- 用途：警告提示、特殊状态、促销信息
- 辅助色：#FFF3C4（浅黄）、#FF8F00（深橙）

**成功色（Success）**
- 色值：#4CAF50（绿色）
- 用途：成功提示、确认操作、正常状态

**错误色（Error）**
- 色值：#F44336（红色）
- 用途：错误提示、删除操作、异常状态

**中性色（Neutral）**
- 主要文字：#212121（深灰）
- 次要文字：#757575（中灰）
- 边框线条：#E0E0E0（浅灰）
- 背景色：#FAFAFA（白灰）

#### 3.1.2 对比度要求
- 文字与背景对比度≥4.5:1（WCAG AA标准）
- 重要信息对比度≥7:1（WCAG AAA标准）
- 按钮与背景对比度≥3:1
- 边框与背景对比度≥3:1

### 3.2 字体规范

#### 3.2.1 字体选择
**Windows平台**
- 主字体：Microsoft YaHei（微软雅黑）
- 备选字体：SimHei（黑体）
- 数字字体：Arial

**Android平台**
- 主字体：Noto Sans CJK SC
- 备选字体：DroidSans Fallback
- 数字字体：Roboto

#### 3.2.2 字号层级
**标题层级**
- H1：32px，加粗，用于页面标题
- H2：28px，加粗，用于模块标题
- H3：24px，加粗，用于区块标题

**正文层级**
- 主要文字：20px，常规，用于重要信息
- 次要文字：18px，常规，用于一般信息
- 说明文字：16px，常规，用于辅助说明

**特殊字号**
- 金额数字：24px，加粗，红色显示
- 按钮文字：20px，加粗
- 状态文字：18px，常规

#### 3.2.3 行高规范
- 标题行高：1.2倍字号
- 正文行高：1.5倍字号
- 按钮行高：1.4倍字号

### 3.3 图标系统

#### 3.3.1 图标风格
- 采用线性图标风格
- 线条粗细：2px
- 圆角：2px
- 填充：根据状态决定

#### 3.3.2 图标尺寸
- 小图标：16x16px
- 标准图标：24x24px
- 大图标：32x32px
- 特大图标：48x48px

#### 3.3.3 核心图标定义
- 购物车：shopping_cart
- 支付：payment
- 会员：person
- 设置：settings
- 搜索：search
- 删除：delete
- 确认：check_circle
- 取消：cancel
- 帮助：help
- 退出：exit_to_app

## 4. 组件设计规范

### 4.1 按钮组件

#### 4.1.1 主要按钮（Primary Button）
**视觉样式**
- 背景色：#1976D2
- 文字色：#FFFFFF
- 边框：无
- 圆角：8px
- 阴影：0 2px 4px rgba(0,0,0,0.2)

**尺寸规格**
- 小按钮：120x40px
- 标准按钮：160x48px
- 大按钮：200x56px
- 全宽按钮：100%x56px

**状态变化**
- 悬停：背景色变深10%
- 按下：背景色变深20%
- 禁用：背景色#BDBDBD，文字色#757575

#### 4.1.2 次要按钮（Secondary Button）
**视觉样式**
- 背景色：透明
- 文字色：#1976D2
- 边框：2px solid #1976D2
- 圆角：8px

**状态变化**
- 悬停：背景色#E3F2FD
- 按下：背景色#BBDEFB
- 禁用：边框色#BDBDBD，文字色#BDBDBD

#### 4.1.3 危险按钮（Danger Button）
**视觉样式**
- 背景色：#F44336
- 文字色：#FFFFFF
- 其他样式同主要按钮

#### 4.1.4 按钮最佳实践
- 按钮文字简洁明确，≤6个汉字
- 重要按钮放在右侧或底部
- 相关按钮保持视觉上的连续性
- 提供足够的点击区域（≥44x44px）

### 4.2 输入框组件

#### 4.2.1 文本输入框
**视觉样式**
- 边框：2px solid #E0E0E0
- 圆角：4px
- 内边距：12px 16px
- 高度：48px
- 字体：20px

**状态变化**
- 聚焦：边框色#1976D2，阴影0 0 0 2px rgba(25,118,210,0.2)
- 错误：边框色#F44336
- 禁用：背景色#F5F5F5，文字色#BDBDBD

#### 4.2.2 标签设计
- 位置：输入框上方
- 字体：18px，颜色#757575
- 必填标记：红色星号*

#### 4.2.3 错误提示
- 位置：输入框下方
- 字体：16px，颜色#F44336
- 图标：error图标

### 4.3 列表组件

#### 4.3.1 商品列表项
**布局结构**
```
┌─────────────────────────────────────────┐
│ [图标] 商品名称              ￥价格      │
│       数量: X     小计: ￥XX.XX   [操作] │
└─────────────────────────────────────────┘
```

**样式规格**
- 高度：80px
- 内边距：16px
- 分隔线：1px solid #E0E0E0
- 悬停：背景色#F5F5F5

#### 4.3.2 菜品卡片
**布局结构**
```
┌─────────────────┐
│                 │
│     菜品图片     │
│                 │
├─────────────────┤
│   菜品名称       │
│   ￥价格        │
└─────────────────┘
```

**样式规格**
- 尺寸：160x200px
- 圆角：8px
- 阴影：0 2px 8px rgba(0,0,0,0.1)
- 图片比例：16:9

### 4.4 对话框组件

#### 4.4.1 模态对话框
**视觉样式**
- 背景：白色
- 圆角：12px
- 阴影：0 8px 24px rgba(0,0,0,0.3)
- 最小宽度：400px
- 最大宽度：80%屏宽

**结构组成**
- 标题区：36px高度，24px字体
- 内容区：可变高度，18px字体
- 操作区：56px高度，按钮右对齐

#### 4.4.2 确认对话框
**标准模板**
- 图标：根据类型选择（success/warning/error）
- 标题：简洁明确的操作描述
- 内容：详细的操作说明或后果
- 按钮：取消（左）+ 确认（右）

### 4.5 表单组件

#### 4.5.1 表单布局
- 标签在上，输入框在下
- 垂直间距：24px
- 必填项标记：红色星号
- 分组：使用分割线或背景色区分

#### 4.5.2 验证反馈
- 实时验证：失焦时触发
- 错误显示：红色边框+错误提示
- 成功显示：绿色边框+成功图标

## 5. 交互设计规范

### 5.1 操作反馈

#### 5.1.1 视觉反馈
**按钮交互**
- 悬停：颜色变化+轻微阴影
- 按下：颜色变深+阴影缩小
- 点击：涟漪效果（Material Design）

**状态指示**
- 加载：旋转图标+文字提示
- 成功：绿色对勾+成功提示
- 错误：红色叉号+错误说明

#### 5.1.2 动画效果
**过渡动画**
- 持续时间：200-300ms
- 缓动函数：ease-out
- 页面切换：fade/slide效果

**微交互**
- 按钮点击：100ms脉冲效果
- 卡片悬停：200ms上升效果
- 输入聚焦：150ms边框颜色过渡

#### 5.1.3 声音反馈
**操作提示音**
- 成功操作：轻快的提示音
- 错误操作：低沉的警告音
- 按键音：轻微的点击音
- 音量控制：可在设置中调节

### 5.2 手势操作

#### 5.2.1 触屏手势
**基础手势**
- 点击：选择/确认
- 长按：显示操作菜单
- 双击：快速操作
- 滑动：翻页/滚动

**特定手势**
- 向右滑动：删除列表项
- 向下滑动：刷新数据
- 双指缩放：放大/缩小内容

#### 5.2.2 键盘操作
**快捷键定义**
- F1：帮助
- F2：搜索商品
- F3：会员查询
- F4：支付方式
- F5：刷新
- Escape：取消/返回
- Enter：确认/下一步
- Delete：删除选中项

### 5.3 导航设计

#### 5.3.1 主导航
**导航结构**
- 首页（收银台）
- 商品管理
- 会员管理
- 订单查询
- 报表统计
- 系统设置

**导航样式**
- 位置：顶部横向排列
- 高度：48px
- 字体：18px
- 选中状态：底部蓝色下划线

#### 5.3.2 面包屑导航
**使用场景**
- 多级页面结构
- 设置页面导航
- 查询结果页面

**样式规格**
- 分隔符：>
- 字体：16px
- 颜色：#757575
- 当前页：#212121，不可点击

## 6. 特殊场景设计

### 6.1 AI扫菜识别界面

#### 6.1.1 摄像头预览区
**布局设计**
- 尺寸：320x240px
- 位置：菜品展示区右上角
- 边框：2px solid #1976D2
- 圆角：8px

**状态指示**
- 待机：显示"请将菜品放入识别区"
- 识别中：显示转圈加载动画
- 识别成功：绿色边框+成功提示
- 识别失败：红色边框+重试按钮

#### 6.1.2 识别结果展示
**结果卡片**
- 背景：白色卡片，阴影效果
- 布局：左侧图片+右侧信息
- 信息：菜品名称、价格、置信度
- 操作：确认添加/重新识别

### 6.2 支付界面设计

#### 6.2.1 支付方式选择
**布局设计**
- 网格布局：2x4排列
- 卡片尺寸：120x80px
- 图标尺寸：32x32px
- 文字：16px

**支付选项**
- 现金支付
- 银行卡
- 微信支付
- 支付宝
- 储值卡
- 老年卡
- 会员积分
- 组合支付

#### 6.2.2 支付进度指示
**步骤指示**
1. 选择支付方式
2. 确认支付金额
3. 执行支付操作
4. 支付完成确认

**进度条设计**
- 位置：支付区域顶部
- 样式：点状进度条
- 颜色：已完成（蓝色）、当前（橙色）、未完成（灰色）

### 6.3 错误处理界面

#### 6.3.1 网络错误
**错误页面**
- 图标：wifi_off图标
- 标题："网络连接失败"
- 说明："请检查网络连接并重试"
- 操作：重试按钮

#### 6.3.2 系统错误
**错误对话框**
- 图标：error图标
- 标题："系统异常"
- 说明：具体错误描述
- 操作：确定+联系技术支持

## 7. 无障碍设计

### 7.1 视觉辅助

#### 7.1.1 高对比度模式
**配色调整**
- 背景：纯黑#000000
- 文字：纯白#FFFFFF
- 按钮：高对比度色彩
- 边框：明显的分界线

#### 7.1.2 字体放大
**放大级别**
- 标准：18px基准
- 大字体：22px基准
- 超大字体：26px基准
- 最大字体：32px基准

### 7.2 操作辅助

#### 7.2.1 语音播报
**播报内容**
- 当前操作状态
- 金额信息
- 错误提示
- 操作指导

**播报设置**
- 语速：可调节（慢/中/快）
- 音量：独立音量控制
- 语音：支持方言选择

#### 7.2.2 操作简化
**一键操作**
- 快速结账
- 常用菜品
- 支付方式
- 会员查询

**容错设计**
- 操作撤销
- 误触保护
- 确认机制
- 帮助提示

## 8. 响应式适配

### 8.1 屏幕适配策略

#### 8.1.1 断点设计
**断点定义**
- 超小屏：< 576px
- 小屏：576px - 768px
- 中屏：768px - 992px
- 大屏：992px - 1200px
- 超大屏：> 1200px

#### 8.1.2 布局调整
**超小屏（手机）**
- 单列布局
- 全屏显示
- 简化导航
- 大按钮设计

**小屏（平板竖屏）**
- 双列布局
- 侧边导航
- 卡片式设计
- 手势优化

**中屏及以上（电脑/大屏）**
- 多列布局
- 顶部导航
- 丰富交互
- 键盘优化

### 8.2 性能优化

#### 8.2.1 图片优化
- 多分辨率适配
- 懒加载策略
- 压缩优化
- WebP格式支持

#### 8.2.2 动画优化
- GPU硬件加速
- 60fps流畅度
- 降级策略
- 性能监控

## 9. 品牌一致性

### 9.1 品牌元素

#### 9.1.1 Logo使用
**Logo规范**
- 最小尺寸：48x48px
- 清晰区域：Logo周围1倍高度
- 色彩：主色调或单色
- 位置：左上角或中心

#### 9.1.2 色彩应用
**品牌色彩**
- 主色：代表专业可靠
- 辅助色：代表温暖友好
- 中性色：保持简洁清晰

### 9.2 语言文字

#### 9.2.1 语言风格
**用词原则**
- 简洁明了
- 避免专业术语
- 使用生活化语言
- 积极正面表达

#### 9.2.2 错误信息
**友好提示**
- 避免技术术语
- 提供解决方案
- 保持礼貌语气
- 给出下一步指导

## 10. 开发指导

### 10.1 前端实现

#### 10.1.1 CSS规范
**命名约定**
- 使用BEM命名法
- 组件前缀：ui-
- 状态前缀：is-/has-
- 修饰符：--modifier

**代码组织**
- 变量定义：colors.scss
- 混合器：mixins.scss
- 组件样式：components/
- 工具类：utilities.scss

#### 10.1.2 组件库选择
**推荐框架**
- React：Ant Design + 自定义组件
- Vue：Element UI + 自定义组件
- Angular：Angular Material + 自定义组件

**自定义组件**
- 适老化按钮
- 大字体输入框
- 高对比度卡片
- 语音播报组件

### 10.2 设计资源

#### 10.2.1 设计文件
**Sketch/Figma资源**
- 组件库文件
- 图标库文件
- 设计规范文件
- 原型交互文件

#### 10.2.2 切图规范
**输出格式**
- 图标：SVG格式
- 插图：PNG格式
- 照片：JPG格式
- 动画：GIF/WebM格式

**命名规范**
- 功能_状态_尺寸.格式
- 例：button_primary_large.svg

这套UI/UX设计规范将确保助老订餐收银系统具有一致、友好、易用的用户界面，特别针对老年用户的需求进行了优化设计。 