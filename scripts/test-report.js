#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 生成测试报告
 */
function generateTestReport() {
  const reportDir = path.join(__dirname, '../test-reports');
  
  // 确保报告目录存在
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }

  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      coverage: {
        statements: 0,
        branches: 0,
        functions: 0,
        lines: 0
      }
    },
    testSuites: [],
    recommendations: []
  };

  // 读取Jest覆盖率报告
  const coverageFile = path.join(__dirname, '../coverage/coverage-summary.json');
  if (fs.existsSync(coverageFile)) {
    try {
      const coverage = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
      if (coverage.total) {
        report.summary.coverage = {
          statements: coverage.total.statements.pct,
          branches: coverage.total.branches.pct,
          functions: coverage.total.functions.pct,
          lines: coverage.total.lines.pct
        };
      }
    } catch (error) {
      console.warn('无法读取覆盖率报告:', error.message);
    }
  }

  // 生成建议
  const { coverage } = report.summary;
  if (coverage.statements < 80) {
    report.recommendations.push('语句覆盖率低于80%，建议增加更多单元测试');
  }
  if (coverage.branches < 80) {
    report.recommendations.push('分支覆盖率低于80%，建议增加边界条件测试');
  }
  if (coverage.functions < 80) {
    report.recommendations.push('函数覆盖率低于80%，建议测试更多函数');
  }

  // 适老化测试建议
  report.recommendations.push('建议进行手动适老化可用性测试');
  report.recommendations.push('建议使用屏幕阅读器测试可访问性');
  report.recommendations.push('建议在不同设备上测试响应式设计');

  // 保存报告
  const reportFile = path.join(reportDir, `test-report-${Date.now()}.json`);
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

  // 生成HTML报告
  const htmlReport = generateHtmlReport(report);
  const htmlFile = path.join(reportDir, `test-report-${Date.now()}.html`);
  fs.writeFileSync(htmlFile, htmlReport);

  console.log('测试报告已生成:');
  console.log('JSON报告:', reportFile);
  console.log('HTML报告:', htmlFile);

  return report;
}

/**
 * 生成HTML格式的测试报告
 */
function generateHtmlReport(report) {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>助老订餐收银系统 - 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        .coverage { display: flex; gap: 20px; margin: 20px 0; }
        .coverage-item { 
            background: #fff; 
            border: 1px solid #ddd; 
            padding: 15px; 
            border-radius: 8px; 
            text-align: center;
            flex: 1;
        }
        .coverage-value { font-size: 24px; font-weight: bold; }
        .good { color: #52c41a; }
        .warning { color: #faad14; }
        .error { color: #f5222d; }
        .recommendations { background: #fff7e6; padding: 15px; border-radius: 8px; }
        .recommendations ul { margin: 0; padding-left: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>助老订餐收银系统 - 测试报告</h1>
        <p>生成时间: ${report.timestamp}</p>
    </div>

    <h2>测试覆盖率</h2>
    <div class="coverage">
        <div class="coverage-item">
            <div class="coverage-value ${getCoverageClass(report.summary.coverage.statements)}">${report.summary.coverage.statements}%</div>
            <div>语句覆盖率</div>
        </div>
        <div class="coverage-item">
            <div class="coverage-value ${getCoverageClass(report.summary.coverage.branches)}">${report.summary.coverage.branches}%</div>
            <div>分支覆盖率</div>
        </div>
        <div class="coverage-item">
            <div class="coverage-value ${getCoverageClass(report.summary.coverage.functions)}">${report.summary.coverage.functions}%</div>
            <div>函数覆盖率</div>
        </div>
        <div class="coverage-item">
            <div class="coverage-value ${getCoverageClass(report.summary.coverage.lines)}">${report.summary.coverage.lines}%</div>
            <div>行覆盖率</div>
        </div>
    </div>

    <h2>改进建议</h2>
    <div class="recommendations">
        <ul>
            ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
        </ul>
    </div>

    <h2>测试框架配置</h2>
    <ul>
        <li>✅ Jest 单元测试框架已配置</li>
        <li>✅ React Testing Library 已配置</li>
        <li>✅ 测试覆盖率报告已启用</li>
        <li>⚠️ Playwright E2E测试需要安装依赖</li>
        <li>⚠️ 性能测试需要配置Lighthouse CI</li>
    </ul>
</body>
</html>
  `;
}

function getCoverageClass(value) {
  if (value >= 80) return 'good';
  if (value >= 60) return 'warning';
  return 'error';
}

// 如果直接运行此脚本
if (require.main === module) {
  generateTestReport();
}

module.exports = { generateTestReport }; 