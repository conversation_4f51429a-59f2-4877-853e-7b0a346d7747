# 助老订餐收银系统

基于React18 + TypeScript + Electron的跨平台收银系统，专为老年人友好设计。

## 📋 项目特色

- 🎯 **适老化设计**：大字体、高对比度、简化操作
- 🤖 **AI智能识别**：本地AI菜品识别，自动添加到订单
- 📱 **跨平台支持**：Windows桌面端 + Android移动端
- 🔄 **实时同步**：订单、会员、财务数据实时同步
- 💳 **多种支付**：现金、微信、支付宝、储值卡
- 📊 **智能报表**：销售统计、财务分析、营业报表

## 🛠️ 技术栈

### 前端核心
- **React 18** - 用户界面框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design 5.x** - 适老化定制UI组件
- **Redux Toolkit** - 状态管理
- **React Router 6** - 路由管理

### 跨平台方案
- **Electron 19.x** - Windows桌面端
- **Capacitor 4.x** - Android移动端
- **代码复用率 85%+**

### AI识别
- **TensorFlow.js 4.x** - 本地AI推理
- **MobileNetV2** - 轻量级识别模型
- **85-92%识别准确率**

### 开发工具
- **Webpack 5** - 模块打包
- **ESLint + Prettier** - 代码规范
- **Jest + RTL** - 测试框架
- **Husky + lint-staged** - Git提交钩子

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0
- Python 3.8+ (TensorFlow.js构建)

### 安装依赖
```bash
npm install
```

### 开发环境启动
```bash
# 启动React开发服务器
npm start

# 启动Electron桌面应用
npm run electron-dev
```

### 构建打包
```bash
# 构建Web版本
npm run build

# 打包Windows桌面版
npm run build-win

# 打包所有平台
npm run dist
```

## 📁 项目结构

```
src/
├── components/          # 公共组件
│   ├── Layout/         # 布局组件
│   ├── AI/             # AI识别组件
│   ├── Order/          # 订单组件
│   └── Payment/        # 支付组件
├── pages/              # 页面组件
│   ├── CashierPage/    # 收银台主页
│   ├── MemberPage/     # 会员管理
│   └── SettingsPage/   # 系统设置
├── store/              # 状态管理
│   ├── slices/         # Redux切片
│   └── index.ts        # Store配置
├── services/           # API服务
│   ├── api.ts          # API基础配置
│   ├── order.ts        # 订单API
│   └── member.ts       # 会员API
├── utils/              # 工具函数
├── types/              # TypeScript类型
├── assets/             # 静态资源
└── hooks/              # 自定义Hooks
```

## 🎨 UI设计规范

### 布局比例
- **订单列表区域**：30%宽度
- **AI识别区域**：40%宽度 ⭐
- **菜品展示区域**：30%宽度

### 适老化标准
- **最小字体**：18px
- **按钮高度**：≥48px
- **点击目标**：≥44px
- **颜色对比度**：≥4.5:1
- **操作步骤**：最多3步完成

### 主色调
- **主色**：#1976D2 (蓝色)
- **辅色**：#FFC107 (橙色)
- **成功**：#4CAF50 (绿色)
- **警告**：#FF9800 (橙色)
- **错误**：#F44336 (红色)

## 🤖 AI识别功能

### 技术实现
- **本地推理**：TensorFlow.js离线运行
- **模型架构**：MobileNetV2 + 自定义分类层
- **输入尺寸**：224x224x3 RGB图像
- **识别类别**：100-300种常见菜品

### 性能指标
- **识别准确率**：85-92%
- **响应时间**：1-3秒
- **自动添加阈值**：85%置信度
- **连续识别**：每3秒扫描一次

## 📱 跨平台适配

### Windows桌面端
- **最小分辨率**：1024x768
- **推荐分辨率**：1440x900
- **安装包大小**：约150MB
- **系统要求**：Windows 10+

### Android移动端
- **最小分辨率**：720x1280
- **推荐分辨率**：1080x1920
- **APK大小**：约50MB
- **系统要求**：Android 8.0+

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
npm test

# 运行覆盖率测试
npm run test:coverage

# 运行E2E测试
npm run test:e2e
```

### 测试覆盖率目标
- **单元测试覆盖率**：≥85%
- **集成测试覆盖率**：≥75%
- **E2E测试覆盖率**：≥60%

## 📊 性能优化

### Bundle优化
- **代码分割**：按路由和功能拆分
- **懒加载**：非核心组件懒加载
- **Tree Shaking**：移除未使用代码
- **压缩优化**：Gzip + Brotli压缩

### 运行时优化
- **虚拟滚动**：大列表性能优化
- **图片懒加载**：减少初始加载时间
- **缓存策略**：API结果缓存
- **内存管理**：组件卸载清理

## 🔧 开发规范

### 代码规范
- **命名规范**：PascalCase组件，camelCase函数
- **文件命名**：kebab-case文件名
- **注释规范**：JSDoc注释格式
- **提交规范**：Conventional Commits

### Git工作流
```bash
# 功能开发
git checkout -b feature/ai-recognition
git commit -m "feat: add ai recognition module"

# 修复bug
git checkout -b fix/order-calculation
git commit -m "fix: correct order total calculation"
```

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✅ 项目脚手架搭建完成
- ✅ 基础开发环境配置
- ✅ TypeScript + React18集成
- ✅ Electron桌面端配置
- ⏳ AI识别模块开发中...

## 👥 开发团队

- **项目负责人**：助老收银团队
- **技术架构**：React + TypeScript + Electron
- **设计理念**：适老化优先，用户体验至上

## 📄 许可证

MIT License - 详见 [LICENSE](./LICENSE) 文件

## 🤝 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目！

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

---

**让科技更好地服务老年人群体** 🌟
