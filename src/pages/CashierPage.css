/* 收银页面样式 - 高端现代化风格 */
.cashier-page {
  height: 100vh;
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #0f1419 100%);
  position: relative;

  /* 背景装饰 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(249, 115, 22, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }

  /* 布局结构 */
  .ant-layout-content {
    padding: 0;
    background: transparent;
  }

  /* 主容器 */
  .cashier-content {
    height: calc(100vh - 70px); /* 总高度减去状态栏70px */
    overflow: hidden;
    padding: 16px;

    .main-container {
      height: 100%;
      display: flex;
      gap: 16px;
      background: transparent;

      /* 左侧订单区域 (32%) */
      .order-section-container {
        width: 32%;
        background: rgba(15, 20, 25, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(59, 130, 246, 0.2);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
          0 20px 40px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }

      /* 中间操作区域 (36%) */
      .operation-section-container {
        width: 36%;
        background: rgba(15, 20, 25, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(249, 115, 22, 0.2);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
          0 20px 40px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }

      /* 右侧菜品区域 (32%) */
      .menu-section-container {
        width: 32%;
        background: rgba(15, 20, 25, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(34, 197, 94, 0.2);
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
          0 20px 40px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }
    }
  }
}

/* 专业收银机深色主题全局样式 */
.cashier-page * {
  color: #ffffff;
}

.cashier-page .ant-card {
  background: #2d2d2d;
  border: 1px solid #404040;
}

.cashier-page .ant-card-body {
  background: #2d2d2d;
}

.cashier-page .ant-divider {
  border-color: #404040;
}

.cashier-page .ant-badge-count {
  background: #ff6b35;
  color: #ffffff;
}

/* 适老化设计优化 */
/* 大字体模式 */
.elder-cashier-layout.elder-large-font .main-container .order-section-container,
.elder-cashier-layout.elder-large-font .main-container .operation-section-container,
.elder-cashier-layout.elder-large-font .main-container .menu-section-container {
  font-size: 22px;
}

/* 高对比度模式 */
.elder-cashier-layout.elder-high-contrast .main-container .order-section-container,
.elder-cashier-layout.elder-high-contrast .main-container .operation-section-container,
.elder-cashier-layout.elder-high-contrast .main-container .menu-section-container {
  border: 3px solid #ff6b35;
  background: #2d2d2d;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .cashier-page .main-container {
    gap: 0;
  }

  .cashier-page .main-container .order-section-container {
    width: 28%;
  }

  .cashier-page .main-container .operation-section-container {
    width: 44%;
  }

  .cashier-page .main-container .menu-section-container {
    width: 28%;
  }
}

@media (max-width: 768px) {
  .cashier-page .main-container {
    flex-direction: column;
  }

  .cashier-page .main-container .order-section-container,
  .cashier-page .main-container .operation-section-container,
  .cashier-page .main-container .menu-section-container {
    width: 100%;
    height: 33.33%;
  }
}