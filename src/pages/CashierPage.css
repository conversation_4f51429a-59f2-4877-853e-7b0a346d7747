/* 收银页面样式 - 极简现代化设计 */
.cashier-page {
  height: 100vh;
  background: #f8fafc;
  position: relative;

  /* 布局结构 */
  .ant-layout-content {
    padding: 0;
    background: #f8fafc;
  }

  /* 主容器 */
  .cashier-content {
    height: calc(100vh - 64px); /* 总高度减去状态栏64px */
    overflow: hidden;
    padding: 8px;

    .main-container {
      height: 100%;
      display: flex;
      gap: 8px;
      background: transparent;

      /* 左侧订单区域 (28%) */
      .order-section-container {
        width: 28%;
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        overflow: hidden;
        box-shadow:
          0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }

      /* 中间操作区域 (44%) */
      .operation-section-container {
        width: 44%;
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        overflow: hidden;
        box-shadow:
          0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }

      /* 右侧菜品区域 (28%) */
      .menu-section-container {
        width: 28%;
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        overflow: hidden;
        box-shadow:
          0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }
    }
  }
}

/* 现代化浅色主题全局样式 */
.cashier-page * {
  color: #1e293b;
}

.cashier-page .ant-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.cashier-page .ant-card-body {
  background: #ffffff;
}

.cashier-page .ant-divider {
  border-color: #e2e8f0;
}

.cashier-page .ant-badge-count {
  background: #3b82f6;
  color: #ffffff;
}

/* 适老化设计优化 */
/* 大字体模式 */
.elder-cashier-layout.elder-large-font .main-container .order-section-container,
.elder-cashier-layout.elder-large-font .main-container .operation-section-container,
.elder-cashier-layout.elder-large-font .main-container .menu-section-container {
  font-size: 22px;
}

/* 高对比度模式 */
.elder-cashier-layout.elder-high-contrast .main-container .order-section-container,
.elder-cashier-layout.elder-high-contrast .main-container .operation-section-container,
.elder-cashier-layout.elder-high-contrast .main-container .menu-section-container {
  border: 3px solid #ff6b35;
  background: #2d2d2d;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .cashier-page .main-container {
    gap: 0;
  }

  .cashier-page .main-container .order-section-container {
    width: 28%;
  }

  .cashier-page .main-container .operation-section-container {
    width: 44%;
  }

  .cashier-page .main-container .menu-section-container {
    width: 28%;
  }
}

@media (max-width: 768px) {
  .cashier-page .main-container {
    flex-direction: column;
  }

  .cashier-page .main-container .order-section-container,
  .cashier-page .main-container .operation-section-container,
  .cashier-page .main-container .menu-section-container {
    width: 100%;
    height: 33.33%;
  }
}