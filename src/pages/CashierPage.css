/* 收银页面样式 - 专业收银机风格 */
.cashier-page {
  height: 100vh;
  background: #1a1a1a;

  /* 布局结构 */
  .ant-layout-content {
    padding: 0;
    background: #1a1a1a;
  }

  /* 主容器 */
  .cashier-content {
    height: calc(100vh - 60px); /* 总高度减去状态栏60px */
    overflow: hidden;

    .main-container {
      height: 100%;
      display: flex;
      gap: 0;
      background: #1a1a1a;

      /* 左侧订单区域 (30%) */
      .order-section-container {
        width: 30%;
        background: #2d2d2d;
        border-right: 2px solid #404040;
        overflow: hidden;
      }

      /* 中间操作区域 (40%) */
      .operation-section-container {
        width: 40%;
        background: #1a1a1a;
        border-right: 2px solid #404040;
        overflow: hidden;
      }

      /* 右侧菜品区域 (30%) */
      .menu-section-container {
        width: 30%;
        background: #2d2d2d;
        overflow: hidden;
      }
    }
  }
}

/* 专业收银机深色主题全局样式 */
.cashier-page * {
  color: #ffffff;
}

.cashier-page .ant-card {
  background: #2d2d2d;
  border: 1px solid #404040;
}

.cashier-page .ant-card-body {
  background: #2d2d2d;
}

.cashier-page .ant-divider {
  border-color: #404040;
}

.cashier-page .ant-badge-count {
  background: #ff6b35;
  color: #ffffff;
}

/* 适老化设计优化 */
/* 大字体模式 */
.elder-cashier-layout.elder-large-font .main-container .order-section-container,
.elder-cashier-layout.elder-large-font .main-container .operation-section-container,
.elder-cashier-layout.elder-large-font .main-container .menu-section-container {
  font-size: 22px;
}

/* 高对比度模式 */
.elder-cashier-layout.elder-high-contrast .main-container .order-section-container,
.elder-cashier-layout.elder-high-contrast .main-container .operation-section-container,
.elder-cashier-layout.elder-high-contrast .main-container .menu-section-container {
  border: 3px solid #ff6b35;
  background: #2d2d2d;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .cashier-page .main-container {
    gap: 0;
  }

  .cashier-page .main-container .order-section-container {
    width: 28%;
  }

  .cashier-page .main-container .operation-section-container {
    width: 44%;
  }

  .cashier-page .main-container .menu-section-container {
    width: 28%;
  }
}

@media (max-width: 768px) {
  .cashier-page .main-container {
    flex-direction: column;
  }

  .cashier-page .main-container .order-section-container,
  .cashier-page .main-container .operation-section-container,
  .cashier-page .main-container .menu-section-container {
    width: 100%;
    height: 33.33%;
  }
}