/* 收银页面样式 - 1:1还原设计稿 */
.cashier-page {
  height: 100vh;
  background: #2c2c2c;
  position: relative;
  display: flex;

  /* 布局结构 */
  .ant-layout-content {
    padding: 0;
    background: #2c2c2c;
    flex: 1;
    display: flex;
  }

  /* 主容器 */
  .cashier-content {
    height: 100vh;
    overflow: hidden;
    padding: 0;
    flex: 1;
    display: flex;

    .main-container {
      height: 100%;
      display: flex;
      gap: 0;
      background: #2c2c2c;
      flex: 1;

      /* 左侧订单区域 */
      .order-section-container {
        width: 350px;
        background: #1e1e1e;
        border: none;
        border-radius: 0;
        overflow: hidden;
        box-shadow: none;
        border-right: 1px solid #404040;
      }

      /* 中间操作区域 */
      .operation-section-container {
        width: 400px;
        background: #2c2c2c;
        border: none;
        border-radius: 0;
        overflow: hidden;
        box-shadow: none;
        border-right: 1px solid #404040;
      }

      /* 右侧菜品区域 */
      .menu-section-container {
        flex: 1;
        background: #f5f5f5;
        border: none;
        border-radius: 0;
        overflow: hidden;
        box-shadow: none;
      }
    }
  }
}

/* 现代化浅色主题全局样式 */
.cashier-page * {
  color: #1e293b;
}

.cashier-page .ant-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.cashier-page .ant-card-body {
  background: #ffffff;
}

.cashier-page .ant-divider {
  border-color: #e2e8f0;
}

.cashier-page .ant-badge-count {
  background: #3b82f6;
  color: #ffffff;
}

/* 适老化设计优化 */
/* 大字体模式 */
.elder-cashier-layout.elder-large-font .main-container .order-section-container,
.elder-cashier-layout.elder-large-font .main-container .operation-section-container,
.elder-cashier-layout.elder-large-font .main-container .menu-section-container {
  font-size: 22px;
}

/* 高对比度模式 */
.elder-cashier-layout.elder-high-contrast .main-container .order-section-container,
.elder-cashier-layout.elder-high-contrast .main-container .operation-section-container,
.elder-cashier-layout.elder-high-contrast .main-container .menu-section-container {
  border: 3px solid #ff6b35;
  background: #2d2d2d;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .cashier-page .main-container {
    gap: 0;
  }

  .cashier-page .main-container .order-section-container {
    width: 28%;
  }

  .cashier-page .main-container .operation-section-container {
    width: 44%;
  }

  .cashier-page .main-container .menu-section-container {
    width: 28%;
  }
}

@media (max-width: 768px) {
  .cashier-page .main-container {
    flex-direction: column;
  }

  .cashier-page .main-container .order-section-container,
  .cashier-page .main-container .operation-section-container,
  .cashier-page .main-container .menu-section-container {
    width: 100%;
    height: 33.33%;
  }
}