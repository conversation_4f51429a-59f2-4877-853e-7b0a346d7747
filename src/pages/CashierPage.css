/* 收银页面样式 */
.cashier-page {
  height: 100vh;
  background: #FAFAFA;
  
  /* 布局结构 */
  .ant-layout-content {
    padding: 0;
    background: #FAFAFA;
  }
  
  /* 主容器 */
  .cashier-content {
    height: calc(100vh - 60px); /* 总高度减去状态栏60px */
    overflow: hidden;
    
    .main-container {
      height: 100%;
      display: flex;
      gap: 2px;
      background: #FAFAFA;
      
      /* 左侧订单区域 (35%) */
      .order-section-container {
        width: 35%;
        background: white;
        border-right: 1px solid #e8e8e8;
        overflow: hidden;
      }

      /* 中间操作区域 (30%) */
      .operation-section-container {
        width: 30%;
        background: white;
        border-right: 1px solid #e8e8e8;
        overflow: hidden;
      }

      /* 右侧菜品区域 (35%) */
      .menu-section-container {
        width: 35%;
        background: white;
        overflow: hidden;
      }
    }
  }
}

/* 适老化设计优化 */
/* 大字体模式 */
.elder-cashier-layout.elder-large-font .main-container .order-section-container,
.elder-cashier-layout.elder-large-font .main-container .operation-section-container,
.elder-cashier-layout.elder-large-font .main-container .menu-section-container {
  font-size: 20px;
}

/* 高对比度模式 */
.elder-cashier-layout.elder-high-contrast .main-container .order-section-container,
.elder-cashier-layout.elder-high-contrast .main-container .operation-section-container,
.elder-cashier-layout.elder-high-contrast .main-container .menu-section-container {
  border: 3px solid #1976D2;
  background: #FFFFFF;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .cashier-page .main-container {
    gap: 1px;
  }

  .cashier-page .main-container .order-section-container {
    width: 28%;
  }

  .cashier-page .main-container .operation-section-container {
    width: 44%;
  }

  .cashier-page .main-container .menu-section-container {
    width: 28%;
  }
}

@media (max-width: 768px) {
  .cashier-page .main-container {
    flex-direction: column;
  }

  .cashier-page .main-container .order-section-container,
  .cashier-page .main-container .operation-section-container,
  .cashier-page .main-container .menu-section-container {
    width: 100%;
    height: 33.33%;
  }
}