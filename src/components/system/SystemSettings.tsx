import React, { useState, useEffect, useCallback } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Switch, 
  Select, 
  TimePicker, 
  InputNumber, 
  Button, 
  message, 
  Tabs, 
  Space,
  Row,
  Col 
} from 'antd';
import { SaveOutlined, ReloadOutlined } from '@ant-design/icons';
import { systemService, type SystemSettings } from '../../services/systemService';
import './SystemSettings.less';

const { Option } = Select;
const { TabPane } = Tabs;

const SystemSettingsComponent: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [activeTab, setActiveTab] = useState('basic');

  // 加载系统设置
  const loadSettings = useCallback(async () => {
    try {
      setLoading(true);
      const data = await systemService.getSystemSettings();
      setSettings(data);
      form.setFieldsValue(data);
    } catch (error) {
      console.error('加载系统设置失败:', error);
      message.error('加载系统设置失败');
    } finally {
      setLoading(false);
    }
  }, [form]);

  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  const handleSave = async (values: any) => {
    try {
      setLoading(true);
      const updatedSettings = await systemService.updateSystemSettings(values);
      setSettings(updatedSettings);
      message.success('系统设置保存成功');
    } catch (error) {
      console.error('保存系统设置失败:', error);
      message.error('保存系统设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.resetFields();
    if (settings) {
      form.setFieldsValue(settings);
    }
  };

  return (
    <div className="system-settings">
      <Card 
        title="系统设置" 
        className="settings-card"
        extra={
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadSettings}
              loading={loading}
            >
              刷新
            </Button>
            <Button 
              type="primary" 
              icon={<SaveOutlined />} 
              onClick={() => form.submit()}
              loading={loading}
            >
              保存设置
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          className="settings-form"
        >
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            {/* 基本设置 */}
            <TabPane tab="基本设置" key="basic">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item
                    name="storeName"
                    label="店铺名称"
                    rules={[{ required: true, message: '请输入店铺名称' }]}
                  >
                    <Input placeholder="请输入店铺名称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="storePhone"
                    label="联系电话"
                    rules={[{ required: true, message: '请输入联系电话' }]}
                  >
                    <Input placeholder="请输入联系电话" />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item
                    name="storeAddress"
                    label="店铺地址"
                    rules={[{ required: true, message: '请输入店铺地址' }]}
                  >
                    <Input placeholder="请输入店铺地址" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['businessHours', 'open']}
                    label="营业开始时间"
                  >
                    <Input placeholder="08:00" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['businessHours', 'close']}
                    label="营业结束时间"
                  >
                    <Input placeholder="22:00" />
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>

            {/* 收银设置 */}
            <TabPane tab="收银设置" key="cashier">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item
                    name={['cashierSettings', 'autoLogout']}
                    label="自动登出"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['cashierSettings', 'autoLogoutTime']}
                    label="自动登出时间（分钟）"
                  >
                    <InputNumber min={5} max={120} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['cashierSettings', 'soundEnabled']}
                    label="声音提示"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['cashierSettings', 'printEnabled']}
                    label="自动打印小票"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item
                    name={['cashierSettings', 'receiptTemplate']}
                    label="小票模板"
                  >
                    <Select>
                      <Option value="default">默认模板</Option>
                      <Option value="simple">简洁模板</Option>
                      <Option value="detailed">详细模板</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>

            {/* 适老化设置 */}
            <TabPane tab="适老化设置" key="elderly">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item
                    name={['elderlyMode', 'enabled']}
                    label="启用适老化模式"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['elderlyMode', 'fontSize']}
                    label="字体大小"
                  >
                    <Select>
                      <Option value="normal">正常</Option>
                      <Option value="large">大字体</Option>
                      <Option value="extra-large">特大字体</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['elderlyMode', 'highContrast']}
                    label="高对比度"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['elderlyMode', 'simpleLayout']}
                    label="简化布局"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['elderlyMode', 'voicePrompt']}
                    label="语音提示"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>

            {/* AI设置 */}
            <TabPane tab="AI设置" key="ai">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item
                    name={['aiSettings', 'enabled']}
                    label="启用AI识别"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['aiSettings', 'autoAdd']}
                    label="自动添加到订单"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['aiSettings', 'confidence']}
                    label="识别置信度"
                  >
                    <InputNumber 
                      min={0.1} 
                      max={1} 
                      step={0.1} 
                      formatter={value => `${Math.round(Number(value) * 100)}%`}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['aiSettings', 'modelPath']}
                    label="模型路径"
                  >
                    <Input placeholder="/models/food-recognition.json" />
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>

            {/* 会员设置 */}
            <TabPane tab="会员设置" key="member">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item
                    name={['memberSettings', 'pointsEnabled']}
                    label="启用积分系统"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['memberSettings', 'pointsRatio']}
                    label="积分比例（1元=?积分）"
                  >
                    <InputNumber min={0.1} max={10} step={0.1} />
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>

            {/* 其他设置 */}
            <TabPane tab="其他设置" key="other">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item
                    name={['otherSettings', 'dataBackupEnabled']}
                    label="自动备份数据"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['otherSettings', 'backupFrequency']}
                    label="备份频率"
                  >
                    <Select>
                      <Option value="daily">每日</Option>
                      <Option value="weekly">每周</Option>
                      <Option value="monthly">每月</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['otherSettings', 'maxOrderHistory']}
                    label="订单历史保留天数"
                  >
                    <InputNumber min={7} max={365} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['otherSettings', 'debugMode']}
                    label="调试模式"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>
          </Tabs>

          <div className="form-actions">
            <Space>
              <Button onClick={handleReset}>重置</Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存设置
              </Button>
            </Space>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default SystemSettingsComponent; 