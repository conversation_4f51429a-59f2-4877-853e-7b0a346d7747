.system-settings {
  .settings-card {
    .ant-card-body {
      padding: 24px;
    }
  }

  .settings-form {
    .ant-tabs-tab {
      font-size: 16px;
      font-weight: 500;
    }

    .ant-tabs-content {
      padding-top: 24px;
    }

    .ant-form-item-label > label {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }

    .ant-input,
    .ant-select-selector,
    .ant-input-number {
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      
      &:hover {
        border-color: #40a9ff;
      }

      &:focus {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    .ant-switch {
      &.ant-switch-checked {
        background-color: #52c41a;
      }
    }

    .form-actions {
      margin-top: 32px;
      padding-top: 24px;
      border-top: 1px solid #f0f0f0;
      text-align: right;
    }
  }
}

// 适老化设计
.elderly-mode {
  .system-settings {
    .settings-form {
      .ant-tabs-tab {
        font-size: 18px;
        padding: 12px 20px;
      }

      .ant-form-item-label > label {
        font-size: 16px;
      }

      .ant-input,
      .ant-select-selector,
      .ant-input-number {
        height: 44px;
        font-size: 16px;
      }

      .ant-btn {
        height: 44px;
        font-size: 16px;
      }
    }
  }
}

// 高对比度模式
.high-contrast {
  .system-settings {
    .settings-card {
      background: #fff;
      border: 2px solid #000;
    }

    .settings-form {
      .ant-form-item-label > label {
        color: #000;
      }

      .ant-input,
      .ant-select-selector,
      .ant-input-number {
        border-color: #000;
        background: #fff;
        color: #000;

        &:hover, &:focus {
          border-color: #000;
          box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
        }
      }

      .ant-switch {
        &.ant-switch-checked {
          background-color: #000;
        }
      }
    }
  }
} 