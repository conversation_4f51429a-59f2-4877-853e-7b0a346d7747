// 购物车商品项样式
.cart-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
  
  &.updating {
    opacity: 0.7;
    pointer-events: none;
  }
  
  .item-image {
    flex: 0 0 64px;
    
    .ant-image {
      border-radius: 6px;
      overflow: hidden;
    }
    
    .image-placeholder {
      width: 64px;
      height: 64px;
      border-radius: 6px;
      background-color: #f5f5f5;
      border: 1px solid #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #bfbfbf;
      font-size: 24px;
    }
  }
  
  .item-info {
    flex: 1;
    min-width: 0;
    
    .item-name {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 4px;
      
      .elder-text {
        color: #262626;
      }
      
      .info-icon {
        color: #8c8c8c;
        cursor: help;
        
        &:hover {
          color: #1677ff;
        }
      }
    }
    
    .item-category {
      margin-bottom: 6px;
      
      .elder-text {
        color: #8c8c8c;
      }
    }
    
    .item-price {
      .price-with-discount {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .price-original {
          text-decoration: line-through;
          color: #bfbfbf !important;
        }
        
        .discount-badge {
          background-color: #ff4d4f;
          color: #ffffff;
          font-size: 10px;
          padding: 1px 4px;
          border-radius: 2px;
          font-weight: 500;
        }
      }
    }
  }
  
  .item-quantity {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    
    .quantity-controls {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .quantity-btn {
        width: 28px;
        height: 28px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        
        .anticon {
          font-size: 12px;
        }
      }
      
      .quantity-input {
        width: 50px;
        text-align: center;
        
        .ant-input-number-input {
          text-align: center;
          font-weight: 600;
          color: #262626;
        }
      }
    }
    
    .quantity-label {
      .elder-text {
        color: #8c8c8c;
        text-align: center;
      }
    }
  }
  
  .item-total {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    min-width: 80px;
    
    .total-price {
      .elder-text {
        color: #1677ff;
      }
    }
    
    .total-label {
      .elder-text {
        color: #8c8c8c;
      }
    }
  }
  
  .item-actions {
    flex: 0 0 auto;
    
    .remove-btn {
      width: 32px;
      height: 32px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      
      .anticon {
        font-size: 14px;
      }
    }
  }
  
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    
    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid #f0f0f0;
      border-top: 2px solid #1677ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 1200px) {
  .cart-item {
    gap: 10px;
    padding: 14px;
    
    .item-image {
      flex: 0 0 56px;
      
      .ant-image,
      .image-placeholder {
        width: 56px;
        height: 56px;
      }
      
      .image-placeholder {
        font-size: 20px;
      }
    }
    
    .item-info {
      .item-name {
        gap: 4px;
        margin-bottom: 3px;
        
        .elder-text {
          font-size: 14px;
        }
      }
      
      .item-category {
        margin-bottom: 4px;
        
        .elder-text {
          font-size: 12px;
        }
      }
      
      .item-price {
        .price-with-discount {
          gap: 6px;
          
          .discount-badge {
            font-size: 9px;
            padding: 1px 3px;
          }
        }
      }
    }
    
    .item-quantity {
      .quantity-controls {
        gap: 3px;
        
        .quantity-btn {
          width: 26px;
          height: 26px;
          
          .anticon {
            font-size: 11px;
          }
        }
        
        .quantity-input {
          width: 45px;
        }
      }
    }
    
    .item-total {
      min-width: 70px;
    }
    
    .item-actions {
      .remove-btn {
        width: 28px;
        height: 28px;
        
        .anticon {
          font-size: 12px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .cart-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px;
    
    .item-image {
      flex: 0 0 auto;
      align-self: center;
      
      .ant-image,
      .image-placeholder {
        width: 48px;
        height: 48px;
      }
      
      .image-placeholder {
        font-size: 18px;
      }
    }
    
    .item-info {
      text-align: center;
      
      .item-name {
        justify-content: center;
        margin-bottom: 2px;
        
        .elder-text {
          font-size: 13px;
        }
      }
      
      .item-category {
        margin-bottom: 3px;
        
        .elder-text {
          font-size: 11px;
        }
      }
      
      .item-price {
        .price-with-discount {
          justify-content: center;
          gap: 4px;
          
          .discount-badge {
            font-size: 8px;
          }
        }
      }
    }
    
    .item-quantity,
    .item-total {
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
    }
    
    .item-quantity {
      .quantity-controls {
        gap: 2px;
        
        .quantity-btn {
          width: 24px;
          height: 24px;
          
          .anticon {
            font-size: 10px;
          }
        }
        
        .quantity-input {
          width: 40px;
        }
      }
    }
    
    .item-actions {
      align-self: center;
      
      .remove-btn {
        width: 32px;
        height: 32px;
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .cart-item {
    border: 2px solid #000000;
    
    &:hover {
      border-color: #0050b3;
      box-shadow: 0 2px 8px rgba(0, 80, 179, 0.2);
    }
    
    .item-image {
      .image-placeholder {
        border: 2px solid #000000;
        background-color: #ffffff;
        color: #434343;
      }
    }
    
    .item-info {
      .item-name {
        .elder-text {
          color: #000000;
        }
        
        .info-icon {
          color: #434343;
          
          &:hover {
            color: #0050b3;
          }
        }
      }
      
      .item-category {
        .elder-text {
          color: #434343;
        }
      }
      
      .item-price {
        .price-with-discount {
          .discount-badge {
            background-color: #d4380d;
          }
        }
      }
    }
    
    .item-quantity {
      .quantity-controls {
        .quantity-btn {
          border: 1px solid #000000;
        }
        
        .quantity-input {
          border: 2px solid #000000;
          
          .ant-input-number-input {
            color: #000000;
          }
        }
      }
      
      .quantity-label {
        .elder-text {
          color: #434343;
        }
      }
    }
    
    .item-total {
      .total-price {
        .elder-text {
          color: #0050b3;
        }
      }
      
      .total-label {
        .elder-text {
          color: #434343;
        }
      }
    }
    
    .item-actions {
      .remove-btn {
        border: 1px solid #000000;
      }
    }
  }
}

// 打印样式
@media print {
  .cart-item {
    border: 1px solid #000000;
    
    .item-actions {
      display: none;
    }
    
    .loading-overlay {
      display: none;
    }
  }
} 