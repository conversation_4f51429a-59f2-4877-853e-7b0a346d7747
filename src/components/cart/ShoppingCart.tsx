import React from 'react';
import { Empty } from 'antd';
import { ShoppingCartOutlined } from '@ant-design/icons';
import { ElderCard } from '../elder';
import './ShoppingCart.less';

interface ShoppingCartProps {
  className?: string;
}

/**
 * 购物车组件 - 简化版本
 */
const ShoppingCart: React.FC<ShoppingCartProps> = ({
  className = '',
}) => {
  return (
    <ElderCard 
      title="购物车" 
      className={`shopping-cart ${className}`}
      extra={<ShoppingCartOutlined />}
    >
      <Empty 
        description="购物车为空"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    </ElderCard>
  );
};

export default ShoppingCart; 