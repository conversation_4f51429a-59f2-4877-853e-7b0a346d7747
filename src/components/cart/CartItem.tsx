import React, { useState, useCallback } from 'react';
import { InputNumber, Popconfirm, Image, Tooltip } from 'antd';
import { 
  DeleteOutlined, 
  PlusOutlined, 
  MinusOutlined,
  InfoCircleOutlined,
  TagOutlined
} from '@ant-design/icons';
import { useAppDispatch } from '../../hooks/redux';
import { 
  updateCartItemQuantity, 
  removeFromCart 
} from '../../store/slices/orderSlice';
import { ElderButton } from '../elder';
import type { OrderItem } from '../../store/slices/orderSlice';
import './CartItem.less';

export interface CartItemProps {
  item: OrderItem;
  loading?: boolean;
  showDetails?: boolean;
  className?: string;
}

/**
 * 购物车商品项组件
 * 
 * 功能特性：
 * - 商品信息展示（图片、名称、价格、规格）
 * - 数量调整（+/-按钮、直接输入）
 * - 商品删除确认
 * - 小计计算显示
 * - 适老化设计
 */
const CartItem: React.FC<CartItemProps> = ({
  item,
  loading = false,
  showDetails = true,
  className = '',
}) => {
  const dispatch = useAppDispatch();
  
  // 本地状态
  const [isUpdating, setIsUpdating] = useState(false);

  // 更新商品数量
  const handleQuantityChange = useCallback(async (newQuantity: number | null) => {
    if (!newQuantity || newQuantity < 0) return;
    
    if (newQuantity === item.quantity) return;

    setIsUpdating(true);
    
    try {
      dispatch(updateCartItemQuantity({
        productId: item.productId,
        quantity: newQuantity,
      }));
    } catch (error) {
      console.error('更新商品数量失败:', error);
    } finally {
      setIsUpdating(false);
    }
  }, [dispatch, item.productId, item.quantity]);

  // 增加数量
  const handleIncrease = useCallback(() => {
    handleQuantityChange(item.quantity + 1);
  }, [handleQuantityChange, item.quantity]);

  // 减少数量
  const handleDecrease = useCallback(() => {
    if (item.quantity > 1) {
      handleQuantityChange(item.quantity - 1);
    }
  }, [handleQuantityChange, item.quantity]);

  // 删除商品
  const handleRemove = useCallback(() => {
    dispatch(removeFromCart(item.productId));
  }, [dispatch, item.productId]);

  // 格式化价格
  const formatPrice = (price: number): string => {
    return `¥${price.toFixed(2)}`;
  };

  // 计算折扣信息
  const hasDiscount = item.discount && item.discount > 0;
  const originalPrice = hasDiscount ? item.price / (1 - (item.discount || 0)) : item.price;

  return (
    <div className={`cart-item ${className} ${isUpdating ? 'updating' : ''}`}>
      {/* 商品图片 */}
      <div className="item-image">
        {item.image ? (
          <Image
            src={item.image}
            alt={item.name}
            width={64}
            height={64}
            preview={false}
            fallback="/images/placeholder-dish.png"
          />
        ) : (
          <div className="image-placeholder">
            <TagOutlined />
          </div>
        )}
      </div>

      {/* 商品信息 */}
      <div className="item-info">
        <div className="item-name">
          <span className="elder-text elder-text-base elder-text-bold">
            {item.name}
          </span>
          {showDetails && item.specifications && (
            <Tooltip title={item.specifications} placement="top">
              <InfoCircleOutlined className="info-icon" />
            </Tooltip>
          )}
        </div>
        
        <div className="item-category">
          <span className="elder-text elder-text-sm elder-text-secondary">
            {item.categoryName}
          </span>
          {item.unit && (
            <span className="elder-text elder-text-sm elder-text-secondary">
              · {item.unit}
            </span>
          )}
        </div>

        {/* 价格信息 */}
        <div className="item-price">
          {hasDiscount ? (
            <div className="price-with-discount">
              <span className="elder-text elder-text-sm elder-text-secondary price-original">
                {formatPrice(originalPrice)}
              </span>
              <span className="elder-text elder-text-base elder-text-bold elder-text-error">
                {formatPrice(item.price)}
              </span>
              <span className="discount-badge">
                -{Math.round((item.discount || 0) * 100)}%
              </span>
            </div>
          ) : (
            <span className="elder-text elder-text-base elder-text-bold">
              {formatPrice(item.price)}
            </span>
          )}
        </div>
      </div>

      {/* 数量控制 */}
      <div className="item-quantity">
        <div className="quantity-controls">
          <ElderButton
            size="default"
            icon={<MinusOutlined />}
            onClick={handleDecrease}
            disabled={item.quantity <= 1 || isUpdating || loading}
            className="quantity-btn"
            aria-label="减少数量"
          />
          
          <InputNumber
            value={item.quantity}
            min={1}
            max={999}
            size="small"
            className="quantity-input elder-input"
            onChange={handleQuantityChange}
            disabled={isUpdating || loading}
            controls={false}
          />
          
          <ElderButton
            size="default"
            icon={<PlusOutlined />}
            onClick={handleIncrease}
            disabled={isUpdating || loading}
            className="quantity-btn"
            aria-label="增加数量"
          />
        </div>
        
        <div className="quantity-label">
          <span className="elder-text elder-text-xs elder-text-secondary">
            数量
          </span>
        </div>
      </div>

      {/* 小计 */}
      <div className="item-total">
        <div className="total-price">
          <span className="elder-text elder-text-lg elder-text-bold elder-text-primary">
            {formatPrice(item.totalPrice)}
          </span>
        </div>
        <div className="total-label">
          <span className="elder-text elder-text-xs elder-text-secondary">
            小计
          </span>
        </div>
      </div>

      {/* 删除按钮 */}
      <div className="item-actions">
        <Popconfirm
          title="确认删除"
          description={`确定要从购物车中删除"${item.name}"吗？`}
          onConfirm={handleRemove}
          okText="确认删除"
          cancelText="取消"
          placement="topRight"
          disabled={isUpdating || loading}
        >
          <ElderButton
            danger
            size="default"
            icon={<DeleteOutlined />}
            disabled={isUpdating || loading}
            className="remove-btn"
            aria-label="删除商品"
          />
        </Popconfirm>
      </div>

      {/* 加载遮罩 */}
      {(isUpdating || loading) && (
        <div className="loading-overlay">
          <div className="loading-spinner" />
        </div>
      )}
    </div>
  );
};

export default CartItem; 