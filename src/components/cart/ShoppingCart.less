// 购物车主组件样式
.shopping-cart {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .cart-icon {
        font-size: 20px;
        color: #1677ff;
      }
      
      .elder-text {
        color: #262626;
      }
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .ant-btn {
        height: 32px;
        padding: 0 12px;
        
        &.ant-btn-text {
          color: #595959;
          
          &:hover {
            color: #1677ff;
            background-color: #f0f6ff;
          }
          
          &.ant-btn-dangerous {
            color: #ff4d4f;
            
            &:hover {
              color: #ff7875;
              background-color: #fff1f0;
            }
          }
        }
      }
    }
  }
  
  .cart-error {
    margin-bottom: 16px;
  }
  
  .cart-content {
    flex: 1;
    overflow-y: auto;
    
    .cart-items {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 16px;
    }
    
    .member-discount-prompt {
      margin-bottom: 16px;
      
      .discount-card {
        border: 1px solid #d4edda;
        background-color: #f0f9ff;
        
        .discount-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .elder-text {
            color: #52c41a;
          }
          
          .ant-btn {
            color: #1677ff;
            padding: 0;
            height: auto;
            
            &:hover {
              color: #40a9ff;
            }
          }
        }
      }
    }
    
    .cart-empty {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
      
      .ant-empty {
        .empty-description {
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          .elder-text {
            color: #8c8c8c;
          }
        }
      }
    }
  }
  
  .cart-stats {
    padding: 12px 0;
    border-top: 1px solid #f0f0f0;
    
    .stats-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .elder-text {
        color: #8c8c8c;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .shopping-cart {
    .cart-header {
      padding: 14px 0;
      margin-bottom: 14px;
      
      .header-title {
        gap: 6px;
        
        .cart-icon {
          font-size: 18px;
        }
        
        .elder-text {
          font-size: 16px;
        }
      }
      
      .header-actions {
        gap: 6px;
        
        .ant-btn {
          height: 28px;
          padding: 0 10px;
          font-size: 13px;
        }
      }
    }
    
    .cart-content {
      .cart-items {
        gap: 10px;
        margin-bottom: 14px;
      }
      
      .member-discount-prompt {
        margin-bottom: 14px;
      }
    }
    
    .cart-stats {
      padding: 10px 0;
    }
  }
}

@media (max-width: 768px) {
  .shopping-cart {
    .cart-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      padding: 12px 0;
      margin-bottom: 12px;
      
      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
    
    .cart-content {
      .cart-items {
        gap: 8px;
        margin-bottom: 12px;
      }
      
      .member-discount-prompt {
        margin-bottom: 12px;
        
        .discount-content {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }
    }
    
    .cart-stats {
      padding: 8px 0;
      
      .stats-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .shopping-cart {
    .cart-header {
      border-bottom: 2px solid #000000;
      
      .header-title {
        .cart-icon {
          color: #0050b3;
        }
        
        .elder-text {
          color: #000000;
        }
      }
      
      .header-actions {
        .ant-btn {
          border: 1px solid #000000;
          
          &.ant-btn-text {
            background-color: #ffffff;
            color: #000000;
            
            &:hover {
              background-color: #f0f0f0;
            }
            
            &.ant-btn-dangerous {
              color: #d4380d;
              
              &:hover {
                background-color: #fff1f0;
              }
            }
          }
        }
      }
    }
    
    .cart-content {
      .member-discount-prompt {
        .discount-card {
          border: 2px solid #0b7f00;
          background-color: #ffffff;
          
          .discount-content {
            .elder-text {
              color: #0b7f00;
            }
            
            .ant-btn {
              color: #0050b3;
              
              &:hover {
                color: #003a8c;
              }
            }
          }
        }
      }
    }
    
    .cart-stats {
      border-top: 2px solid #000000;
      
      .stats-row {
        .elder-text {
          color: #434343;
        }
      }
    }
  }
}

// 打印样式
@media print {
  .shopping-cart {
    .cart-header {
      .header-actions {
        display: none;
      }
    }
    
    .cart-content {
      .member-discount-prompt {
        .discount-content {
          .ant-btn {
            display: none;
          }
        }
      }
    }
  }
} 