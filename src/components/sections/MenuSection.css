/* 菜品展示区域样式 - 现代化设计 */
.menu-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  color: #1e293b;
}

/* 菜品区域标题 */
.menu-section .menu-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background: #ffffff;
}

.menu-section .menu-header .menu-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
}

.menu-section .menu-header .menu-title:before {
  content: '🍽️';
  margin-right: 8px;
  font-size: 20px;
}

/* 搜索栏 */
.menu-search {
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.menu-search .ant-input-affix-wrapper {
  border-radius: 8px;
  border: 1px solid #e2e8f0 !important;
  background: #ffffff !important;
  color: #1e293b !important;
}

.menu-search .ant-input-affix-wrapper input {
  background: #ffffff !important;
  color: #1e293b !important;
}

.menu-search .ant-input-affix-wrapper:hover {
  border-color: #cbd5e1 !important;
}

.menu-search .ant-input-affix-wrapper:focus-within {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.menu-search .ant-input-affix-wrapper .ant-input-prefix {
  color: #64748b !important;
}

/* 分类标签页 */
.menu-categories {
  padding: 8px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav {
  margin-bottom: 0;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab {
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 6px;
  margin-right: 6px;
  min-height: 32px;
  display: flex;
  align-items: center;
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  color: #64748b !important;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab:hover {
  background-color: #f1f5f9 !important;
  border-color: #cbd5e1 !important;
  color: #1e293b !important;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab.ant-tabs-tab-active {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
  border-color: #2563eb !important;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab.ant-tabs-tab-active .ant-badge .ant-badge-count {
  background-color: #ffffff !important;
  color: #3b82f6 !important;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab .ant-badge .ant-badge-count {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
  font-size: 10px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-ink-bar {
  display: none;
}

/* 菜品列表 */
.menu-list {
  flex: 1;
  overflow-y: auto;
  background: #ffffff;
}

.menu-list .menu-items {
  padding: 8px 0;
}

.menu-list .empty-text {
  color: #64748b !important;
  font-size: 14px;
}

.menu-list .ant-empty {
  color: #64748b;
}

.menu-list .ant-empty .ant-empty-description {
  color: #64748b;
}

/* 菜品项 */
.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
  margin: 0;
  border-radius: 0;
  border: none;
}

.menu-item:hover {
  background-color: #f8fafc;
}

.menu-item:last-child {
  border-bottom: 1px solid #f1f5f9;
}

.menu-item.menu-item-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f8fafc;
}

.menu-item.menu-item-disabled:hover {
  background-color: #f8fafc;
}

.menu-item .item-info {
  flex: 1;
}

.menu-item .item-info .item-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.menu-item .item-info .item-price {
  font-size: 14px;
  font-weight: 700;
  color: #3b82f6;
}

.menu-item .item-actions {
  margin-left: 12px;
}

.menu-item .item-actions .ant-btn {
  font-size: 12px;
  padding: 4px 12px;
  height: 32px;
  border-radius: 6px;
  font-weight: 600;
  background: #3b82f6 !important;
  border-color: #2563eb !important;
  color: #ffffff !important;
}

.menu-item .item-actions .ant-btn:hover {
  background: #2563eb !important;
  border-color: #1d4ed8 !important;
}

.menu-item .item-status {
  margin-left: 12px;
  padding: 4px 8px;
  background-color: #9ca3af;
  color: #ffffff;
  font-size: 12px;
  border-radius: 6px;
  font-weight: 600;
}


/* 响应式设计 */
@media (max-width: 1200px) {
  .menu-section .menu-header {
    padding: 12px 16px 8px 16px;
  }

  .menu-section .menu-header .menu-title {
    font-size: 16px;
  }

  .menu-section .menu-search {
    padding: 8px 12px;
  }

  .menu-section .menu-categories {
    padding: 0 12px;
  }

  .menu-section .menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab {
    padding: 6px 12px;
    font-size: 13px;
    min-height: 32px;
    margin-right: 3px;
  }

  .menu-section .menu-item {
    padding: 10px 12px;
  }

  .menu-section .menu-item .item-info .item-name {
    font-size: 15px;
  }

  .menu-section .menu-item .item-info .item-price {
    font-size: 13px;
  }

  .menu-section .menu-item .item-actions .ant-btn {
    font-size: 11px;
    padding: 3px 8px;
  }
}

@media (max-width: 768px) {
  .menu-section .menu-header {
    padding: 10px 12px 6px 12px;
  }

  .menu-section .menu-header .menu-title {
    font-size: 14px;
  }

  .menu-section .menu-search {
    padding: 6px 8px;
  }

  .menu-section .menu-categories {
    padding: 0 8px;
  }

  .menu-section .menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab {
    padding: 4px 8px;
    font-size: 12px;
    min-height: 28px;
    margin-right: 2px;
  }

  .menu-section .menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab .ant-badge .ant-badge-count {
    font-size: 8px;
    min-width: 14px;
    height: 14px;
    line-height: 14px;
  }

  .menu-section .menu-item {
    padding: 8px 10px;
  }

  .menu-section .menu-item .item-info .item-name {
    font-size: 14px;
  }

  .menu-section .menu-item .item-info .item-price {
    font-size: 12px;
  }

  .menu-section .menu-item .item-actions .ant-btn {
    font-size: 10px;
    padding: 2px 6px;
  }
}

