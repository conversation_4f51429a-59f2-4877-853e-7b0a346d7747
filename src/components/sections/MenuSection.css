/* 菜品展示区域样式 - 高端现代化设计 */
.menu-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  color: #e2e8f0;
}

/* 菜品区域标题 */
.menu-section .menu-header {
  padding: 24px;
  border-bottom: 1px solid rgba(34, 197, 94, 0.3);
  background: rgba(26, 32, 44, 0.8);
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.menu-section .menu-header .menu-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #e2e8f0;
  display: flex;
  align-items: center;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

.menu-section .menu-header .menu-title:before {
  content: '🍽️';
  margin-right: 12px;
  font-size: 24px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* 搜索栏 */
.menu-search {
  padding: 20px 24px;
  background: rgba(26, 32, 44, 0.6);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(34, 197, 94, 0.2);
}

.menu-search .ant-input-affix-wrapper {
  border-radius: 12px;
  border: 1px solid rgba(34, 197, 94, 0.3) !important;
  background: rgba(30, 41, 59, 0.8) !important;
  color: #e2e8f0 !important;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.menu-search .ant-input-affix-wrapper input {
  background: #404040 !important;
  color: #ffffff !important;
}

.menu-search .ant-input-affix-wrapper:hover {
  border-color: #ff6b35 !important;
}

.menu-search .ant-input-affix-wrapper:focus-within {
  border-color: #ff6b35 !important;
  box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.2) !important;
}

.menu-search .ant-input-affix-wrapper .ant-input-prefix {
  color: #cccccc !important;
}

/* 分类标签页 */
.menu-categories {
  padding: 0 16px;
  background: #2d2d2d;
  border-bottom: 2px solid #404040;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav {
  margin-bottom: 0;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab {
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px 8px 0 0;
  margin-right: 8px;
  min-height: 48px;
  display: flex;
  align-items: center;
  background: #404040 !important;
  border: 2px solid #555555 !important;
  border-bottom: none !important;
  color: #ffffff !important;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab:hover {
  background-color: #555555 !important;
  border-color: #ff6b35 !important;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab.ant-tabs-tab-active {
  background-color: #ff6b35 !important;
  color: #ffffff !important;
  border-color: #ff6b35 !important;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab.ant-tabs-tab-active .ant-badge .ant-badge-count {
  background-color: #ffffff !important;
  color: #ff6b35 !important;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab .ant-badge .ant-badge-count {
  background-color: #ff6b35 !important;
  color: #ffffff !important;
  font-size: 12px;
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}

.menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-ink-bar {
  display: none;
}

/* 菜品列表 */
.menu-list {
  flex: 1;
  overflow-y: auto;
  background: #2d2d2d;
}

.menu-list .menu-items {
  padding: 12px 0;
}

.menu-list .empty-text {
  color: #888888 !important;
  font-size: 16px;
}

.menu-list .ant-empty {
  color: #888888;
}

.menu-list .ant-empty .ant-empty-description {
  color: #888888;
}

/* 菜品项 */
.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #404040;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #2d2d2d;
  margin: 0 16px;
  border-radius: 8px;
  margin-bottom: 8px;
  border: 2px solid #404040;
}

.menu-item:hover {
  background-color: #3a3a3a;
  border-color: #ff6b35;
  transform: translateY(-1px);
}

.menu-item:last-child {
  border-bottom: 1px solid #404040;
}

.menu-item.menu-item-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #1a1a1a;
  border-color: #333333;
}

.menu-item.menu-item-disabled:hover {
  background-color: #1a1a1a;
  border-color: #333333;
  transform: none;
}

.menu-item .item-info {
  flex: 1;
}

.menu-item .item-info .item-name {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 6px;
}

.menu-item .item-info .item-price {
  font-size: 16px;
  font-weight: 700;
  color: #ff6b35;
}

.menu-item .item-actions {
  margin-left: 16px;
}

.menu-item .item-actions .ant-btn {
  font-size: 14px;
  padding: 8px 16px;
  height: 40px;
  border-radius: 8px;
  font-weight: 600;
  background: #ff6b35 !important;
  border-color: #ff6b35 !important;
  color: #ffffff !important;
}

.menu-item .item-actions .ant-btn:hover {
  background: #ff8a65 !important;
  border-color: #ff8a65 !important;
}

.menu-item .item-status {
  margin-left: 16px;
  padding: 8px 12px;
  background-color: #666666;
  color: #cccccc;
  font-size: 14px;
  border-radius: 8px;
  font-weight: 600;
}


/* 响应式设计 */
@media (max-width: 1200px) {
  .menu-section .menu-header {
    padding: 12px 16px 8px 16px;
  }

  .menu-section .menu-header .menu-title {
    font-size: 16px;
  }

  .menu-section .menu-search {
    padding: 8px 12px;
  }

  .menu-section .menu-categories {
    padding: 0 12px;
  }

  .menu-section .menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab {
    padding: 6px 12px;
    font-size: 13px;
    min-height: 32px;
    margin-right: 3px;
  }

  .menu-section .menu-item {
    padding: 10px 12px;
  }

  .menu-section .menu-item .item-info .item-name {
    font-size: 15px;
  }

  .menu-section .menu-item .item-info .item-price {
    font-size: 13px;
  }

  .menu-section .menu-item .item-actions .ant-btn {
    font-size: 11px;
    padding: 3px 8px;
  }
}

@media (max-width: 768px) {
  .menu-section .menu-header {
    padding: 10px 12px 6px 12px;
  }

  .menu-section .menu-header .menu-title {
    font-size: 14px;
  }

  .menu-section .menu-search {
    padding: 6px 8px;
  }

  .menu-section .menu-categories {
    padding: 0 8px;
  }

  .menu-section .menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab {
    padding: 4px 8px;
    font-size: 12px;
    min-height: 28px;
    margin-right: 2px;
  }

  .menu-section .menu-categories .cashier-menu-tabs .ant-tabs-nav .ant-tabs-tab .ant-badge .ant-badge-count {
    font-size: 8px;
    min-width: 14px;
    height: 14px;
    line-height: 14px;
  }

  .menu-section .menu-item {
    padding: 8px 10px;
  }

  .menu-section .menu-item .item-info .item-name {
    font-size: 14px;
  }

  .menu-section .menu-item .item-info .item-price {
    font-size: 12px;
  }

  .menu-section .menu-item .item-actions .ant-btn {
    font-size: 10px;
    padding: 2px 6px;
  }
}

