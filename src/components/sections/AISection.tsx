import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Switch } from 'antd';
import { useAppDispatch } from '../../hooks/redux';
import { addToCart } from '../../store/slices/orderSlice';
import './AISection.css';

export interface AISectionProps {
  className?: string;
}

// 模拟AI识别结果
interface RecognitionResult {
  id: string;
  dishName: string;
  confidence: number;
  price: number;
  timestamp: string;
  image?: string;
  added: boolean;
}

/**
 * AI识别区域组件 - 专业收银机风格
 *
 * 功能特性：
 * - 开关控制AI识别功能
 * - 上划手势显示AI识别面板
 * - 实时摄像头画面显示
 * - AI菜品识别结果展示
 * - 识别历史记录
 * - 自动添加到购物车
 */
const AISection: React.FC<AISectionProps> = ({
  className = '',
}) => {
  const dispatch = useAppDispatch();
  const videoRef = useRef<HTMLVideoElement>(null);
  const aiPanelRef = useRef<HTMLDivElement>(null);

  // 状态管理
  const [aiEnabled, setAiEnabled] = useState(false); // AI功能开关
  const [aiPanelVisible, setAiPanelVisible] = useState(false); // AI面板显示状态
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [recognitionResults, setRecognitionResults] = useState<RecognitionResult[]>([]);
  const [currentResult, setCurrentResult] = useState<RecognitionResult | null>(null);
  const [autoAdd, setAutoAdd] = useState(true);
  const [cameraEnabled, setCameraEnabled] = useState(false);
  const [touchStartY, setTouchStartY] = useState(0);

  // 模拟识别配置
  const [recognitionConfig] = useState({
    confidence: 0.85,
    interval: 3000,
    enabled: true
  });

  // 手势控制 - 上划显示AI面板
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartY(e.touches[0].clientY);
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    const touchEndY = e.changedTouches[0].clientY;
    const deltaY = touchStartY - touchEndY;

    // 上划超过100px且AI功能已开启
    if (deltaY > 100 && aiEnabled) {
      setAiPanelVisible(true);
    }
    // 下划超过100px
    else if (deltaY < -100) {
      setAiPanelVisible(false);
    }
  };

  // AI功能开关控制
  const handleAiToggle = (checked: boolean) => {
    setAiEnabled(checked);
    if (!checked) {
      setAiPanelVisible(false);
      setIsRecognizing(false);
      setCameraEnabled(false);
    }
  };

  // 启动摄像头
  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraEnabled(true);
      }
    } catch (error) {
      console.error('启动摄像头失败:', error);
    }
  };

  // 添加到购物车
  const handleAddToCart = useCallback((result: RecognitionResult) => {
    dispatch(addToCart({
      id: result.id,
      productId: result.id,
      name: result.dishName,
      price: result.price,
      quantity: 1,
      unit: '份',
      categoryId: 'ai_recognized',
      categoryName: 'AI识别',
    }));

    // 更新结果状态
    setRecognitionResults(prev =>
      prev.map(item =>
        item.id === result.id ? { ...item, added: true } : item
      )
    );

    if (currentResult?.id === result.id) {
      setCurrentResult({ ...result, added: true });
    }
  }, [dispatch, currentResult?.id]);

  // 模拟AI识别 - 增强版连续识别
  const simulateRecognition = useCallback(() => {
    const mockDishes = [
      { name: '宫保鸡丁', price: 28.0, icon: '🍗' },
      { name: '红烧肉', price: 35.0, icon: '🥩' },
      { name: '麻婆豆腐', price: 18.0, icon: '🍲' },
      { name: '鱼香肉丝', price: 25.0, icon: '🐟' },
      { name: '番茄鸡蛋汤', price: 16.0, icon: '🍜' },
      { name: '蒜蓉菜心', price: 12.0, icon: '🥬' },
      { name: '糖醋里脊', price: 22.0, icon: '🍖' },
      { name: '白米饭', price: 3.0, icon: '🍚' }
    ];

    const randomDish = mockDishes[Math.floor(Math.random() * mockDishes.length)];
    const confidence = 0.7 + Math.random() * 0.3; // 70%-100%

    const result: RecognitionResult = {
      id: `ai_${Date.now()}`,
      dishName: randomDish.name,
      confidence,
      price: randomDish.price,
      timestamp: new Date().toLocaleTimeString(),
      image: randomDish.icon,
      added: false
    };

    setCurrentResult(result);
    setRecognitionResults(prev => [result, ...prev.slice(0, 9)]); // 保留最近10条

    // 如果置信度够高且启用自动添加
    if (confidence >= recognitionConfig.confidence && autoAdd) {
      handleAddToCart(result);
    }
  }, [recognitionConfig.confidence, autoAdd, handleAddToCart]);

  // 开始/停止识别
  const toggleRecognition = () => {
    if (isRecognizing) {
      setIsRecognizing(false);
    } else {
      if (!cameraEnabled) {
        startCamera();
      }
      setIsRecognizing(true);
    }
  };

  // 识别循环
  useEffect(() => {
    let interval: any;

    if (isRecognizing && cameraEnabled && recognitionConfig.enabled) {
      interval = setInterval(simulateRecognition, recognitionConfig.interval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecognizing, cameraEnabled, recognitionConfig, simulateRecognition]);

  return (
    <div
      className={`ai-section ${className}`}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
    >
      {/* 主操作区域 - 专业收银机风格 */}
      <div className="main-operation-area">
        {/* AI功能开关 */}
        <div className="ai-toggle-section">
          <div className="toggle-label">AI识别</div>
          <Switch
            checked={aiEnabled}
            onChange={handleAiToggle}
            className="ai-switch"
          />
        </div>

        {/* 操作按钮区域 */}
        <div className="operation-buttons">
          <button className="operation-btn btn-checkout">
            <div className="btn-icon">💰</div>
            <div className="btn-text">收银</div>
          </button>

          <button className="operation-btn btn-delivery">
            <div className="btn-icon">🚚</div>
            <div className="btn-text">外卖</div>
          </button>

          <button className="operation-btn btn-member">
            <div className="btn-icon">👤</div>
            <div className="btn-text">会员</div>
          </button>

          <button className="operation-btn btn-discount">
            <div className="btn-icon">🎫</div>
            <div className="btn-text">优惠</div>
          </button>

          <button className="operation-btn btn-payment">
            <div className="btn-icon">💳</div>
            <div className="btn-text">支付</div>
          </button>

          <button className="operation-btn btn-settings">
            <div className="btn-icon">⚙️</div>
            <div className="btn-text">设置</div>
          </button>
        </div>

        {/* 手势提示 */}
        {aiEnabled && !aiPanelVisible && (
          <div className="gesture-hint">
            <div className="hint-icon">👆</div>
            <div className="hint-text">上划显示AI识别</div>
          </div>
        )}
      </div>

      {/* AI识别面板 - 滑动显示 */}
      {aiEnabled && (
        <div
          ref={aiPanelRef}
          className={`ai-panel ${aiPanelVisible ? 'visible' : 'hidden'}`}
        >
          {/* 面板拖拽条 */}
          <div className="panel-drag-bar">
            <div className="drag-indicator"></div>
          </div>

          {/* AI识别标题 */}
          <div className="ai-header">
            🤖 AI智能识别
          </div>

          {/* AI状态栏 */}
          <div className="ai-status-bar">
            <div className="status-indicator">
              <div className={`status-dot ${isRecognizing ? 'active' : ''}`}></div>
              <span>识别状态：{isRecognizing ? '连续识别中' : '已暂停'}</span>
            </div>
            <div className="status-indicator">
              <span>🌡️ 光线：良好</span>
            </div>
          </div>

          {/* 摄像头预览区 */}
          <div className={`camera-preview ${isRecognizing ? 'scanning' : ''}`}>
            <video ref={videoRef} className="camera-video" autoPlay muted />
            <div className="camera-overlay">
              <div className="camera-icon">📸</div>
              <div className="camera-status">
                {isRecognizing ? '🔄 正在扫描...' : '点击开始识别'}
              </div>
              <div className="camera-guide">
                💡 自动识别：检测到菜品将自动添加到订单
              </div>
            </div>
          </div>

          {/* 识别历史记录 */}
          <div className="recognition-history">
            <div className="history-header">
              📝 识别记录
            </div>
            <div className="history-list">
              {recognitionResults.length > 0 ? (
                recognitionResults.map((result) => (
                  <div key={result.id} className="history-item">
                    <span className="history-icon">{result.image}</span>
                    <div className="history-info">
                      <div className="history-name">{result.dishName}</div>
                      <div className="history-time">
                        {result.timestamp} {result.added ? '自动添加' : '待确认'}
                      </div>
                    </div>
                    <div className="history-price">¥{result.price.toFixed(2)}</div>
                  </div>
                ))
              ) : (
                <div className="history-item">
                  <span className="history-icon">🍗</span>
                  <div className="history-info">
                    <div className="history-name">宫保鸡丁</div>
                    <div className="history-time">14:28 自动添加</div>
                  </div>
                  <div className="history-price">¥18.00</div>
                </div>
              )}
            </div>
          </div>

          {/* AI控制按钮 */}
          <div className="ai-controls">
            <button
              className={`ai-btn ${isRecognizing ? 'success' : ''}`}
              onClick={toggleRecognition}
            >
              {isRecognizing ? '⏸️ 暂停识别' : '▶️ 开始识别'}
            </button>
            <button
              className="ai-btn"
              onClick={() => setRecognitionResults([])}
            >
              🗑️ 清空记录
            </button>
            <button
              className="ai-btn"
              onClick={() => setAiPanelVisible(false)}
            >
              ❌ 关闭面板
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AISection; 