import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useAppDispatch } from '../../hooks/redux';
import { addToCart } from '../../store/slices/orderSlice';
import './AISection.less';

export interface AISectionProps {
  className?: string;
}

// 模拟AI识别结果
interface RecognitionResult {
  id: string;
  dishName: string;
  confidence: number;
  price: number;
  timestamp: string;
  image?: string;
  added: boolean;
}

/**
 * AI识别区域组件
 * 
 * 功能特性：
 * - 实时摄像头画面显示
 * - AI菜品识别结果展示
 * - 识别历史记录
 * - 自动添加到购物车
 * - 识别参数设置
 */
const AISection: React.FC<AISectionProps> = ({
  className = '',
}) => {
  const dispatch = useAppDispatch();
  const videoRef = useRef<HTMLVideoElement>(null);
  
  // 状态管理
  const [isRecognizing, setIsRecognizing] = useState(false);
  const [recognitionResults, setRecognitionResults] = useState<RecognitionResult[]>([]);
  const [currentResult, setCurrentResult] = useState<RecognitionResult | null>(null);
  const [autoAdd, setAutoAdd] = useState(true);
  const [cameraEnabled, setCameraEnabled] = useState(false);

  // 模拟识别配置
  const [recognitionConfig] = useState({
    confidence: 0.85,
    interval: 3000,
    enabled: true
  });

  // 启动摄像头
  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraEnabled(true);
      }
    } catch (error) {
      console.error('启动摄像头失败:', error);
    }
  };

  // 添加到购物车
  const handleAddToCart = useCallback((result: RecognitionResult) => {
    dispatch(addToCart({
      id: result.id,
      productId: result.id,
      name: result.dishName,
      price: result.price,
      quantity: 1,
      unit: '份',
      categoryId: 'ai_recognized',
      categoryName: 'AI识别',
    }));

    // 更新结果状态
    setRecognitionResults(prev =>
      prev.map(item =>
        item.id === result.id ? { ...item, added: true } : item
      )
    );

    if (currentResult?.id === result.id) {
      setCurrentResult({ ...result, added: true });
    }
  }, [dispatch, currentResult?.id]);

  // 模拟AI识别 - 增强版连续识别
  const simulateRecognition = useCallback(() => {
    const mockDishes = [
      { name: '宫保鸡丁', price: 28.0, icon: '🍗' },
      { name: '红烧肉', price: 35.0, icon: '🥩' },
      { name: '麻婆豆腐', price: 18.0, icon: '🍲' },
      { name: '鱼香肉丝', price: 25.0, icon: '🐟' },
      { name: '番茄鸡蛋汤', price: 16.0, icon: '🍜' },
      { name: '蒜蓉菜心', price: 12.0, icon: '🥬' },
      { name: '糖醋里脊', price: 22.0, icon: '🍖' },
      { name: '白米饭', price: 3.0, icon: '🍚' }
    ];

    const randomDish = mockDishes[Math.floor(Math.random() * mockDishes.length)];
    const confidence = 0.7 + Math.random() * 0.3; // 70%-100%

    const result: RecognitionResult = {
      id: `ai_${Date.now()}`,
      dishName: randomDish.name,
      confidence,
      price: randomDish.price,
      timestamp: new Date().toLocaleTimeString(),
      image: randomDish.icon,
      added: false
    };

    setCurrentResult(result);
    setRecognitionResults(prev => [result, ...prev.slice(0, 9)]); // 保留最近10条

    // 如果置信度够高且启用自动添加
    if (confidence >= recognitionConfig.confidence && autoAdd) {
      handleAddToCart(result);
    }
  }, [recognitionConfig.confidence, autoAdd, handleAddToCart]);

  // 开始/停止识别
  const toggleRecognition = () => {
    if (isRecognizing) {
      setIsRecognizing(false);
    } else {
      if (!cameraEnabled) {
        startCamera();
      }
      setIsRecognizing(true);
    }
  };

  // 识别循环
  useEffect(() => {
    let interval: any;

    if (isRecognizing && cameraEnabled && recognitionConfig.enabled) {
      interval = setInterval(simulateRecognition, recognitionConfig.interval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecognizing, cameraEnabled, recognitionConfig, simulateRecognition]);

  return (
    <div
      className={`ai-section ${className} ${isRecognizing ? 'auto-mode' : ''}`}
      style={{
        width: '100%',
        height: '100%',
        background: 'white',
        padding: '20px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        flexDirection: 'column',
        fontFamily: '"Microsoft YaHei", Arial, sans-serif'
      }}
    >
      {/* AI识别标题 */}
      <div
        className="ai-header"
        style={{
          textAlign: 'center',
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1976D2',
          marginBottom: '20px',
          borderBottom: '2px solid #1976D2',
          paddingBottom: '10px'
        }}
      >
        🤖 AI智能扫菜识别
      </div>

      {/* AI状态栏 */}
      <div
        className="ai-status-bar"
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          background: '#E3F2FD',
          padding: '12px 16px',
          borderRadius: '8px',
          marginBottom: '20px',
          fontSize: '16px'
        }}
      >
        <div className="status-indicator" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div
            className={`status-dot ${isRecognizing ? 'active' : ''}`}
            style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              background: '#4CAF50'
            }}
          ></div>
          <span id="ai-status">识别状态：{isRecognizing ? '连续识别中' : '已暂停'}</span>
        </div>
        <div className="status-indicator" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span>🌡️ 光线：良好</span>
        </div>
      </div>

      {/* 摄像头预览区 */}
      <div
        className={`camera-preview ${isRecognizing ? 'scanning' : ''}`}
        id="camera-preview"
        style={{
          width: '100%',
          height: '280px',
          background: isRecognizing
            ? 'linear-gradient(135deg, #C8E6C9, #A5D6A7)'
            : 'linear-gradient(135deg, #BBDEFB, #90CAF9)',
          border: isRecognizing ? '3px solid #4CAF50' : '3px solid #1976D2',
          borderRadius: '12px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#1976D2',
          marginBottom: '20px',
          position: 'relative',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
        }}
      >
        <div className="camera-icon" style={{ fontSize: '64px', marginBottom: '15px' }}>📸</div>
        <div
          className="camera-status"
          id="camera-status"
          style={{ fontSize: '18px', textAlign: 'center', fontWeight: 'bold' }}
        >
          {isRecognizing ? '🔄 正在扫描...' : '点击开始识别'}
        </div>
        <div
          className="camera-guide"
          style={{
            position: 'absolute',
            bottom: '15px',
            left: '50%',
            transform: 'translateX(-50%)',
            fontSize: '14px',
            color: '#1565C0',
            textAlign: 'center'
          }}
        >
          💡 自动识别：检测到菜品将自动添加到订单
        </div>
      </div>

      {/* 识别历史记录 */}
      <div
        className="recognition-history"
        id="recognition-history"
        style={{
          background: '#F5F5F5',
          borderRadius: '12px',
          padding: '16px',
          marginBottom: '20px',
          maxHeight: '200px',
          overflowY: 'auto'
        }}
      >
        <div
          className="history-header"
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#212121',
            marginBottom: '12px',
            borderBottom: '1px solid #E0E0E0',
            paddingBottom: '8px'
          }}
        >
          📝 识别记录
        </div>
        <div
          className="history-list"
          id="history-list"
          style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}
        >
          {recognitionResults.length > 0 ? (
            recognitionResults.map((result) => (
              <div
                key={result.id}
                className="history-item"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  padding: '8px 12px',
                  background: 'white',
                  borderRadius: '8px',
                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                }}
              >
                <span className="history-icon" style={{ fontSize: '24px' }}>{result.image}</span>
                <div className="history-info" style={{ flex: 1 }}>
                  <div
                    className="history-name"
                    style={{ fontSize: '16px', fontWeight: 'bold', color: '#212121' }}
                  >
                    {result.dishName}
                  </div>
                  <div
                    className="history-time"
                    style={{ fontSize: '12px', color: '#757575' }}
                  >
                    {result.timestamp} {result.added ? '自动添加' : '待确认'}
                  </div>
                </div>
                <div
                  className="history-price"
                  style={{ fontSize: '14px', fontWeight: 'bold', color: '#4CAF50' }}
                >
                  ¥{result.price.toFixed(2)}
                </div>
              </div>
            ))
          ) : (
            <div
              className="history-item"
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                padding: '8px 12px',
                background: 'white',
                borderRadius: '8px',
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
              }}
            >
              <span className="history-icon" style={{ fontSize: '24px' }}>🍗</span>
              <div className="history-info" style={{ flex: 1 }}>
                <div
                  className="history-name"
                  style={{ fontSize: '16px', fontWeight: 'bold', color: '#212121' }}
                >
                  宫保鸡丁
                </div>
                <div
                  className="history-time"
                  style={{ fontSize: '12px', color: '#757575' }}
                >
                  14:28 自动添加
                </div>
              </div>
              <div
                className="history-price"
                style={{ fontSize: '14px', fontWeight: 'bold', color: '#4CAF50' }}
              >
                ¥18.00
              </div>
            </div>
          )}
        </div>
      </div>

      {/* AI控制按钮 */}
      <div
        className="ai-controls"
        style={{ display: 'flex', gap: '12px', marginTop: 'auto' }}
      >
        <button
          className={`ai-btn ${isRecognizing ? 'success' : ''}`}
          id="toggle-recognition"
          onClick={toggleRecognition}
          style={{
            flex: 1,
            padding: '12px 16px',
            border: '2px solid #1976D2',
            background: isRecognizing ? '#4CAF50' : 'white',
            color: isRecognizing ? 'white' : '#1976D2',
            borderColor: isRecognizing ? '#4CAF50' : '#1976D2',
            borderRadius: '8px',
            fontSize: '16px',
            fontWeight: 'bold',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
        >
          {isRecognizing ? '⏸️ 暂停识别' : '▶️ 开始识别'}
        </button>
        <button
          className="ai-btn"
          onClick={() => setRecognitionResults([])}
          style={{
            flex: 1,
            padding: '12px 16px',
            border: '2px solid #1976D2',
            background: 'white',
            color: '#1976D2',
            borderRadius: '8px',
            fontSize: '16px',
            fontWeight: 'bold',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
        >
          🗑️ 清空记录
        </button>
        <button
          className="ai-btn"
          onClick={() => {/* 打开设置 */}}
          style={{
            flex: 1,
            padding: '12px 16px',
            border: '2px solid #1976D2',
            background: 'white',
            color: '#1976D2',
            borderRadius: '8px',
            fontSize: '16px',
            fontWeight: 'bold',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
        >
          ⚙️ 设置
        </button>
      </div>
    </div>
  );
};

export default AISection; 