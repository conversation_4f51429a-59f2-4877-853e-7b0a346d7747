/* AI区域样式 - 现代化设计 */
.ai-section {
  height: 100%;
  background: #ffffff;
  position: relative;
  overflow: hidden;
  color: #1e293b;
}

/* 主操作区域 */
.main-operation-area {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background: #ffffff;
}

/* AI功能开关区域 */
.ai-toggle-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: rgba(26, 32, 44, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  margin-bottom: 24px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.toggle-label {
  font-size: 18px;
  font-weight: 600;
  color: #e2e8f0;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

.ai-switch .ant-switch {
  background: rgba(71, 85, 105, 0.8) !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
}

.ai-switch .ant-switch-checked {
  background: linear-gradient(135deg, #ff6b35 0%, #f59e0b 100%) !important;
  box-shadow: 0 4px 12px rgba(245, 101, 53, 0.3) !important;
}

.ai-switch .ant-switch-handle {
  background: #ffffff !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* 操作按钮区域 */
.operation-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 12px;
  flex: 1;
  margin-bottom: 16px;
}

.operation-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #1e293b;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  min-height: 100px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.operation-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.operation-btn:active {
  transform: translateY(0);
}

.btn-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.btn-text {
  font-size: 14px;
  font-weight: 600;
}

/* 特殊按钮颜色 */
.btn-checkout {
  background: #3b82f6;
  border-color: #2563eb;
  color: #ffffff;
}

.btn-checkout:hover {
  background: #2563eb;
  border-color: #1d4ed8;
  color: #ffffff;
}

.btn-delivery {
  background: #10b981;
  border-color: #059669;
  color: #ffffff;
}

.btn-delivery:hover {
  background: #059669;
  border-color: #047857;
  color: #ffffff;
}

.btn-member {
  background: #8b5cf6;
  border-color: #7c3aed;
  color: #ffffff;
}

.btn-member:hover {
  background: #7c3aed;
  border-color: #6d28d9;
  color: #ffffff;
}

.btn-discount {
  background: #f59e0b;
  border-color: #d97706;
  color: #ffffff;
}

.btn-discount:hover {
  background: #d97706;
  border-color: #b45309;
  color: #ffffff;
}

.btn-payment {
  background: #ec4899;
  border-color: #db2777;
  color: #ffffff;
}

.btn-payment:hover {
  background: #db2777;
  border-color: #be185d;
  color: #ffffff;
}

.btn-settings {
  background: #6b7280;
  border-color: #4b5563;
  color: #ffffff;
}

.btn-settings:hover {
  background: #4b5563;
  border-color: #374151;
  color: #ffffff;
}

/* 手势提示 */
.gesture-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: #404040;
  border-radius: 12px;
  border: 2px dashed #666666;
  animation: pulse 2s infinite;
}

.hint-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.hint-text {
  font-size: 14px;
  color: #cccccc;
  text-align: center;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* AI面板 */
.ai-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80%;
  background: #2d2d2d;
  border-top: 3px solid #ff6b35;
  border-radius: 20px 20px 0 0;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
  z-index: 1000;
  overflow-y: auto;
}

.ai-panel.visible {
  transform: translateY(0);
}

.ai-panel.hidden {
  transform: translateY(100%);
}

/* 面板拖拽条 */
.panel-drag-bar {
  display: flex;
  justify-content: center;
  padding: 8px 0 16px 0;
  margin-bottom: 16px;
}

.drag-indicator {
  width: 40px;
  height: 4px;
  background: #666666;
  border-radius: 2px;
}

/* AI标题 */
.ai-header {
  text-align: center;
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #404040;
}

/* AI状态栏 */
.ai-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #404040;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 14px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #666666;
  transition: background 0.3s ease;
}

.status-dot.active {
  background: #4CAF50;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* 摄像头预览区 */
.camera-preview {
  position: relative;
  width: 100%;
  height: 200px;
  background: #1a1a1a;
  border: 2px solid #404040;
  border-radius: 12px;
  margin-bottom: 16px;
  overflow: hidden;
}

.camera-preview.scanning {
  border-color: #4CAF50;
  animation: scanning 2s infinite;
}

@keyframes scanning {
  0%, 100% { border-color: #4CAF50; }
  50% { border-color: #66BB6A; }
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.camera-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  color: #ffffff;
}

.camera-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.camera-status {
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 8px;
}

.camera-guide {
  font-size: 12px;
  text-align: center;
  color: #cccccc;
}

/* 识别历史记录 */
.recognition-history {
  background: #404040;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.history-header {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #555555;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #2d2d2d;
  border-radius: 8px;
  border: 1px solid #555555;
}

.history-icon {
  font-size: 20px;
}

.history-info {
  flex: 1;
}

.history-name {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
}

.history-time {
  font-size: 12px;
  color: #cccccc;
}

.history-price {
  font-size: 14px;
  font-weight: 600;
  color: #4CAF50;
}

/* AI控制按钮 */
.ai-controls {
  display: flex;
  gap: 12px;
  margin-top: auto;
}

.ai-btn {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #404040;
  background: #2d2d2d;
  color: #ffffff;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.ai-btn:hover {
  background: #3a3a3a;
  border-color: #555555;
}

.ai-btn.success {
  background: #4CAF50;
  border-color: #4CAF50;
}

.ai-btn.success:hover {
  background: #66BB6A;
  border-color: #66BB6A;
}
