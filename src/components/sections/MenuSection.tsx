import React, { useState, useMemo } from 'react';
import { Tabs, Empty, Badge } from 'antd';
import type { TabsProps } from 'antd';
import { SearchOutlined, AppstoreOutlined } from '@ant-design/icons';
import { useAppDispatch } from '../../hooks/redux';
import { addToCart } from '../../store/slices/orderSlice';
import { ElderButton, ElderInput } from '../elder';
import './MenuSection.less';

// 模拟菜品数据
const mockMenuData = [
  {
    category: '热菜',
    items: [
      { id: '1', name: '宫保鸡丁', price: 28.0, description: '经典川菜，香辣可口', available: true },
      { id: '2', name: '红烧肉', price: 35.0, description: '肥而不腻，香甜可口', available: true },
      { id: '3', name: '麻婆豆腐', price: 18.0, description: '嫩滑豆腐，麻辣鲜香', available: false },
      { id: '4', name: '鱼香肉丝', price: 25.0, description: '甜酸开胃，下饭神器', available: true }
    ]
  },
  {
    category: '凉菜',
    items: [
      { id: '5', name: '凉拌黄瓜', price: 12.0, description: '清爽解腻，开胃小菜', available: true },
      { id: '6', name: '口水鸡', price: 32.0, description: '麻辣鲜香，口感丰富', available: true },
      { id: '7', name: '凉拌三丝', price: 15.0, description: '营养均衡，色彩丰富', available: true }
    ]
  },
  {
    category: '汤品',
    items: [
      { id: '8', name: '番茄鸡蛋汤', price: 16.0, description: '酸甜开胃，营养丰富', available: true },
      { id: '9', name: '冬瓜排骨汤', price: 28.0, description: '清淡鲜美，滋补养生', available: true },
      { id: '10', name: '紫菜蛋花汤', price: 12.0, description: '清香淡雅，简单美味', available: true }
    ]
  },
  {
    category: '主食',
    items: [
      { id: '11', name: '白米饭', price: 3.0, description: '优质大米，香软可口', available: true },
      { id: '12', name: '炒河粉', price: 22.0, description: '滑嫩爽口，香味浓郁', available: true },
      { id: '13', name: '蛋炒饭', price: 18.0, description: '粒粒分明，营养丰富', available: true }
    ]
  }
];

/**
 * 菜品展示区域组件
 * 
 * 功能特性：
 * - 菜品分类展示
 * - 商品搜索功能
 * - 网格布局展示
 * - 快速添加到购物车
 * - 适老化设计
 */
const MenuSection: React.FC = () => {
  const dispatch = useAppDispatch();
  
  // 本地状态
  const [activeCategory, setActiveCategory] = useState('热菜');
  const [searchKeyword, setSearchKeyword] = useState('');

  // 获取当前分类的菜品
  const currentCategoryItems = useMemo(() => {
    const category = mockMenuData.find(cat => cat.category === activeCategory);
    if (!category) return [];
    
    if (!searchKeyword) return category.items;
    
    return category.items.filter(item => 
      item.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      item.description.toLowerCase().includes(searchKeyword.toLowerCase())
    );
  }, [activeCategory, searchKeyword]);

  // 添加到购物车
  const handleAddToCart = (item: any) => {
    dispatch(addToCart({
      id: `${item.id}_${Date.now()}`,
      productId: item.id,
      name: item.name,
      price: item.price,
      quantity: 1,
      unit: '份',
      categoryId: activeCategory,
      categoryName: activeCategory,
    }));
  };

  // 生成分类标签页
  const categoryTabs: TabsProps['items'] = mockMenuData.map(category => ({
    key: category.category,
    label: (
      <span className="elder-text elder-text-base elder-text-bold">
        {category.category}
        <Badge 
          count={category.items.filter(item => item.available).length} 
          size="small" 
          style={{ marginLeft: 8 }}
        />
      </span>
    ),
  }));

  // 渲染菜品项
  const renderMenuItem = (item: any) => (
    <div
      key={item.id}
      className={`menu-item ${!item.available ? 'menu-item-disabled' : ''}`}
      onClick={() => item.available && handleAddToCart(item)}
    >
      <div className="item-info">
        <div className="item-name">{item.name}</div>
        <div className="item-price">¥{item.price.toFixed(2)}</div>
      </div>
      {item.available && (
        <div className="item-actions">
          <ElderButton
            type="primary"
            size="default"
            onClick={(e) => {
              e.stopPropagation();
              handleAddToCart(item);
            }}
          >
            添加
          </ElderButton>
        </div>
      )}
      {!item.available && (
        <div className="item-status">暂无</div>
      )}
    </div>
  );

  return (
    <div className="menu-section">
      {/* 菜品区域标题 */}
      <div className="menu-header">
        <h3 className="menu-title">菜品展示</h3>
      </div>

      {/* 搜索栏 */}
      <div className="menu-search">
        <ElderInput
          placeholder="搜索菜品..."
          prefix={<SearchOutlined />}
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          size="large"
          clearable
        />
      </div>

      {/* 分类标签页 */}
      <div className="menu-categories">
        <Tabs
          activeKey={activeCategory}
          onChange={setActiveCategory}
          items={categoryTabs}
          size="large"
          tabPosition="top"
          className="cashier-menu-tabs"
        />
      </div>

      {/* 菜品列表 */}
      <div className="menu-list">
        {currentCategoryItems.length > 0 ? (
          <div className="menu-items">
            {currentCategoryItems.map(renderMenuItem)}
          </div>
        ) : (
          <Empty
            description={
              <span className="empty-text">
                {searchKeyword ? '没有找到相关菜品' : '该分类暂无菜品'}
              </span>
            }
            image={<AppstoreOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />}
          />
        )}
      </div>
    </div>
  );
};

export default MenuSection; 