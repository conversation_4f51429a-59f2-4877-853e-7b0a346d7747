import React, { useState } from 'react';
import { Card, Button, Badge, Space, Divider, Avatar } from 'antd';
import { 
  ShoppingCartOutlined, 
  UserOutlined, 
  PlusOutlined, 
  MinusOutlined,
  DeleteOutlined,
  ClearOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { 
  removeFromCart, 
  updateCartItemQuantity, 
  clearCart, 
  selectCartItems, 
  selectCartTotal,
  type OrderItem
} from '../../store/slices/orderSlice';
import { ElderCard, ElderButton } from '../elder';
import './OrderSection.less';

interface OrderSectionProps {
  className?: string;
}

/**
 * 订单区域组件
 * 
 * 功能特性：
 * - 显示购物车商品列表
 * - 商品数量调整
 * - 会员信息展示
 * - 订单总计计算
 * - 结算操作按钮
 */
const OrderSection: React.FC<OrderSectionProps> = ({
  className = '',
}) => {
  const dispatch = useAppDispatch();
  const cartItems = useAppSelector(selectCartItems);
  const cartTotal = useAppSelector(selectCartTotal);
  
  // 模拟会员信息
  const [memberInfo] = useState({
    id: 'M001',
    name: '张大爷',
    phone: '138****5678',
    cardNo: '1001',
    level: 'gold',
    points: 1280,
    balance: 156.50
  });

  // 增加商品数量
  const handleIncreaseQuantity = (item: OrderItem) => {
    dispatch(updateCartItemQuantity({ 
      productId: item.productId, 
      quantity: item.quantity + 1 
    }));
  };

  // 减少商品数量
  const handleDecreaseQuantity = (item: OrderItem) => {
    if (item.quantity > 1) {
      dispatch(updateCartItemQuantity({ 
        productId: item.productId, 
        quantity: item.quantity - 1 
      }));
    } else {
      dispatch(removeFromCart(item.productId));
    }
  };

  // 移除商品
  const handleRemoveItem = (productId: string) => {
    dispatch(removeFromCart(productId));
  };

  // 清空购物车
  const handleClearCart = () => {
    dispatch(clearCart());
  };

  // 渲染购物车商品
  const renderCartItem = (item: OrderItem) => (
    <div key={item.id} className="order-item">
      <div className="item-info">
        <div className="item-name">
          {item.name}
        </div>
        <div className="item-price">
          ¥{item.price.toFixed(2)}/{item.unit}
        </div>
      </div>
      
      <div className="item-controls">
        <div className="quantity-controls">
          <ElderButton
            type="primary"
            size="default"
            icon={<MinusOutlined />}
            onClick={() => handleDecreaseQuantity(item)}
            className="quantity-btn"
          />
          <span className="quantity-display">
            {item.quantity}
          </span>
          <ElderButton
            type="primary"
            size="default"
            icon={<PlusOutlined />}
            onClick={() => handleIncreaseQuantity(item)}
            className="quantity-btn"
          />
        </div>
        
        <div className="item-total">
          ¥{(item.price * item.quantity).toFixed(2)}
        </div>

        <div className="remove-btn">
          <ElderButton
            type="text"
            size="default"
            icon={<DeleteOutlined />}
            onClick={() => handleRemoveItem(item.productId)}
            danger
          />
        </div>
      </div>
    </div>
  );

  return (
    <div className={`order-section ${className}`}>
      {/* 订单标题 */}
      <div className="order-header">
        <h3 className="order-title">
          📋 订单清单
          {cartItems.length > 0 && (
            <Badge
              count={cartItems.length}
              style={{ marginLeft: 8 }}
            />
          )}
        </h3>
        {cartItems.length > 0 && (
          <ElderButton
            type="text"
            size="default"
            icon={<ClearOutlined />}
            onClick={handleClearCart}
            danger
          >
            清空
          </ElderButton>
        )}
      </div>

      {/* 会员信息 */}
      <Card className="member-info-card" size="small">
        <div className="member-info">
          <Avatar 
            size={40} 
            icon={<UserOutlined />} 
            style={{ backgroundColor: '#FFC107' }}
          />
          <div className="member-details">
            <div className="member-name elder-text elder-text-base elder-text-bold">
              {memberInfo.name} ({memberInfo.level === 'gold' ? '金卡' : '普通'})
            </div>
            <div className="member-meta elder-text elder-text-sm elder-text-secondary">
              卡号: {memberInfo.cardNo} | 积分: {memberInfo.points}
            </div>
            <div className="member-balance elder-text elder-text-sm">
              余额: ¥{memberInfo.balance.toFixed(2)}
            </div>
          </div>
        </div>
      </Card>

      {/* 商品列表 */}
      <div className="order-items">
        {cartItems.length > 0 ? (
          cartItems.map(renderCartItem)
        ) : (
          <div className="empty-cart">
            <ShoppingCartOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
            <p className="elder-text elder-text-base elder-text-secondary">
              购物车为空，请选择商品
            </p>
          </div>
        )}
      </div>

      {/* 订单汇总 */}
      <div className="order-summary">
        <Divider style={{ margin: '12px 0' }} />
        
        <div className="summary-row">
          <span className="elder-text elder-text-base">商品总计:</span>
          <span className="elder-text elder-text-base elder-text-bold">
            ¥{cartTotal.subtotal.toFixed(2)}
          </span>
        </div>
        
        <div className="summary-row">
          <span className="elder-text elder-text-base">会员优惠:</span>
          <span className="elder-text elder-text-base elder-text-success">
            -¥{cartTotal.discount.toFixed(2)}
          </span>
        </div>
        
        <Divider style={{ margin: '8px 0' }} />
        
        <div className="summary-total">
          <span className="elder-text elder-text-lg elder-text-bold">
            应付金额:
          </span>
          <span className="elder-text elder-text-xl elder-text-bold elder-text-error">
            ¥{cartTotal.total.toFixed(2)}
          </span>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="order-actions">
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <ElderButton
            type="primary"
            size="large"
            icon={<CheckCircleOutlined />}
            disabled={cartItems.length === 0}
            block
          >
            立即结算 ({cartItems.length}件)
          </ElderButton>
          
          <Space style={{ width: '100%' }}>
            <ElderButton type="default" size="large" style={{ flex: 1 }}>
              现金支付
            </ElderButton>
            <ElderButton type="default" size="large" style={{ flex: 1 }}>
              刷卡支付
            </ElderButton>
          </Space>
          
          <ElderButton type="default" size="large" block>
            扫码支付
          </ElderButton>
        </Space>
      </div>
    </div>
  );
};

export default OrderSection; 