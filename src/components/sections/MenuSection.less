// 菜品展示区域样式 - 收银机风格
.menu-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;

  // 菜品区域标题
  .menu-header {
    padding: 16px 20px 12px 20px;
    border-bottom: 2px solid #1976D2;
    background: #f8f9fa;

    .menu-title {
      margin: 0;
      font-size: 18px;
      font-weight: bold;
      color: #1976D2;
      display: flex;
      align-items: center;

      &:before {
        content: '🍽️';
        margin-right: 8px;
      }
    }
  }
}

// 搜索栏
.menu-search {
  padding: 12px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e8e8e8;

  .ant-input-affix-wrapper {
    border-radius: 4px;
    border: 1px solid #d9d9d9;

    &:hover {
      border-color: #1976D2;
    }

    &:focus-within {
      border-color: #1976D2;
      box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
    }
  }
}

// 分类标签页
.menu-categories {
  padding: 0 16px;
  background: #ffffff;
  border-bottom: 1px solid #e8e8e8;

  .cashier-menu-tabs {
    .ant-tabs-nav {
      margin-bottom: 0;

      .ant-tabs-tab {
        padding: 8px 16px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 4px 4px 0 0;
        margin-right: 4px;
        min-height: 36px;
        display: flex;
        align-items: center;
        background: #f5f5f5;
        border: 1px solid #d9d9d9;
        border-bottom: none;

        &:hover {
          background-color: #e6f4ff;
          border-color: #1976D2;
        }

        &.ant-tabs-tab-active {
          background-color: #1976D2;
          color: #ffffff;
          border-color: #1976D2;

          .ant-badge {
            .ant-badge-count {
              background-color: #ffffff;
              color: #1976D2;
            }
          }
        }

        .ant-badge {
          .ant-badge-count {
            background-color: #1976D2;
            color: #ffffff;
            font-size: 10px;
            min-width: 16px;
            height: 16px;
            line-height: 16px;
          }
        }
      }

      .ant-tabs-ink-bar {
        display: none;
      }
    }
  }
}

// 菜品列表
.menu-list {
  flex: 1;
  overflow-y: auto;
  background: #ffffff;

  .menu-items {
    padding: 8px 0;
  }

  .empty-text {
    color: #999999;
    font-size: 14px;
  }
}

// 菜品项
.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }

  &.menu-item-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #f5f5f5;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  .item-info {
    flex: 1;

    .item-name {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      margin-bottom: 4px;
    }

    .item-price {
      font-size: 14px;
      font-weight: bold;
      color: #f5222d;
    }
  }

  .item-actions {
    margin-left: 12px;

    .ant-btn {
      font-size: 12px;
      padding: 4px 12px;
      height: auto;
      border-radius: 4px;
    }
  }

  .item-status {
    margin-left: 12px;
    padding: 4px 8px;
    background-color: #f5f5f5;
    color: #999999;
    font-size: 12px;
    border-radius: 4px;
  }
}


// 响应式设计
@media (max-width: 1200px) {
  .menu-section {
    .menu-header {
      padding: 12px 16px 8px 16px;

      .menu-title {
        font-size: 16px;
      }
    }

    .menu-search {
      padding: 8px 12px;
    }

    .menu-categories {
      padding: 0 12px;

      .cashier-menu-tabs {
        .ant-tabs-nav {
          .ant-tabs-tab {
            padding: 6px 12px;
            font-size: 13px;
            min-height: 32px;
            margin-right: 3px;
          }
        }
      }
    }

    .menu-item {
      padding: 10px 12px;

      .item-info {
        .item-name {
          font-size: 15px;
        }

        .item-price {
          font-size: 13px;
        }
      }

      .item-actions {
        .ant-btn {
          font-size: 11px;
          padding: 3px 8px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .menu-section {
    .menu-header {
      padding: 10px 12px 6px 12px;

      .menu-title {
        font-size: 14px;
      }
    }

    .menu-search {
      padding: 6px 8px;
    }

    .menu-categories {
      padding: 0 8px;

      .cashier-menu-tabs {
        .ant-tabs-nav {
          .ant-tabs-tab {
            padding: 4px 8px;
            font-size: 12px;
            min-height: 28px;
            margin-right: 2px;

            .ant-badge {
              .ant-badge-count {
                font-size: 8px;
                min-width: 14px;
                height: 14px;
                line-height: 14px;
              }
            }
          }
        }
      }
    }

    .menu-item {
      padding: 8px 10px;

      .item-info {
        .item-name {
          font-size: 14px;
        }

        .item-price {
          font-size: 12px;
        }
      }

      .item-actions {
        .ant-btn {
          font-size: 10px;
          padding: 2px 6px;
        }
      }
    }
  }
}

