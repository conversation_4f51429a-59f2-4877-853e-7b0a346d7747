// 订单列表区域样式 - 收银机风格
.order-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;

  // 订单标题
  .order-header {
    padding: 16px 20px 12px 20px;
    border-bottom: 2px solid #1976D2;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .order-title {
      margin: 0;
      font-size: 18px;
      font-weight: bold;
      color: #1976D2;
      display: flex;
      align-items: center;
    }
  }
  
  .elder-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .ant-tabs-nav {
      margin-bottom: 16px;
      
      .ant-tabs-tab {
        padding: 12px 20px;
        font-size: 16px;
        
        .tab-title {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .anticon {
            font-size: 18px;
          }
          
          .elder-text {
            color: #595959;
          }
        }
        
        &.ant-tabs-tab-active {
          .tab-title {
            .elder-text {
              color: #1677ff;
            }
          }
        }
      }
    }
    
    .ant-tabs-content-holder {
      flex: 1;
      overflow: hidden;
      
      .ant-tabs-content {
        height: 100%;
        
        .ant-tabs-tabpane {
          height: 100%;
          
          .tab-content {
            height: 100%;
            overflow-y: auto;
          }
        }
      }
    }
  }
  
  .section-shopping-cart {
    height: 100%;
  }
  
  .section-order-history {
    height: 100%;
  }
}

// 订单列表
.order-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  
  .elder-order-list {
    .ant-list-item {
      padding: 0;
      margin-bottom: 12px;
      border: none;
    }
  }
}

// 会员信息卡片
.member-info-card {
  margin: 12px 16px;
  border: 1px solid #e8e8e8;

  .member-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .member-details {
      flex: 1;

      .member-name {
        font-size: 16px;
        font-weight: bold;
        color: #333333;
        margin-bottom: 4px;
      }

      .member-meta {
        font-size: 12px;
        color: #666666;
        margin-bottom: 2px;
      }

      .member-balance {
        font-size: 14px;
        color: #1976D2;
        font-weight: 500;
      }
    }
  }
}

// 商品列表区域
.order-items {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;

  .empty-cart {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #999999;

    p {
      margin-top: 12px;
      font-size: 14px;
    }
  }
}

// 订单项
.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }

  .item-info {
    flex: 1;

    .item-name {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      margin-bottom: 4px;
    }

    .item-price {
      font-size: 14px;
      color: #666666;
    }
  }

  .item-controls {
    display: flex;
    align-items: center;
    gap: 8px;

    .quantity-controls {
      display: flex;
      align-items: center;
      gap: 6px;

      .ant-btn {
        min-width: 28px;
        height: 28px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }

      .quantity-display {
        min-width: 32px;
        text-align: center;
        font-size: 14px;
        font-weight: 600;
        color: #333333;
      }
    }

    .item-total {
      margin-left: 12px;
      font-size: 16px;
      font-weight: bold;
      color: #f5222d;
      min-width: 60px;
      text-align: right;
    }

    .remove-btn {
      margin-left: 8px;

      .ant-btn {
        min-width: 28px;
        height: 28px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

// 订单汇总
.order-summary {
  padding: 16px;
  background: #f8f9fa;
  border-top: 1px solid #e8e8e8;

  .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .summary-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 8px;
    border-top: 1px solid #d9d9d9;
    font-size: 16px;
    font-weight: bold;
  }
}

// 操作按钮区域
.order-actions {
  padding: 16px;
  background: #ffffff;
  border-top: 1px solid #e8e8e8;

  .ant-space {
    width: 100%;
  }

  .ant-btn {
    font-size: 14px;
    height: 40px;
    border-radius: 4px;
  }
}

// 支付确认弹窗样式
.payment-modal {
  .payment-content {
    .payment-summary {
      margin-bottom: 24px;
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 8px;
      
      .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &.total-row {
          margin-top: 12px;
          padding-top: 12px;
          border-top: 1px solid #e8e8e8;
        }
        
        .elder-text {
          color: #262626;
          
          &.elder-text-success {
            color: #52c41a;
          }
          
          &.elder-text-error {
            color: #f5222d;
          }
        }
      }
    }
    
    .payment-methods {
      margin-bottom: 24px;
      
      .method-title {
        margin-bottom: 12px;
        
        .elder-text {
          color: #262626;
        }
      }
      
      .method-options {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        
        .method-option {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          padding: 16px;
          border: 2px solid #f0f0f0;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          background-color: #ffffff;
          
          &:hover {
            border-color: #d9d9d9;
            background-color: #f8f9fa;
          }
          
          &.selected {
            border-color: #1677ff;
            background-color: #f0f6ff;
            
            .anticon {
              color: #1677ff;
            }
            
            .elder-text {
              color: #1677ff;
            }
          }
          
          .anticon {
            font-size: 24px;
            color: #8c8c8c;
            transition: color 0.3s ease;
          }
          
          .elder-text {
            color: #595959;
            font-weight: 500;
            transition: color 0.3s ease;
          }
        }
      }
    }
    
    .payment-notes {
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;
      
      .notes-row {
        margin-bottom: 4px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .elder-text {
          color: #8c8c8c;
          line-height: 1.4;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .order-item {
    padding: 12px;
    
    .order-item-info {
      gap: 10px;
      margin-bottom: 10px;
      
      .item-image {
        width: 50px;
        height: 50px;
      }
      
      .item-details {
        h4 {
          font-size: 16px;
        }
      }
    }
    
    .order-item-actions {
      gap: 8px;
      
      .quantity-controls {
        gap: 6px;
        
        .ant-btn {
          min-width: 32px;
          height: 32px;
        }
        
        .quantity-display {
          min-width: 36px;
          font-size: 16px;
        }
      }
      
      .item-total {
        span {
          font-size: 16px;
        }
      }
    }
  }
  
  .order-summary {
    .summary-section {
      .ant-typography-title {
        font-size: 18px;
      }
      
      .summary-row {
        &.total {
          .elder-text {
            font-size: 20px;
          }
        }
      }
    }
  }
  
  .elder-tabs {
    .ant-tabs-nav {
      margin-bottom: 14px;
      
      .ant-tabs-tab {
        padding: 10px 16px;
        font-size: 15px;
        
        .tab-title {
          gap: 6px;
          
          .anticon {
            font-size: 16px;
          }
        }
      }
    }
  }
  
  .payment-modal {
    .payment-content {
      .payment-summary {
        margin-bottom: 20px;
        padding: 14px;
        
        .summary-row {
          margin-bottom: 6px;
          
          &.total-row {
            margin-top: 10px;
            padding-top: 10px;
          }
        }
      }
      
      .payment-methods {
        margin-bottom: 20px;
        
        .method-title {
          margin-bottom: 10px;
        }
        
        .method-options {
          gap: 10px;
          
          .method-option {
            gap: 6px;
            padding: 14px;
            
            .anticon {
              font-size: 20px;
            }
          }
        }
      }
      
      .payment-notes {
        padding-top: 14px;
        
        .notes-row {
          margin-bottom: 3px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .order-section {
    gap: 12px;
  }
  
  .order-item {
    padding: 10px;
    
    .order-item-info {
      gap: 8px;
      margin-bottom: 8px;
      
      .item-image {
        width: 45px;
        height: 45px;
      }
      
      .item-details {
        h4 {
          font-size: 15px;
        }
        
        p {
          font-size: 12px;
        }
      }
    }
    
    .order-item-actions {
      flex-direction: column;
      gap: 8px;
      
      .quantity-controls {
        order: 2;
        
        .ant-btn {
          min-width: 28px;
          height: 28px;
        }
        
        .quantity-display {
          min-width: 32px;
          font-size: 14px;
        }
      }
      
      .item-total {
        order: 1;
        text-align: left;
        
        span {
          font-size: 15px;
        }
      }
    }
  }
  
  .order-summary {
    .summary-section {
      .ant-typography-title {
        font-size: 16px;
      }
      
      .summary-row {
        font-size: 14px;
        
        &.total {
          .elder-text {
            font-size: 18px;
          }
        }
      }
    }
    
    .order-actions {
      .ant-space {
        .ant-space-item {
          &:first-child,
          &:last-child {
            flex: 1;
          }
        }
      }
    }
  }
  
  .elder-tabs {
    .ant-tabs-nav {
      margin-bottom: 12px;
      
      .ant-tabs-tab {
        padding: 8px 12px;
        font-size: 14px;
        
        .tab-title {
          gap: 4px;
          
          .anticon {
            font-size: 14px;
          }
          
          .elder-text {
            font-size: 14px;
          }
        }
      }
    }
  }
  
  .payment-modal {
    .ant-modal {
      width: 90% !important;
      max-width: 400px;
    }
    
    .payment-content {
      .payment-summary {
        margin-bottom: 16px;
        padding: 12px;
        
        .summary-row {
          margin-bottom: 4px;
          
          &.total-row {
            margin-top: 8px;
            padding-top: 8px;
          }
          
          .elder-text {
            font-size: 14px;
            
            &.elder-text-lg {
              font-size: 16px;
            }
            
            &.elder-text-xl {
              font-size: 18px;
            }
          }
        }
      }
      
      .payment-methods {
        margin-bottom: 16px;
        
        .method-title {
          margin-bottom: 8px;
        }
        
        .method-options {
          grid-template-columns: repeat(2, 1fr);
          gap: 8px;
          
          .method-option {
            gap: 4px;
            padding: 12px 8px;
            
            .anticon {
              font-size: 18px;
            }
            
            .elder-text {
              font-size: 12px;
            }
          }
        }
      }
      
      .payment-notes {
        padding-top: 12px;
        
        .notes-row {
          margin-bottom: 2px;
          
          .elder-text {
            font-size: 11px;
          }
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .order-item {
    border: 2px solid #000000;
    
    &:hover {
      border-color: #0050b3;
      background-color: #f0f8ff;
    }
    
    .order-item-info {
      .item-details {
        h4 {
          color: #000000;
        }
        
        p {
          color: #434343;
        }
      }
    }
    
    .order-item-actions {
      .item-total {
        span {
          color: #0050b3;
        }
      }
    }
  }
  
  .order-summary {
    border: 3px solid #000000;
    background: #ffffff;
    
    .summary-section {
      .summary-row {
        &.total {
          border-top: 3px solid #000000;
          
          .elder-text-error {
            color: #a8071a;
          }
        }
      }
    }
  }
  
  .elder-tabs {
    .ant-tabs-nav {
      .ant-tabs-tab {
        border: 1px solid #000000;
        margin-right: 4px;
        
        .tab-title {
          .elder-text {
            color: #000000;
          }
        }
        
        &.ant-tabs-tab-active {
          background-color: #0050b3;
          
          .tab-title {
            .elder-text {
              color: #ffffff;
            }
          }
        }
      }
    }
  }
  
  .payment-modal {
    .payment-content {
      .payment-summary {
        border: 2px solid #000000;
        background-color: #ffffff;
        
        .summary-row {
          &.total-row {
            border-top: 2px solid #000000;
          }
          
          .elder-text {
            color: #000000;
            
            &.elder-text-success {
              color: #0b7f00;
            }
            
            &.elder-text-error {
              color: #d4380d;
            }
          }
        }
      }
      
      .payment-methods {
        .method-title {
          .elder-text {
            color: #000000;
          }
        }
        
        .method-options {
          .method-option {
            border: 2px solid #000000;
            background-color: #ffffff;
            
            &:hover {
              background-color: #f5f5f5;
            }
            
            &.selected {
              background-color: #0050b3;
              
              .anticon,
              .elder-text {
                color: #ffffff;
              }
            }
            
            .anticon {
              color: #434343;
            }
            
            .elder-text {
              color: #000000;
            }
          }
        }
      }
      
      .payment-notes {
        border-top: 2px solid #000000;
        
        .notes-row {
          .elder-text {
            color: #434343;
          }
        }
      }
    }
  }
}

// 打印样式
@media print {
  .order-section {
    height: auto;
  }
  
  .order-list {
    overflow: visible;
  }
  
  .order-item {
    border: 1px solid #000000;
    margin-bottom: 10px;
    break-inside: avoid;
    
    &:hover {
      box-shadow: none;
    }
  }
  
  .order-summary {
    border: 2px solid #000000;
    break-inside: avoid;
    
    .order-actions {
      display: none;
    }
  }
  
  .elder-tabs {
    .ant-tabs-nav {
      display: none;
    }
  }
  
  .payment-modal {
    display: none;
  }
} 