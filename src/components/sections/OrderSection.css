/* 订单列表区域样式 - 现代化设计 */
.order-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  color: #1e293b;
}

/* 订单标题 */
.order-section .order-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-section .order-header .order-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
}

/* 会员信息卡片 */
.member-info-card {
  margin: 20px;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  background: rgba(26, 32, 44, 0.8) !important;
  backdrop-filter: blur(10px);
  border-radius: 16px !important;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.member-info-card .ant-card-body {
  background: transparent !important;
  padding: 20px !important;
}

.member-info-card .member-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.member-info-card .member-info .member-details {
  flex: 1;
}

.member-info-card .member-info .member-details .member-name {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff !important;
  margin-bottom: 6px;
}

.member-info-card .member-info .member-details .member-meta {
  font-size: 14px;
  color: #cccccc !important;
  margin-bottom: 4px;
}

.member-info-card .member-info .member-details .member-balance {
  font-size: 16px;
  color: #4CAF50 !important;
  font-weight: 600;
}

/* 商品列表区域 */
.order-items {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
  background: #ffffff;
}

.order-items .empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #64748b;
}

.order-items .empty-cart p {
  margin-top: 16px;
  font-size: 16px;
  color: #64748b !important;
}

/* 订单项 */
.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.2s ease;
  background: #ffffff;
  margin: 0;
  border-radius: 0;
  border: none;
}

.order-item:hover {
  background: #f8fafc;
}

.order-item:last-child {
  border-bottom: 1px solid #f1f5f9;
}

.order-item .item-info {
  flex: 1;
}

.order-item .item-info .item-name {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 6px;
}

.order-item .item-info .item-price {
  font-size: 14px;
  color: #cccccc;
}

.order-item .item-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.order-item .item-controls .quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-item .item-controls .quantity-controls .ant-btn {
  min-width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  background: #404040 !important;
  border: 1px solid #666666 !important;
  color: #ffffff !important;
}

.order-item .item-controls .quantity-controls .ant-btn:hover {
  background: #555555 !important;
  border-color: #777777 !important;
}

.order-item .item-controls .quantity-controls .quantity-display {
  min-width: 40px;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  background: #1a1a1a;
  padding: 8px;
  border-radius: 6px;
}

.order-item .item-controls .item-total {
  margin-left: 16px;
  font-size: 18px;
  font-weight: 700;
  color: #ff6b35;
  min-width: 80px;
  text-align: right;
}

.order-item .item-controls .remove-btn {
  margin-left: 12px;
}

.order-item .item-controls .remove-btn .ant-btn {
  min-width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #d32f2f !important;
  border: 1px solid #f44336 !important;
  color: #ffffff !important;
}

.order-item .item-controls .remove-btn .ant-btn:hover {
  background: #f44336 !important;
  border-color: #ff5722 !important;
}

/* 订单汇总 */
.order-summary {
  padding: 16px 20px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.order-summary .summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #64748b;
}

.order-summary .summary-row:last-child {
  margin-bottom: 0;
}

.order-summary .summary-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
}

/* 操作按钮区域 */
.order-actions {
  padding: 16px 20px;
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
}

.order-actions .ant-space {
  width: 100%;
}

.order-actions .ant-btn {
  font-size: 14px;
  height: 44px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
}

/* 主要按钮样式 */
.order-actions .ant-btn-primary {
  background: #3b82f6 !important;
  border: 1px solid #2563eb !important;
  color: #ffffff !important;
}

.order-actions .ant-btn-primary:hover {
  background: #2563eb !important;
  border-color: #1d4ed8 !important;
  transform: translateY(-1px);
}

.order-actions .ant-btn-primary:disabled {
  background: #9ca3af !important;
  border-color: #6b7280 !important;
  color: #ffffff !important;
  transform: none !important;
}

/* 次要按钮样式 */
.order-actions .ant-btn-default {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  color: #1e293b !important;
}

.order-actions .ant-btn-default:hover {
  background: #f1f5f9 !important;
  border-color: #cbd5e1 !important;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .order-section .order-header {
    padding: 12px 16px 8px 16px;
  }
  
  .order-section .order-header .order-title {
    font-size: 16px;
  }
  
  .order-item {
    padding: 10px 12px;
  }
  
  .order-item .item-info .item-name {
    font-size: 15px;
  }
  
  .order-item .item-info .item-price {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .order-section .order-header {
    padding: 10px 12px 6px 12px;
  }
  
  .order-section .order-header .order-title {
    font-size: 14px;
  }
  
  .order-item {
    padding: 8px 10px;
  }
  
  .order-item .item-info .item-name {
    font-size: 14px;
  }
  
  .order-item .item-info .item-price {
    font-size: 12px;
  }
}
