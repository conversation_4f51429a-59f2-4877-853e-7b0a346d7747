/* 订单列表区域样式 - 高端现代化设计 */
.order-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  color: #e2e8f0;
}

/* 订单标题 */
.order-section .order-header {
  padding: 24px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  background: rgba(26, 32, 44, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.order-section .order-header .order-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #e2e8f0;
  display: flex;
  align-items: center;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 会员信息卡片 */
.member-info-card {
  margin: 20px;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  background: rgba(26, 32, 44, 0.8) !important;
  backdrop-filter: blur(10px);
  border-radius: 16px !important;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.member-info-card .ant-card-body {
  background: transparent !important;
  padding: 20px !important;
}

.member-info-card .member-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.member-info-card .member-info .member-details {
  flex: 1;
}

.member-info-card .member-info .member-details .member-name {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff !important;
  margin-bottom: 6px;
}

.member-info-card .member-info .member-details .member-meta {
  font-size: 14px;
  color: #cccccc !important;
  margin-bottom: 4px;
}

.member-info-card .member-info .member-details .member-balance {
  font-size: 16px;
  color: #4CAF50 !important;
  font-weight: 600;
}

/* 商品列表区域 */
.order-items {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  background: transparent;
}

.order-items .empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #94a3b8;
}

.order-items .empty-cart p {
  margin-top: 16px;
  font-size: 16px;
  color: #94a3b8 !important;
}

/* 订单项 */
.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(26, 32, 44, 0.4);
  backdrop-filter: blur(10px);
  margin: 8px 16px;
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.order-item:hover {
  background: rgba(30, 41, 59, 0.6);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.order-item:last-child {
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.order-item .item-info {
  flex: 1;
}

.order-item .item-info .item-name {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 6px;
}

.order-item .item-info .item-price {
  font-size: 14px;
  color: #cccccc;
}

.order-item .item-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.order-item .item-controls .quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-item .item-controls .quantity-controls .ant-btn {
  min-width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  background: #404040 !important;
  border: 1px solid #666666 !important;
  color: #ffffff !important;
}

.order-item .item-controls .quantity-controls .ant-btn:hover {
  background: #555555 !important;
  border-color: #777777 !important;
}

.order-item .item-controls .quantity-controls .quantity-display {
  min-width: 40px;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  background: #1a1a1a;
  padding: 8px;
  border-radius: 6px;
}

.order-item .item-controls .item-total {
  margin-left: 16px;
  font-size: 18px;
  font-weight: 700;
  color: #ff6b35;
  min-width: 80px;
  text-align: right;
}

.order-item .item-controls .remove-btn {
  margin-left: 12px;
}

.order-item .item-controls .remove-btn .ant-btn {
  min-width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #d32f2f !important;
  border: 1px solid #f44336 !important;
  color: #ffffff !important;
}

.order-item .item-controls .remove-btn .ant-btn:hover {
  background: #f44336 !important;
  border-color: #ff5722 !important;
}

/* 订单汇总 */
.order-summary {
  padding: 24px;
  background: rgba(26, 32, 44, 0.8);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    0 -4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.order-summary .summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  color: #e2e8f0;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

.order-summary .summary-row:last-child {
  margin-bottom: 0;
}

.order-summary .summary-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid rgba(59, 130, 246, 0.3);
  font-size: 22px;
  font-weight: 700;
  color: #e2e8f0;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 操作按钮区域 */
.order-actions {
  padding: 24px;
  background: rgba(26, 32, 44, 0.8);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    0 -4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.order-actions .ant-space {
  width: 100%;
}

.order-actions .ant-btn {
  font-size: 16px;
  height: 60px;
  border-radius: 16px;
  font-weight: 600;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 主要按钮样式 */
.order-actions .ant-btn-primary {
  background: linear-gradient(135deg, #ff6b35 0%, #f59e0b 100%) !important;
  border: 1px solid rgba(245, 101, 53, 0.5) !important;
  color: #ffffff !important;
  box-shadow:
    0 8px 16px rgba(245, 101, 53, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.order-actions .ant-btn-primary:hover {
  background: linear-gradient(135deg, #f59e0b 0%, #ea580c 100%) !important;
  border-color: rgba(245, 101, 53, 0.7) !important;
  transform: translateY(-2px);
  box-shadow:
    0 12px 24px rgba(245, 101, 53, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
}

.order-actions .ant-btn-primary:disabled {
  background: rgba(71, 85, 105, 0.8) !important;
  border-color: rgba(71, 85, 105, 0.5) !important;
  color: #94a3b8 !important;
  box-shadow: none !important;
  transform: none !important;
}

/* 次要按钮样式 */
.order-actions .ant-btn-default {
  background: rgba(30, 41, 59, 0.8) !important;
  border: 1px solid rgba(59, 130, 246, 0.4) !important;
  color: #e2e8f0 !important;
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.order-actions .ant-btn-default:hover {
  background: rgba(30, 41, 59, 0.9) !important;
  border-color: rgba(59, 130, 246, 0.6) !important;
  transform: translateY(-2px);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .order-section .order-header {
    padding: 12px 16px 8px 16px;
  }
  
  .order-section .order-header .order-title {
    font-size: 16px;
  }
  
  .order-item {
    padding: 10px 12px;
  }
  
  .order-item .item-info .item-name {
    font-size: 15px;
  }
  
  .order-item .item-info .item-price {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .order-section .order-header {
    padding: 10px 12px 6px 12px;
  }
  
  .order-section .order-header .order-title {
    font-size: 14px;
  }
  
  .order-item {
    padding: 8px 10px;
  }
  
  .order-item .item-info .item-name {
    font-size: 14px;
  }
  
  .order-item .item-info .item-price {
    font-size: 12px;
  }
}
