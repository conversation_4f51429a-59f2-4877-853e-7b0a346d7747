/* 订单列表区域样式 - 收银机风格 */
.order-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

/* 订单标题 */
.order-section .order-header {
  padding: 16px 20px 12px 20px;
  border-bottom: 2px solid #1976D2;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-section .order-header .order-title {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #1976D2;
  display: flex;
  align-items: center;
}

/* 会员信息卡片 */
.member-info-card {
  margin: 12px 16px;
  border: 1px solid #e8e8e8;
}

.member-info-card .member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-info-card .member-info .member-details {
  flex: 1;
}

.member-info-card .member-info .member-details .member-name {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 4px;
}

.member-info-card .member-info .member-details .member-meta {
  font-size: 12px;
  color: #666666;
  margin-bottom: 2px;
}

.member-info-card .member-info .member-details .member-balance {
  font-size: 14px;
  color: #1976D2;
  font-weight: 500;
}

/* 商品列表区域 */
.order-items {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.order-items .empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999999;
}

.order-items .empty-cart p {
  margin-top: 12px;
  font-size: 14px;
}

/* 订单项 */
.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.order-item:hover {
  background-color: #f8f9fa;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item .item-info {
  flex: 1;
}

.order-item .item-info .item-name {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 4px;
}

.order-item .item-info .item-price {
  font-size: 14px;
  color: #666666;
}

.order-item .item-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-item .item-controls .quantity-controls {
  display: flex;
  align-items: center;
  gap: 6px;
}

.order-item .item-controls .quantity-controls .ant-btn {
  min-width: 28px;
  height: 28px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.order-item .item-controls .quantity-controls .quantity-display {
  min-width: 32px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #333333;
}

.order-item .item-controls .item-total {
  margin-left: 12px;
  font-size: 16px;
  font-weight: bold;
  color: #f5222d;
  min-width: 60px;
  text-align: right;
}

.order-item .item-controls .remove-btn {
  margin-left: 8px;
}

.order-item .item-controls .remove-btn .ant-btn {
  min-width: 28px;
  height: 28px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 订单汇总 */
.order-summary {
  padding: 16px;
  background: #f8f9fa;
  border-top: 1px solid #e8e8e8;
}

.order-summary .summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.order-summary .summary-row:last-child {
  margin-bottom: 0;
}

.order-summary .summary-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #d9d9d9;
  font-size: 16px;
  font-weight: bold;
}

/* 操作按钮区域 */
.order-actions {
  padding: 16px;
  background: #ffffff;
  border-top: 1px solid #e8e8e8;
}

.order-actions .ant-space {
  width: 100%;
}

.order-actions .ant-btn {
  font-size: 14px;
  height: 40px;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .order-section .order-header {
    padding: 12px 16px 8px 16px;
  }
  
  .order-section .order-header .order-title {
    font-size: 16px;
  }
  
  .order-item {
    padding: 10px 12px;
  }
  
  .order-item .item-info .item-name {
    font-size: 15px;
  }
  
  .order-item .item-info .item-price {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .order-section .order-header {
    padding: 10px 12px 6px 12px;
  }
  
  .order-section .order-header .order-title {
    font-size: 14px;
  }
  
  .order-item {
    padding: 8px 10px;
  }
  
  .order-item .item-info .item-name {
    font-size: 14px;
  }
  
  .order-item .item-info .item-price {
    font-size: 12px;
  }
}
