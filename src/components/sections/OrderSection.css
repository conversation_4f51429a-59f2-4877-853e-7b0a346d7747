/* 订单列表区域样式 - 1:1还原设计稿 */
.order-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;
  color: #ffffff;
}

/* 订单标题 */
.order-section .order-header {
  padding: 20px;
  border-bottom: 1px solid #333333;
  background: #1e1e1e;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-section .order-header .order-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
}

/* 会员信息卡片 */
.member-info-card {
  margin: 20px;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
  background: rgba(26, 32, 44, 0.8) !important;
  backdrop-filter: blur(10px);
  border-radius: 16px !important;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.member-info-card .ant-card-body {
  background: transparent !important;
  padding: 20px !important;
}

.member-info-card .member-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.member-info-card .member-info .member-details {
  flex: 1;
}

.member-info-card .member-info .member-details .member-name {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff !important;
  margin-bottom: 6px;
}

.member-info-card .member-info .member-details .member-meta {
  font-size: 14px;
  color: #cccccc !important;
  margin-bottom: 4px;
}

.member-info-card .member-info .member-details .member-balance {
  font-size: 16px;
  color: #4CAF50 !important;
  font-weight: 600;
}

/* 商品列表区域 */
.order-items {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background: #1e1e1e;
}

.order-items .empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #888888;
}

.order-items .empty-cart p {
  margin-top: 16px;
  font-size: 16px;
  color: #888888 !important;
}

/* 订单项 */
.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #333333;
  transition: all 0.2s ease;
  background: #1e1e1e;
  margin: 0;
  border-radius: 0;
  border: none;
}

.order-item:hover {
  background: #2a2a2a;
}

.order-item:last-child {
  border-bottom: 1px solid #333333;
}

.order-item .item-info {
  flex: 1;
}

.order-item .item-info .item-name {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 6px;
}

.order-item .item-info .item-price {
  font-size: 14px;
  color: #ff6b35;
  font-weight: 700;
}

.order-item .item-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.order-item .item-controls .quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-item .item-controls .quantity-controls .ant-btn {
  min-width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  background: #404040 !important;
  border: 1px solid #666666 !important;
  color: #ffffff !important;
}

.order-item .item-controls .quantity-controls .ant-btn:hover {
  background: #555555 !important;
  border-color: #777777 !important;
}

.order-item .item-controls .quantity-controls .quantity-display {
  min-width: 40px;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  background: #1a1a1a;
  padding: 8px;
  border-radius: 6px;
}

.order-item .item-controls .item-total {
  margin-left: 16px;
  font-size: 18px;
  font-weight: 700;
  color: #ff6b35;
  min-width: 80px;
  text-align: right;
}

.order-item .item-controls .remove-btn {
  margin-left: 12px;
}

.order-item .item-controls .remove-btn .ant-btn {
  min-width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #d32f2f !important;
  border: 1px solid #f44336 !important;
  color: #ffffff !important;
}

.order-item .item-controls .remove-btn .ant-btn:hover {
  background: #f44336 !important;
  border-color: #ff5722 !important;
}

/* 订单汇总 */
.order-summary {
  padding: 20px;
  background: #1e1e1e;
  border-top: 1px solid #333333;
}

.order-summary .summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: #ffffff;
}

.order-summary .summary-row:last-child {
  margin-bottom: 0;
}

.order-summary .summary-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #333333;
  font-size: 20px;
  font-weight: 700;
  color: #ff6b35;
}

/* 操作按钮区域 */
.order-actions {
  padding: 20px;
  background: #1e1e1e;
  border-top: 1px solid #333333;
}

.order-actions .ant-space {
  width: 100%;
}

.order-actions .ant-btn {
  font-size: 16px;
  height: 50px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
}

/* 主要按钮样式 - 橙色收银按钮 */
.order-actions .ant-btn-primary {
  background: #ff6b35 !important;
  border: 1px solid #ff6b35 !important;
  color: #ffffff !important;
}

.order-actions .ant-btn-primary:hover {
  background: #ff8a65 !important;
  border-color: #ff8a65 !important;
  transform: translateY(-1px);
}

.order-actions .ant-btn-primary:disabled {
  background: #666666 !important;
  border-color: #666666 !important;
  color: #888888 !important;
  transform: none !important;
}

/* 次要按钮样式 */
.order-actions .ant-btn-default {
  background: #333333 !important;
  border: 1px solid #555555 !important;
  color: #ffffff !important;
}

.order-actions .ant-btn-default:hover {
  background: #444444 !important;
  border-color: #666666 !important;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .order-section .order-header {
    padding: 12px 16px 8px 16px;
  }
  
  .order-section .order-header .order-title {
    font-size: 16px;
  }
  
  .order-item {
    padding: 10px 12px;
  }
  
  .order-item .item-info .item-name {
    font-size: 15px;
  }
  
  .order-item .item-info .item-price {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .order-section .order-header {
    padding: 10px 12px 6px 12px;
  }
  
  .order-section .order-header .order-title {
    font-size: 14px;
  }
  
  .order-item {
    padding: 8px 10px;
  }
  
  .order-item .item-info .item-name {
    font-size: 14px;
  }
  
  .order-item .item-info .item-price {
    font-size: 12px;
  }
}
