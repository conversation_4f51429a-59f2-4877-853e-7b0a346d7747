/* 左侧边栏样式 - 1:1还原设计稿 */
.sidebar {
  width: 80px;
  height: 100vh;
  background: #1a1a1a;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  border-right: 1px solid #333333;
}

/* 用户头像区域 */
.sidebar-user {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

.sidebar-user .user-avatar {
  background: #4a90e2;
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 8px;
}

.sidebar-user .user-info {
  text-align: center;
}

.sidebar-user .user-name {
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 2px;
}

.sidebar-user .user-role {
  color: #888888;
  font-size: 10px;
}

/* 导航菜单 */
.sidebar-menu {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  padding: 0 10px;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #888888;
}

.menu-item:hover {
  background: #333333;
  color: #ffffff;
}

.menu-item.active {
  background: #4a90e2;
  color: #ffffff;
}

.menu-item .menu-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.menu-item .menu-text {
  font-size: 10px;
  font-weight: 500;
  text-align: center;
}

/* 底部设置 */
.sidebar-bottom {
  width: 100%;
  padding: 0 10px;
}

.sidebar-bottom .menu-item {
  margin-bottom: 0;
}
