/* 系统状态栏样式 - 现代化浅色设计 */
.status-bar {
  height: 64px;
  background: #ffffff;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e2e8f0;
  position: relative;
  z-index: 100;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 左侧信息区域 */
.status-bar-left {
  display: flex;
  align-items: center;
  gap: 32px;
}

/* 右侧操作区域 */
.status-bar-right {
  display: flex;
  align-items: center;
  gap: 24px;
}

/* 状态项通用样式 */
.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1e293b;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 40px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

.status-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.status-item:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 状态图标 */
.status-icon {
  font-size: 20px;
  color: #ffffff;
}

.status-icon.syncing {
  animation: rotate 2s linear infinite;
}

/* 旋转动画（数据同步） */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 时间显示 */
.time-display {
  background: #3b82f6 !important;
  border: 1px solid #2563eb !important;
  padding: 8px 16px;
  font-weight: 600;
  border-radius: 8px;
}

.time-display .elder-text {
  color: #ffffff !important;
  font-size: 14px !important;
  font-weight: 600 !important;
}

/* 同步状态 */
.sync-status {
  background: #10b981 !important;
  border: 1px solid #059669 !important;
  border-radius: 8px;
}

.sync-status .elder-text {
  color: #ffffff !important;
  font-size: 14px !important;
  font-weight: 600 !important;
}

/* 网络状态 */
.network-status {
  background: #6366f1 !important;
  border: 1px solid #4f46e5 !important;
  border-radius: 8px;
}

.network-status .elder-text {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #ffffff !important;
}

/* 用户信息 */
.user-info {
  background: #8b5cf6 !important;
  border: 1px solid #7c3aed !important;
  min-width: 160px;
  border-radius: 8px;
}

.user-info:hover {
  background: #7c3aed !important;
  border-color: #6d28d9 !important;
}

.user-info .user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-info .user-details .elder-text {
  color: #ffffff !important;
  line-height: 1.3;
}

.user-info .user-details .elder-text.elder-text-base {
  font-size: 18px !important;
  font-weight: 700 !important;
}

.user-info .user-details .elder-text.elder-text-xs {
  font-size: 14px !important;
  font-weight: 500 !important;
  opacity: 0.9;
}

/* 状态按钮 */
.status-button {
  color: #e2e8f0 !important;
  border: 1px solid rgba(59, 130, 246, 0.4) !important;
  background: rgba(26, 32, 44, 0.8) !important;
  min-width: 52px !important;
  min-height: 52px !important;
  border-radius: 16px !important;
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-button:hover {
  background: rgba(30, 41, 59, 0.9) !important;
  border-color: rgba(59, 130, 246, 0.6) !important;
  transform: translateY(-2px);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

.status-button:focus {
  outline: 2px solid rgba(59, 130, 246, 0.8);
  outline-offset: 2px;
}

.status-button .anticon {
  font-size: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .status-bar {
    padding: 0 16px;
    height: 55px;
  }

  .status-bar-left {
    gap: 24px;
  }

  .status-bar-right {
    gap: 16px;
  }

  .status-item {
    padding: 6px 10px;
    gap: 6px;
  }

  .time-display {
    padding: 8px 12px;
  }

  .time-display .elder-text {
    font-size: 16px;
  }

  .user-info {
    min-width: 140px;
    padding: 6px 12px;
  }
}

@media (max-width: 768px) {
  .status-bar {
    padding: 0 12px;
    height: 50px;
  }

  .status-bar-left {
    gap: 16px;
  }

  .status-bar-right {
    gap: 12px;
  }

  .status-item {
    padding: 4px 8px;
    gap: 4px;
    min-height: 40px;
  }

  .status-icon {
    font-size: 16px;
  }

  .time-display {
    padding: 6px 10px;
  }

  .time-display .elder-text {
    font-size: 14px;
  }

  .sync-status .elder-text {
    font-size: 12px;
  }

  .network-status .elder-text {
    font-size: 12px;
  }

  .user-info {
    min-width: 120px;
    padding: 4px 10px;
  }

  .user-info .user-details .elder-text.elder-text-base {
    font-size: 14px;
  }

  .user-info .user-details .elder-text.elder-text-xs {
    font-size: 10px;
  }

  .status-button {
    min-width: 40px;
    min-height: 40px;
  }

  .status-button .anticon {
    font-size: 16px;
  }
}