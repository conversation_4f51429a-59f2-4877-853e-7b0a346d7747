/* 系统状态栏样式 - 专业收银机风格 */
.status-bar {
  height: 60px;
  background: #1a1a1a;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 3px solid #ff6b35;
  position: relative;
  z-index: 100;
}

/* 左侧信息区域 */
.status-bar-left {
  display: flex;
  align-items: center;
  gap: 32px;
}

/* 右侧操作区域 */
.status-bar-right {
  display: flex;
  align-items: center;
  gap: 24px;
}

/* 状态项通用样式 */
.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #ffffff;
  padding: 12px 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 48px;
  background: #2d2d2d;
  border: 2px solid #404040;
}

.status-item:hover {
  background-color: #3a3a3a;
  border-color: #ff6b35;
  transform: translateY(-2px);
}

.status-item:focus {
  outline: 2px solid #ff6b35;
  outline-offset: 2px;
}

/* 状态图标 */
.status-icon {
  font-size: 20px;
  color: #ffffff;
}

.status-icon.syncing {
  animation: rotate 2s linear infinite;
}

/* 旋转动画（数据同步） */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 时间显示 */
.time-display {
  background: #ff6b35 !important;
  border: 2px solid #ff8a65 !important;
  padding: 12px 20px;
  font-weight: 700;
  letter-spacing: 1px;
}

.time-display .elder-text {
  color: #ffffff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 同步状态 */
.sync-status {
  background: #4CAF50 !important;
  border: 2px solid #66BB6A !important;
}

.sync-status .elder-text {
  color: #ffffff !important;
  font-size: 16px !important;
  font-weight: 600 !important;
}

/* 网络状态 */
.network-status {
  background: #2196F3 !important;
  border: 2px solid #42A5F5 !important;
}

.network-status .elder-text {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #ffffff !important;
}

/* 用户信息 */
.user-info {
  background: #9C27B0 !important;
  border: 2px solid #BA68C8 !important;
  min-width: 180px;
}

.user-info:hover {
  background: #AD42C4 !important;
  border-color: #CE93D8 !important;
}

.user-info .user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-info .user-details .elder-text {
  color: #ffffff !important;
  line-height: 1.3;
}

.user-info .user-details .elder-text.elder-text-base {
  font-size: 18px !important;
  font-weight: 700 !important;
}

.user-info .user-details .elder-text.elder-text-xs {
  font-size: 14px !important;
  font-weight: 500 !important;
  opacity: 0.9;
}

/* 状态按钮 */
.status-button {
  color: #ffffff !important;
  border: 2px solid #666666 !important;
  background: #404040 !important;
  min-width: 48px !important;
  min-height: 48px !important;
  border-radius: 12px !important;
}

.status-button:hover {
  background: #555555 !important;
  border-color: #ff6b35 !important;
  transform: translateY(-2px);
}

.status-button:focus {
  outline: 2px solid #ff6b35;
  outline-offset: 2px;
}

.status-button .anticon {
  font-size: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .status-bar {
    padding: 0 16px;
    height: 55px;
  }

  .status-bar-left {
    gap: 24px;
  }

  .status-bar-right {
    gap: 16px;
  }

  .status-item {
    padding: 6px 10px;
    gap: 6px;
  }

  .time-display {
    padding: 8px 12px;
  }

  .time-display .elder-text {
    font-size: 16px;
  }

  .user-info {
    min-width: 140px;
    padding: 6px 12px;
  }
}

@media (max-width: 768px) {
  .status-bar {
    padding: 0 12px;
    height: 50px;
  }

  .status-bar-left {
    gap: 16px;
  }

  .status-bar-right {
    gap: 12px;
  }

  .status-item {
    padding: 4px 8px;
    gap: 4px;
    min-height: 40px;
  }

  .status-icon {
    font-size: 16px;
  }

  .time-display {
    padding: 6px 10px;
  }

  .time-display .elder-text {
    font-size: 14px;
  }

  .sync-status .elder-text {
    font-size: 12px;
  }

  .network-status .elder-text {
    font-size: 12px;
  }

  .user-info {
    min-width: 120px;
    padding: 4px 10px;
  }

  .user-info .user-details .elder-text.elder-text-base {
    font-size: 14px;
  }

  .user-info .user-details .elder-text.elder-text-xs {
    font-size: 10px;
  }

  .status-button {
    min-width: 40px;
    min-height: 40px;
  }

  .status-button .anticon {
    font-size: 16px;
  }
}