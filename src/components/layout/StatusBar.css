/* 系统状态栏样式 - 高端现代化设计 */
.status-bar {
  height: 70px;
  background: rgba(15, 20, 25, 0.98);
  backdrop-filter: blur(20px);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  position: relative;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

/* 渐变装饰线 */
.status-bar::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    rgba(59, 130, 246, 0.8) 0%,
    rgba(249, 115, 22, 0.8) 50%,
    rgba(34, 197, 94, 0.8) 100%);
}

/* 左侧信息区域 */
.status-bar-left {
  display: flex;
  align-items: center;
  gap: 32px;
}

/* 右侧操作区域 */
.status-bar-right {
  display: flex;
  align-items: center;
  gap: 24px;
}

/* 状态项通用样式 */
.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #e2e8f0;
  padding: 12px 20px;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  min-height: 52px;
  background: rgba(26, 32, 44, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.status-item:hover {
  background: rgba(30, 41, 59, 0.9);
  border-color: rgba(59, 130, 246, 0.6);
  transform: translateY(-2px);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.status-item:focus {
  outline: 2px solid rgba(59, 130, 246, 0.8);
  outline-offset: 2px;
}

/* 状态图标 */
.status-icon {
  font-size: 20px;
  color: #ffffff;
}

.status-icon.syncing {
  animation: rotate 2s linear infinite;
}

/* 旋转动画（数据同步） */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 时间显示 */
.time-display {
  background: linear-gradient(135deg, #ff6b35 0%, #f59e0b 100%) !important;
  border: 1px solid rgba(245, 101, 53, 0.5) !important;
  padding: 12px 24px;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow:
    0 8px 16px rgba(245, 101, 53, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.time-display .elder-text {
  color: #ffffff !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 同步状态 */
.sync-status {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%) !important;
  border: 1px solid rgba(34, 197, 94, 0.5) !important;
  box-shadow:
    0 8px 16px rgba(34, 197, 94, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.sync-status .elder-text {
  color: #ffffff !important;
  font-size: 15px !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 网络状态 */
.network-status {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  border: 1px solid rgba(59, 130, 246, 0.5) !important;
  box-shadow:
    0 8px 16px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.network-status .elder-text {
  font-size: 15px !important;
  font-weight: 600 !important;
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 用户信息 */
.user-info {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
  border: 1px solid rgba(139, 92, 246, 0.5) !important;
  min-width: 200px;
  box-shadow:
    0 8px 16px rgba(139, 92, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.user-info:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%) !important;
  border-color: rgba(139, 92, 246, 0.7) !important;
  box-shadow:
    0 12px 24px rgba(139, 92, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
}

.user-info .user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-info .user-details .elder-text {
  color: #ffffff !important;
  line-height: 1.3;
}

.user-info .user-details .elder-text.elder-text-base {
  font-size: 18px !important;
  font-weight: 700 !important;
}

.user-info .user-details .elder-text.elder-text-xs {
  font-size: 14px !important;
  font-weight: 500 !important;
  opacity: 0.9;
}

/* 状态按钮 */
.status-button {
  color: #e2e8f0 !important;
  border: 1px solid rgba(59, 130, 246, 0.4) !important;
  background: rgba(26, 32, 44, 0.8) !important;
  min-width: 52px !important;
  min-height: 52px !important;
  border-radius: 16px !important;
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-button:hover {
  background: rgba(30, 41, 59, 0.9) !important;
  border-color: rgba(59, 130, 246, 0.6) !important;
  transform: translateY(-2px);
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

.status-button:focus {
  outline: 2px solid rgba(59, 130, 246, 0.8);
  outline-offset: 2px;
}

.status-button .anticon {
  font-size: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .status-bar {
    padding: 0 16px;
    height: 55px;
  }

  .status-bar-left {
    gap: 24px;
  }

  .status-bar-right {
    gap: 16px;
  }

  .status-item {
    padding: 6px 10px;
    gap: 6px;
  }

  .time-display {
    padding: 8px 12px;
  }

  .time-display .elder-text {
    font-size: 16px;
  }

  .user-info {
    min-width: 140px;
    padding: 6px 12px;
  }
}

@media (max-width: 768px) {
  .status-bar {
    padding: 0 12px;
    height: 50px;
  }

  .status-bar-left {
    gap: 16px;
  }

  .status-bar-right {
    gap: 12px;
  }

  .status-item {
    padding: 4px 8px;
    gap: 4px;
    min-height: 40px;
  }

  .status-icon {
    font-size: 16px;
  }

  .time-display {
    padding: 6px 10px;
  }

  .time-display .elder-text {
    font-size: 14px;
  }

  .sync-status .elder-text {
    font-size: 12px;
  }

  .network-status .elder-text {
    font-size: 12px;
  }

  .user-info {
    min-width: 120px;
    padding: 4px 10px;
  }

  .user-info .user-details .elder-text.elder-text-base {
    font-size: 14px;
  }

  .user-info .user-details .elder-text.elder-text-xs {
    font-size: 10px;
  }

  .status-button {
    min-width: 40px;
    min-height: 40px;
  }

  .status-button .anticon {
    font-size: 16px;
  }
}