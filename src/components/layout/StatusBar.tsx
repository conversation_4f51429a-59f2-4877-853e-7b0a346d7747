import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Tooltip, Badge, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import {
  ClockCircleOutlined,
  UserOutlined,
  WifiOutlined,
  LogoutOutlined,
  SettingOutlined,
  DatabaseOutlined,
  BellOutlined
} from '@ant-design/icons';
import './StatusBar.less';

/**
 * 系统状态栏组件 - 简化版本
 */
const StatusBar: React.FC = () => {
  // 本地状态
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 网络状态检测
  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      weekday: 'short'
    });
  };

  // 用户菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true,
    },
  ];

  return (
    <div className="status-bar">
      {/* 左侧：系统信息 */}
      <div className="status-bar-left">
        {/* 时间显示 */}
        <div className="status-item time-display">
          <ClockCircleOutlined className="status-icon" />
          <span className="elder-text elder-text-base elder-text-bold">
            {formatTime(currentTime)}
          </span>
        </div>
        
        {/* 数据同步状态 */}
        <Tooltip title="数据已同步">
          <div className="status-item sync-status">
            <DatabaseOutlined 
              className="status-icon"
              style={{ color: '#52c41a' }}
            />
            <span className="elder-text elder-text-sm">
              已同步
            </span>
          </div>
        </Tooltip>
      </div>

      {/* 右侧：用户信息和操作 */}
      <div className="status-bar-right">
        {/* 通知 */}
        <Tooltip title="系统通知">
          <Badge count={0} size="small" offset={[-2, 2]}>
            <Button 
              type="text" 
              icon={<BellOutlined />}
              className="status-button"
              aria-label="系统通知"
            />
          </Badge>
        </Tooltip>

        {/* 网络状态 */}
        <Tooltip title={`网络状态：${isOnline ? '在线' : '离线'}`}>
          <div className="status-item network-status">
            <WifiOutlined style={{ color: isOnline ? '#52c41a' : '#f5222d' }} />
            <span 
              className="elder-text elder-text-sm"
              style={{ color: isOnline ? '#52c41a' : '#f5222d' }}
            >
              {isOnline ? '在线' : '离线'}
            </span>
          </div>
        </Tooltip>

        {/* 用户信息 */}
        <Dropdown 
          menu={{ items: userMenuItems }} 
          placement="bottomRight"
          trigger={['click']}
        >
          <div className="status-item user-info" role="button" tabIndex={0}>
            <UserOutlined className="status-icon" />
            <div className="user-details">
              <span className="elder-text elder-text-base elder-text-bold">
                收银员
              </span>
              <span className="elder-text elder-text-xs elder-text-secondary">
                在线
              </span>
            </div>
          </div>
        </Dropdown>
      </div>
    </div>
  );
};

export default StatusBar; 