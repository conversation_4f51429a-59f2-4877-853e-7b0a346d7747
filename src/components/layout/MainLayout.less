// 主界面布局样式
.main-layout {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-color: #f5f5f5;
  
  &.elder-layout {
    font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
  }
}

// 主内容区域
.main-content {
  padding: 0;
  height: calc(100vh - 60px); // 减去状态栏高度
  background-color: #f5f5f5;
  
  .main-content-wrapper {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
}

// 区域通用样式
.main-section {
  background-color: #ffffff;
  border: 2px solid transparent;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  &:focus {
    outline: 3px solid #1677ff;
    outline-offset: 2px;
    border-color: #1677ff;
  }
  
  // 激活状态
  &-active {
    border-color: #1677ff;
    box-shadow: 0 4px 16px rgba(22, 119, 255, 0.2);
    transform: translateY(-2px);
    
    .section-header {
      background-color: #e6f4ff;
      color: #1677ff;
    }
  }
}

// 区域头部
.section-header {
  padding: 16px 24px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  flex-shrink: 0;
  
  h3 {
    margin: 0;
    color: #262626;
    font-size: 24px;
    font-weight: 600;
    line-height: 1.4;
  }
}

// 区域内容
.section-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  overflow-x: hidden;
  
  // 滚动条样式适老化
  &::-webkit-scrollbar {
    width: 12px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #bfbfbf;
    border-radius: 6px;
    border: 2px solid #f5f5f5;
    
    &:hover {
      background: #8c8c8c;
    }
  }
}

// 分隔线样式
.main-divider {
  width: 1px !important;
  height: 100%;
  margin: 0;
  background-color: #d9d9d9;
  border: none;
  
  &::before {
    display: none;
  }
}

// 快捷键提示
.shortcut-hints {
  position: fixed;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  line-height: 1.4;
  z-index: 1000;
  transition: opacity 0.3s ease;
  
  &:hover {
    opacity: 0.9;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content-wrapper {
    grid-template-columns: 35% 35% 30% !important;
  }
  
  .section-header {
    padding: 12px 16px;
    
    h3 {
      font-size: 22px;
    }
  }
  
  .section-content {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .main-layout {
    height: 100vh;
  }
  
  .main-content {
    height: calc(100vh - 50px);
  }
  
  .main-content-wrapper {
    display: flex !important;
    flex-direction: column;
    gap: 8px !important;
  }
  
  .main-section {
    flex: 1;
    min-height: 200px;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .section-header {
    padding: 8px 12px;
    
    h3 {
      font-size: 20px;
    }
  }
  
  .section-content {
    padding: 12px;
  }
  
  .shortcut-hints {
    bottom: 4px;
    font-size: 12px;
    padding: 6px 12px;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .main-section {
    border: 3px solid #000000;
    
    &-active {
      border-color: #0050b3;
      background-color: #e6f4ff;
    }
  }
  
  .section-header {
    background-color: #ffffff;
    border-bottom: 2px solid #000000;
    
    h3 {
      color: #000000;
    }
  }
  
  .main-divider {
    background-color: #000000;
    width: 2px !important;
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  .main-section {
    transition: none;
    
    &:hover {
      transform: none;
    }
    
    &-active {
      transform: none;
    }
  }
  
  .section-header {
    transition: none;
  }
  
  .shortcut-hints {
    transition: none;
  }
}

// 打印样式
@media print {
  .main-layout {
    height: auto;
    overflow: visible;
  }
  
  .main-content {
    height: auto;
  }
  
  .main-section {
    box-shadow: none;
    border: 1px solid #000000;
    break-inside: avoid;
    margin-bottom: 20px;
  }
  
  .shortcut-hints {
    display: none;
  }
} 