import React from 'react';
import { Avatar } from 'antd';
import './Sidebar.css';

/**
 * 左侧边栏组件 - 1:1还原设计稿
 */
const Sidebar: React.FC = () => {
  return (
    <div className="sidebar">
      {/* 用户头像区域 */}
      <div className="sidebar-user">
        <Avatar size={40} className="user-avatar">
          <span>申</span>
        </Avatar>
        <div className="user-info">
          <div className="user-name">上海申友</div>
          <div className="user-role">收银员</div>
        </div>
      </div>

      {/* 导航菜单 */}
      <div className="sidebar-menu">
        <div className="menu-item active">
          <div className="menu-icon">🏠</div>
          <div className="menu-text">首页</div>
        </div>

        <div className="menu-item">
          <div className="menu-icon">📋</div>
          <div className="menu-text">订单</div>
        </div>

        <div className="menu-item">
          <div className="menu-icon">🛒</div>
          <div className="menu-text">外卖</div>
        </div>

        <div className="menu-item">
          <div className="menu-icon">👥</div>
          <div className="menu-text">会员</div>
        </div>

        <div className="menu-item">
          <div className="menu-icon">💰</div>
          <div className="menu-text">消费</div>
        </div>
      </div>

      {/* 底部设置 */}
      <div className="sidebar-bottom">
        <div className="menu-item">
          <div className="menu-icon">⚙️</div>
          <div className="menu-text">设置</div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
