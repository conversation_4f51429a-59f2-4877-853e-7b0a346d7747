import React, { useEffect, useState, useCallback } from 'react';
import { Layout, Divider, message } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { ConfigProvider } from 'antd';
import { elderTheme } from '../../styles/theme';
import { useAppSelector } from '../../hooks/redux';
import StatusBar from './StatusBar';
import OrderSection from '../sections/OrderSection';
import AISection from '../sections/AISection';
import MenuSection from '../sections/MenuSection';
import './MainLayout.less';

const { Content } = Layout;

// 布局配置类型
interface LayoutConfig {
  orderWidth: number;
  aiWidth: number;
  menuWidth: number;
  collapsed: boolean;
}

// 默认布局配置
const DEFAULT_LAYOUT: LayoutConfig = {
  orderWidth: 30,
  aiWidth: 40,
  menuWidth: 30,
  collapsed: false,
};

// 本地存储键名
const LAYOUT_STORAGE_KEY = 'elder_cashier_layout';

/**
 * 主界面布局组件
 * 
 * 功能特性：
 * - 三区域响应式布局（订单30% + AI识别40% + 菜品30%）
 * - 系统状态栏
 * - 快捷键支持
 * - 布局配置本地存储
 * - 适老化设计
 * - 无障碍访问支持
 */
const MainLayout: React.FC = () => {
  const dispatch = useDispatch();
  
  // 获取系统状态
  // const { theme } = useAppSelector(state => state.system);
  // const { isOnline } = useAppSelector(state => state.system);
  
  // 布局状态
  const [layout, setLayout] = useState<LayoutConfig>(DEFAULT_LAYOUT);
  const [isResizing, setIsResizing] = useState(false);
  const [activeSection, setActiveSection] = useState<'order' | 'ai' | 'menu'>('ai');

  // 加载保存的布局配置
  useEffect(() => {
    try {
      const savedLayout = localStorage.getItem(LAYOUT_STORAGE_KEY);
      if (savedLayout) {
        const parsedLayout = JSON.parse(savedLayout);
        setLayout({ ...DEFAULT_LAYOUT, ...parsedLayout });
      }
    } catch (error) {
      console.warn('Failed to load layout config:', error);
    }
  }, []);

  // 保存布局配置
  const saveLayout = useCallback((newLayout: LayoutConfig) => {
    try {
      localStorage.setItem(LAYOUT_STORAGE_KEY, JSON.stringify(newLayout));
    } catch (error) {
      console.warn('Failed to save layout config:', error);
    }
  }, []);

  // 更新布局配置
  const updateLayout = useCallback((updates: Partial<LayoutConfig>) => {
    const newLayout = { ...layout, ...updates };
    setLayout(newLayout);
    saveLayout(newLayout);
  }, [layout, saveLayout]);

  // 重置布局
  const resetLayout = useCallback(() => {
    setLayout(DEFAULT_LAYOUT);
    saveLayout(DEFAULT_LAYOUT);
    message.success('布局已重置为默认设置');
  }, [saveLayout]);

  // 快捷键处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 防止在输入框中触发快捷键
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA') {
        return;
      }

      switch (event.key) {
        case 'F1':
          event.preventDefault();
          setActiveSection('order');
          break;
        case 'F2':
          event.preventDefault();
          setActiveSection('ai');
          break;
        case 'F3':
          event.preventDefault();
          setActiveSection('menu');
          break;
        case 'F4':
          event.preventDefault();
          resetLayout();
          break;
        case 'Escape':
          event.preventDefault();
          setActiveSection('ai'); // 默认聚焦AI区域
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [resetLayout]);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      // 在小屏幕上调整布局
      const width = window.innerWidth;
      if (width < 1200) {
        updateLayout({
          orderWidth: 35,
          aiWidth: 35,
          menuWidth: 30,
        });
      } else if (width >= 1200 && layout.orderWidth !== DEFAULT_LAYOUT.orderWidth) {
        updateLayout(DEFAULT_LAYOUT);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // 初始化时执行一次

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [layout.orderWidth, updateLayout]);

  // 区域聚焦样式
  const getSectionClassName = (section: 'order' | 'ai' | 'menu') => {
    return `main-section ${activeSection === section ? 'main-section-active' : ''}`;
  };

  return (
    <ConfigProvider theme={elderTheme}>
      <Layout className="main-layout elder-layout">
        {/* 系统状态栏 */}
        <StatusBar />
        
        {/* 主内容区域 */}
        <Content className="main-content">
          <div 
            className="main-content-wrapper"
            style={{
              display: 'grid',
              gridTemplateColumns: `${layout.orderWidth}% ${layout.aiWidth}% ${layout.menuWidth}%`,
              height: '100%',
              gap: '1px',
              backgroundColor: '#f0f0f0',
            }}
          >
            {/* 订单列表区域 */}
            <div 
              className={getSectionClassName('order')}
              onClick={() => setActiveSection('order')}
              role="region"
              aria-label="订单列表区域，快捷键F1"
              tabIndex={0}
            >
              <div className="section-header">
                <h3 className="elder-text elder-text-lg elder-text-bold">
                  订单列表 (F1)
                </h3>
              </div>
              <div className="section-content">
                <OrderSection />
              </div>
            </div>

            {/* 分隔线 */}
            <Divider type="vertical" className="main-divider" />

            {/* AI识别区域 */}
            <div 
              className={getSectionClassName('ai')}
              onClick={() => setActiveSection('ai')}
              role="region"
              aria-label="AI菜品识别区域，快捷键F2"
              tabIndex={0}
            >
              <div className="section-header">
                <h3 className="elder-text elder-text-lg elder-text-bold">
                  AI识别 (F2)
                </h3>
              </div>
              <div className="section-content">
                <AISection />
              </div>
            </div>

            {/* 分隔线 */}
            <Divider type="vertical" className="main-divider" />

            {/* 菜品展示区域 */}
            <div 
              className={getSectionClassName('menu')}
              onClick={() => setActiveSection('menu')}
              role="region"
              aria-label="菜品展示区域，快捷键F3"
              tabIndex={0}
            >
              <div className="section-header">
                <h3 className="elder-text elder-text-lg elder-text-bold">
                  菜品展示 (F3)
                </h3>
              </div>
              <div className="section-content">
                <MenuSection />
              </div>
            </div>
          </div>
        </Content>

        {/* 快捷键提示 */}
        <div className="shortcut-hints" aria-live="polite">
          <div className="elder-text elder-text-sm elder-text-secondary">
            快捷键：F1-订单 | F2-AI识别 | F3-菜品 | F4-重置布局 | ESC-回到AI区域
          </div>
        </div>
      </Layout>
    </ConfigProvider>
  );
};

export default MainLayout; 