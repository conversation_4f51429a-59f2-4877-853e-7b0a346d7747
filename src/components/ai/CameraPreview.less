// 摄像头预览组件样式
.camera-preview {
  width: 100%;
  
  .camera-container {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f5f5f5;
    border: 2px solid #d9d9d9;
    margin-bottom: 16px;
    
    .camera-error {
      position: absolute;
      top: 16px;
      left: 16px;
      right: 16px;
      z-index: 10;
    }
    
    .camera-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16px;
      background-color: rgba(245, 245, 245, 0.9);
      z-index: 5;
      
      .elder-text {
        color: #595959;
      }
    }
    
    .camera-video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
      
      &.active {
        border: 2px solid #52c41a;
      }
    }
    
    .camera-canvas {
      position: absolute;
      top: 0;
      left: 0;
    }
    
    .camera-status {
      position: absolute;
      top: 12px;
      right: 12px;
      z-index: 5;
      
      .status-indicator {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 16px;
        
        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #f5222d;
          
          &.active {
            background-color: #52c41a;
            animation: pulse 2s infinite;
          }
        }
        
        &.active .status-dot {
          background-color: #52c41a;
        }
        
        .elder-text {
          color: #ffffff;
          font-size: 12px;
        }
      }
    }
    
    .camera-placeholder {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16px;
      color: #8c8c8c;
      
      .anticon {
        color: #d9d9d9;
      }
    }
  }
  
  .camera-controls {
    display: flex;
    justify-content: center;
    
    .camera-action-buttons {
      display: flex;
      gap: 16px;
      
      .ant-btn {
        min-height: 48px;
        padding: 0 24px;
      }
    }
    
    .ant-btn {
      min-height: 48px;
      padding: 0 24px;
    }
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .camera-preview {
    .camera-container {
      margin-bottom: 12px;
    }
    
    .camera-controls {
      .camera-action-buttons {
        gap: 12px;
        
        .ant-btn {
          min-height: 44px;
          padding: 0 20px;
          font-size: 16px;
        }
      }
      
      .ant-btn {
        min-height: 44px;
        padding: 0 20px;
        font-size: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .camera-preview {
    .camera-container {
      margin-bottom: 10px;
      
      .camera-status {
        top: 8px;
        right: 8px;
        
        .status-indicator {
          padding: 4px 8px;
          
          .status-dot {
            width: 6px;
            height: 6px;
          }
          
          .elder-text {
            font-size: 10px;
          }
        }
      }
      
      .camera-placeholder {
        gap: 12px;
        
        .anticon {
          font-size: 48px;
        }
        
        .elder-text {
          font-size: 16px;
        }
      }
    }
    
    .camera-controls {
      .camera-action-buttons {
        flex-direction: column;
        gap: 8px;
        width: 100%;
        
        .ant-btn {
          width: 100%;
          min-height: 40px;
          padding: 0 16px;
          font-size: 14px;
        }
      }
      
      .ant-btn {
        width: 100%;
        min-height: 40px;
        padding: 0 16px;
        font-size: 14px;
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .camera-preview {
    .camera-container {
      border: 3px solid #000000;
      
      .camera-video {
        &.active {
          border: 3px solid #0b7f00;
        }
      }
      
      .camera-status {
        .status-indicator {
          background-color: rgba(0, 0, 0, 0.9);
          border: 1px solid #ffffff;
          
          .status-dot {
            &.active {
              background-color: #0b7f00;
            }
          }
        }
      }
      
      .camera-placeholder {
        background-color: #ffffff;
        color: #000000;
        
        .anticon {
          color: #434343;
        }
      }
    }
  }
}

// 打印样式
@media print {
  .camera-preview {
    .camera-controls {
      display: none;
    }
    
    .camera-container {
      border: 1px solid #000000;
    }
  }
} 