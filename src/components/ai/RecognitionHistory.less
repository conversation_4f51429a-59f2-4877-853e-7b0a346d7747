// 识别历史组件样式
.recognition-history {
  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .anticon {
        font-size: 18px;
        color: #1677ff;
      }
      
      .elder-text {
        color: #262626;
      }
    }
    
    .header-controls {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .ant-btn {
        height: 32px;
        padding: 0 12px;
        font-size: 14px;
      }
    }
  }
  
  .history-content {
    max-height: 400px;
    overflow-y: auto;
    
    .history-list {
      .history-item {
        padding: 12px 0;
        border-bottom: 1px solid #f5f5f5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .ant-list-item-meta {
          align-items: flex-start;
          
          .ant-list-item-meta-avatar {
            margin-right: 12px;
            
            .history-item-image {
              width: 48px;
              height: 48px;
              border-radius: 6px;
              object-fit: cover;
              border: 1px solid #f0f0f0;
            }
            
            .history-item-placeholder {
              width: 48px;
              height: 48px;
              border-radius: 6px;
              background-color: #f5f5f5;
              border: 1px solid #f0f0f0;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #bfbfbf;
              font-size: 20px;
            }
          }
          
          .ant-list-item-meta-content {
            .history-item-title {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 4px;
              
              .elder-text {
                color: #262626;
                flex: 1;
                margin-right: 8px;
              }
              
              .confidence-tag {
                font-size: 12px;
                font-weight: 600;
                border: none;
                border-radius: 4px;
                padding: 2px 6px;
              }
            }
            
            .history-item-description {
              .item-info {
                margin-bottom: 6px;
                
                .elder-text {
                  color: #8c8c8c;
                  line-height: 1.4;
                }
              }
              
              .confidence-progress {
                margin-bottom: 4px;
                
                .ant-progress {
                  .ant-progress-line {
                    .ant-progress-inner {
                      height: 4px;
                    }
                  }
                }
              }
              
              .timestamp {
                .elder-text {
                  color: #bfbfbf;
                }
              }
            }
          }
        }
        
        .ant-list-item-action {
          margin-left: 12px;
          
          .ant-list-item-action-split {
            display: none;
          }
          
          .ant-btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            
            .anticon {
              font-size: 14px;
            }
            
            &:hover {
              background-color: #f5f5f5;
            }
          }
        }
      }
    }
    
    .expand-control {
      text-align: center;
      padding: 12px 0;
      border-top: 1px solid #f0f0f0;
      
      .ant-btn {
        color: #1677ff;
        font-size: 14px;
        padding: 0;
        height: auto;
        
        &:hover {
          color: #40a9ff;
        }
      }
    }
    
    .ant-empty {
      padding: 24px 0;
      
      .ant-empty-image {
        margin-bottom: 12px;
      }
      
      .ant-empty-description {
        .elder-text {
          color: #8c8c8c;
        }
      }
    }
  }
}

// 筛选控制弹窗
.filter-control {
  padding: 8px;
  min-width: 180px;
  
  .elder-text {
    display: block;
    margin-bottom: 8px;
    color: #595959;
  }
  
  .confidence-filters {
    display: flex;
    gap: 6px;
    
    .ant-btn {
      flex: 1;
      height: 32px;
      font-size: 12px;
      border-radius: 4px;
    }
  }
}

// 识别详情弹窗
.result-details {
  max-width: 300px;
  
  p {
    margin: 0 0 8px 0;
    line-height: 1.4;
    font-size: 14px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    strong {
      color: #262626;
      font-weight: 600;
    }
  }
  
  div {
    margin-left: 8px;
    font-size: 13px;
    color: #8c8c8c;
    line-height: 1.3;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .recognition-history {
    .history-header {
      margin-bottom: 10px;
      padding: 10px 0;
      
      .header-title {
        gap: 6px;
        
        .anticon {
          font-size: 16px;
        }
        
        .elder-text {
          font-size: 14px;
        }
      }
      
      .header-controls {
        gap: 6px;
        
        .ant-btn {
          height: 28px;
          padding: 0 10px;
          font-size: 12px;
        }
      }
    }
    
    .history-content {
      max-height: 350px;
      
      .history-list {
        .history-item {
          padding: 10px 0;
          
          .ant-list-item-meta {
            .ant-list-item-meta-avatar {
              margin-right: 10px;
              
              .history-item-image,
              .history-item-placeholder {
                width: 40px;
                height: 40px;
              }
              
              .history-item-placeholder {
                font-size: 16px;
              }
            }
            
            .ant-list-item-meta-content {
              .history-item-title {
                .confidence-tag {
                  font-size: 10px;
                  padding: 1px 4px;
                }
              }
              
              .history-item-description {
                .item-info {
                  margin-bottom: 4px;
                  
                  .elder-text {
                    font-size: 12px;
                  }
                }
              }
            }
          }
          
          .ant-list-item-action {
            margin-left: 8px;
            
            .ant-btn {
              width: 28px;
              height: 28px;
              
              .anticon {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .recognition-history {
    .history-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      margin-bottom: 8px;
      padding: 8px 0;
      
      .header-controls {
        width: 100%;
        justify-content: flex-end;
      }
    }
    
    .history-content {
      max-height: 300px;
      
      .history-list {
        .history-item {
          padding: 8px 0;
          
          .ant-list-item-meta {
            .ant-list-item-meta-avatar {
              margin-right: 8px;
              
              .history-item-image,
              .history-item-placeholder {
                width: 36px;
                height: 36px;
              }
              
              .history-item-placeholder {
                font-size: 14px;
              }
            }
            
            .ant-list-item-meta-content {
              .history-item-title {
                margin-bottom: 2px;
                
                .elder-text {
                  font-size: 14px;
                }
                
                .confidence-tag {
                  font-size: 9px;
                  padding: 1px 3px;
                }
              }
              
              .history-item-description {
                .item-info {
                  margin-bottom: 3px;
                  
                  .elder-text {
                    font-size: 11px;
                  }
                }
                
                .timestamp {
                  .elder-text {
                    font-size: 10px;
                  }
                }
              }
            }
          }
          
          .ant-list-item-action {
            margin-left: 6px;
            
            .ant-btn {
              width: 24px;
              height: 24px;
              
              .anticon {
                font-size: 10px;
              }
            }
          }
        }
      }
      
      .expand-control {
        padding: 8px 0;
        
        .ant-btn {
          font-size: 12px;
        }
      }
    }
  }
  
  .filter-control {
    min-width: 160px;
    
    .confidence-filters {
      gap: 4px;
      
      .ant-btn {
        height: 28px;
        font-size: 11px;
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .recognition-history {
    .history-header {
      border-bottom: 2px solid #000000;
      
      .header-title {
        .anticon {
          color: #0050b3;
        }
        
        .elder-text {
          color: #000000;
        }
      }
    }
    
    .history-content {
      .history-list {
        .history-item {
          border-bottom: 1px solid #434343;
          
          .ant-list-item-meta {
            .ant-list-item-meta-avatar {
              .history-item-image {
                border: 2px solid #000000;
              }
              
              .history-item-placeholder {
                border: 2px solid #000000;
                background-color: #ffffff;
                color: #434343;
              }
            }
            
            .ant-list-item-meta-content {
              .history-item-title {
                .elder-text {
                  color: #000000;
                }
              }
              
              .history-item-description {
                .item-info {
                  .elder-text {
                    color: #434343;
                  }
                }
                
                .timestamp {
                  .elder-text {
                    color: #434343;
                  }
                }
              }
            }
          }
          
          .ant-list-item-action {
            .ant-btn {
              border: 1px solid #000000;
              
              &:hover {
                background-color: #f0f0f0;
              }
            }
          }
        }
      }
      
      .expand-control {
        border-top: 2px solid #000000;
        
        .ant-btn {
          color: #0050b3;
          
          &:hover {
            color: #003a8c;
          }
        }
      }
    }
  }
}

// 打印样式
@media print {
  .recognition-history {
    .history-header {
      .header-controls {
        display: none;
      }
    }
    
    .history-content {
      max-height: none;
      overflow: visible;
      
      .history-list {
        .history-item {
          .ant-list-item-action {
            display: none;
          }
        }
      }
      
      .expand-control {
        display: none;
      }
    }
  }
} 