import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Alert, Spin } from 'antd';
import { CameraOutlined, StopOutlined, ScanOutlined } from '@ant-design/icons';
import { ElderButton } from '../elder';
import './CameraPreview.less';

interface CameraPreviewProps {
  isActive: boolean;
  onCameraStart?: () => void;
  onCameraStop?: () => void;
  onCapture?: (imageData: ImageData | null) => void;
  width?: number;
  height?: number;
  className?: string;
}

/**
 * 摄像头预览组件
 * 
 * 功能特性：
 * - 实时摄像头预览
 * - 摄像头启动/停止控制
 * - 图像捕获功能
 * - 错误处理和用户提示
 * - 适老化设计
 */
const CameraPreview: React.FC<CameraPreviewProps> = ({
  isActive,
  onCameraStart,
  onCameraStop,
  onCapture,
  width = 640,
  height = 480,
  className = '',
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // 启动摄像头
  const startCamera = useCallback(async () => {
    if (stream) return;

    setIsLoading(true);
    setError('');

    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: width },
          height: { ideal: height },
          facingMode: 'environment' // 优先使用后置摄像头
        }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        setStream(mediaStream);
        onCameraStart?.();
      }
    } catch (err) {
      console.error('摄像头启动失败:', err);
      let errorMessage = '摄像头启动失败';
      
      if (err instanceof Error) {
        if (err.name === 'NotAllowedError') {
          errorMessage = '请允许访问摄像头权限';
        } else if (err.name === 'NotFoundError') {
          errorMessage = '未找到可用的摄像头设备';
        } else if (err.name === 'NotReadableError') {
          errorMessage = '摄像头设备被其他应用占用';
        }
      }
      
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [stream, width, height, onCameraStart]);

  // 停止摄像头
  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
      
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
      
      onCameraStop?.();
    }
  }, [stream, onCameraStop]);

  // 捕获图像
  const captureImage = useCallback(() => {
    if (!videoRef.current || !canvasRef.current || !stream) {
      onCapture?.(null);
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      onCapture?.(null);
      return;
    }

    // 设置canvas尺寸
    canvas.width = video.videoWidth || width;
    canvas.height = video.videoHeight || height;

    // 绘制当前视频帧
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // 获取图像数据
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    onCapture?.(imageData);
  }, [stream, width, height, onCapture]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, [stopCamera]);

  // 监听isActive变化
  useEffect(() => {
    if (isActive && !stream) {
      startCamera();
    } else if (!isActive && stream) {
      stopCamera();
    }
  }, [isActive, stream, startCamera, stopCamera]);

  return (
    <div className={`camera-preview ${className}`}>
      <div className="camera-container" style={{ width, height }}>
        {error && (
          <Alert
            message="摄像头错误"
            description={error}
            type="error"
            showIcon
            closable
            onClose={() => setError('')}
            className="camera-error"
          />
        )}

        {isLoading && (
          <div className="camera-loading">
            <Spin size="large" />
            <span className="elder-text elder-text-base">
              正在启动摄像头...
            </span>
          </div>
        )}

        {!error && !isLoading && (
          <>
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className={`camera-video ${isActive ? 'active' : ''}`}
              style={{ width, height }}
            />
            
            <canvas
              ref={canvasRef}
              className="camera-canvas"
              style={{ display: 'none' }}
            />

            {/* 摄像头状态指示器 */}
            <div className="camera-status">
              <div className={`status-indicator ${isActive ? 'active' : 'inactive'}`}>
                <div className="status-dot" />
                <span className="elder-text elder-text-xs">
                  {isActive ? '摄像头已启动' : '摄像头已停止'}
                </span>
              </div>
            </div>
          </>
        )}

        {/* 占位符 */}
        {!isActive && !isLoading && !error && (
          <div className="camera-placeholder">
            <CameraOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
            <span className="elder-text elder-text-lg elder-text-secondary">
              点击下方按钮启动摄像头
            </span>
          </div>
        )}
      </div>

      {/* 控制按钮 */}
      <div className="camera-controls">
        {!isActive ? (
          <ElderButton
            type="primary"
            size="large"
            icon={<CameraOutlined />}
            onClick={startCamera}
            loading={isLoading}
            disabled={isLoading}
          >
            启动摄像头
          </ElderButton>
        ) : (
          <div className="camera-action-buttons">
            <ElderButton
              type="primary"
              size="large"
              icon={<ScanOutlined />}
              onClick={captureImage}
            >
              拍照识别
            </ElderButton>
            
            <ElderButton
              size="large"
              icon={<StopOutlined />}
              onClick={stopCamera}
            >
              停止摄像头
            </ElderButton>
          </div>
        )}
      </div>
    </div>
  );
};

export default CameraPreview; 