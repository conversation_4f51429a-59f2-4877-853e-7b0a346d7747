import React from 'react';
import { Empty } from 'antd';
import { ElderCard } from '../elder';
import './RecognitionHistory.less';

interface RecognitionHistoryProps {
  maxItems?: number;
  showControls?: boolean;
  className?: string;
}

/**
 * 识别历史组件 - 简化版本
 */
const RecognitionHistory: React.FC<RecognitionHistoryProps> = ({
  className = '',
}) => {
  return (
    <ElderCard 
      title="识别历史" 
      className={`recognition-history ${className}`}
    >
      <Empty 
        description="暂无识别历史记录"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    </ElderCard>
  );
};

export default RecognitionHistory; 