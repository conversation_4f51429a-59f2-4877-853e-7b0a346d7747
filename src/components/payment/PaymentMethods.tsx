import React, { useState, useCallback } from 'react';
import { Radio, Card, InputNumber, QRCode, message, Space, Spin } from 'antd';
import { 
  MoneyCollectOutlined,
  WechatOutlined,
  AlipayOutlined,
  CreditCardOutlined,
  WalletOutlined,
  QrcodeOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { ElderCard, ElderButton } from '../elder';
import { paymentService } from '../../services/paymentService';
import type { Member } from '../../store/slices/memberSlice';
import './PaymentMethods.less';

export interface PaymentMethodsProps {
  totalAmount: number;
  member?: Member | null;
  onPaymentSuccess?: (result: PaymentResult) => void;
  onPaymentCancel?: () => void;
  className?: string;
}

export interface PaymentResult {
  method: 'cash' | 'wechat' | 'alipay' | 'card';
  amount: number;
  transactionId: string;
  timestamp: string;
}

/**
 * 支付方式选择组件
 * 
 * 功能特性：
 * - 多种支付方式支持
 * - 现金找零计算
 * - 二维码支付展示
 * - 储值卡余额验证
 * - 支付状态管理
 * - 适老化设计
 */
const PaymentMethods: React.FC<PaymentMethodsProps> = ({
  totalAmount,
  member,
  onPaymentSuccess,
  onPaymentCancel,
  className = '',
}) => {
  
  // 本地状态
  const [selectedMethod, setSelectedMethod] = useState<'cash' | 'wechat' | 'alipay' | 'card'>('cash');
  const [cashReceived, setCashReceived] = useState<number>(totalAmount);
  const [isProcessing, setIsProcessing] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'processing' | 'success' | 'failed'>('pending');

  // 支付方式配置
  const paymentConfig = {
    cash: {
      name: '现金支付',
      icon: <MoneyCollectOutlined />,
      color: '#52c41a',
      description: '收取现金并找零',
    },
    wechat: {
      name: '微信支付',
      icon: <WechatOutlined />,
      color: '#1aad19',
      description: '扫码支付，安全快捷',
    },
    alipay: {
      name: '支付宝',
      icon: <AlipayOutlined />,
      color: '#1677ff',
      description: '支付宝扫码支付',
    },
    card: {
      name: '储值卡',
      icon: <CreditCardOutlined />,
      color: '#722ed1',
      description: '使用会员储值卡余额',
    },
  };

  // 计算找零
  const changeAmount = cashReceived - totalAmount;

  // 检查储值卡余额是否足够
  const cardBalanceSufficient = member && member.balance >= totalAmount;

  // 处理支付方式选择
  const handleMethodChange = useCallback((method: typeof selectedMethod) => {
    setSelectedMethod(method);
    setPaymentStatus('pending');
    setQrCodeUrl('');
    
    // 重置现金输入
    if (method !== 'cash') {
      setCashReceived(totalAmount);
    }
  }, [totalAmount]);

  // 处理现金支付
  const handleCashPayment = useCallback(async () => {
    if (cashReceived < totalAmount) {
      message.error('收取金额不足');
      return;
    }

    setIsProcessing(true);
    setPaymentStatus('processing');

    try {
      const result = await paymentService.processCashPayment({
        amount: totalAmount,
        received: cashReceived,
        change: changeAmount,
      });

      setPaymentStatus('success');
      
      onPaymentSuccess?.({
        method: 'cash',
        amount: totalAmount,
        transactionId: result.transactionId,
        timestamp: new Date().toISOString(),
      });

      message.success(`支付成功！${changeAmount > 0 ? `找零：¥${changeAmount.toFixed(2)}` : ''}`);
    } catch (error) {
      setPaymentStatus('failed');
      message.error(error instanceof Error ? error.message : '现金支付失败');
    } finally {
      setIsProcessing(false);
    }
  }, [totalAmount, cashReceived, changeAmount, onPaymentSuccess]);

  // 处理二维码支付
  const handleQRPayment = useCallback(async (method: 'wechat' | 'alipay') => {
    setIsProcessing(true);
    setPaymentStatus('processing');

    try {
      const result = await paymentService.generateQRCode({
        method,
        amount: totalAmount,
        orderId: `order_${Date.now()}`,
      });

      setQrCodeUrl(result.qrCodeUrl);
      
      // 轮询支付状态
      const pollPaymentStatus = async () => {
        try {
          const status = await paymentService.checkPaymentStatus(result.transactionId);
          
          if (status.paid) {
            setPaymentStatus('success');
            onPaymentSuccess?.({
              method,
              amount: totalAmount,
              transactionId: result.transactionId,
              timestamp: new Date().toISOString(),
            });
            message.success('支付成功！');
          } else if (status.failed) {
            setPaymentStatus('failed');
            message.error('支付失败，请重试');
          } else {
            // 继续轮询
            setTimeout(pollPaymentStatus, 2000);
          }
        } catch (error) {
          setPaymentStatus('failed');
          message.error('支付状态查询失败');
        }
      };

      // 开始轮询
      setTimeout(pollPaymentStatus, 2000);
      
    } catch (error) {
      setPaymentStatus('failed');
      message.error(error instanceof Error ? error.message : '二维码生成失败');
    } finally {
      setIsProcessing(false);
    }
  }, [totalAmount, onPaymentSuccess]);

  // 处理储值卡支付
  const handleCardPayment = useCallback(async () => {
    if (!member) {
      message.error('请先选择会员');
      return;
    }

    if (!cardBalanceSufficient) {
      message.error('储值卡余额不足');
      return;
    }

    setIsProcessing(true);
    setPaymentStatus('processing');

    try {
      const result = await paymentService.processCardPayment({
        memberId: member.id,
        amount: totalAmount,
      });

      setPaymentStatus('success');
      
      onPaymentSuccess?.({
        method: 'card',
        amount: totalAmount,
        transactionId: result.transactionId,
        timestamp: new Date().toISOString(),
      });

      message.success(`储值卡支付成功！余额：¥${(member.balance - totalAmount).toFixed(2)}`);
    } catch (error) {
      setPaymentStatus('failed');
      message.error(error instanceof Error ? error.message : '储值卡支付失败');
    } finally {
      setIsProcessing(false);
    }
  }, [member, totalAmount, cardBalanceSufficient, onPaymentSuccess]);

  // 处理支付确认
  const handlePaymentConfirm = useCallback(() => {
    switch (selectedMethod) {
      case 'cash':
        handleCashPayment();
        break;
      case 'wechat':
      case 'alipay':
        handleQRPayment(selectedMethod);
        break;
      case 'card':
        handleCardPayment();
        break;
    }
  }, [selectedMethod, handleCashPayment, handleQRPayment, handleCardPayment]);

  return (
    <ElderCard className={`payment-methods ${className}`} size="large">
      {/* 支付金额显示 */}
      <div className="payment-amount">
        <div className="amount-label">
          <span className="elder-text elder-text-lg">应付金额</span>
        </div>
        <div className="amount-value">
          <span className="elder-text elder-text-3xl elder-text-bold elder-text-error">
            ¥{totalAmount.toFixed(2)}
          </span>
        </div>
      </div>

      {/* 支付方式选择 */}
      <div className="method-selection">
        <div className="selection-header">
          <span className="elder-text elder-text-lg elder-text-bold">
            选择支付方式
          </span>
        </div>
        
        <Radio.Group
          value={selectedMethod}
          onChange={(e) => handleMethodChange(e.target.value)}
          className="method-options"
        >
          {Object.entries(paymentConfig).map(([key, config]) => {
            const disabled = key === 'card' && (!member || !cardBalanceSufficient);
            
            return (
              <Radio.Button
                key={key}
                value={key}
                disabled={disabled}
                className={`method-option ${disabled ? 'disabled' : ''}`}
              >
                <div className="option-content">
                  <div className="option-icon" style={{ color: config.color }}>
                    {config.icon}
                  </div>
                  <div className="option-text">
                    <span className="elder-text elder-text-base elder-text-bold">
                      {config.name}
                    </span>
                    <span className="elder-text elder-text-xs elder-text-secondary">
                      {config.description}
                    </span>
                  </div>
                </div>
              </Radio.Button>
            );
          })}
        </Radio.Group>
      </div>

      {/* 支付详情 */}
      <div className="payment-details">
        {/* 现金支付 */}
        {selectedMethod === 'cash' && (
          <div className="cash-payment">
            <div className="cash-input">
              <span className="elder-text elder-text-base">收取金额</span>
              <InputNumber
                value={cashReceived}
                onChange={(value) => setCashReceived(value || 0)}
                min={0}
                precision={2}
                size="large"
                className="elder-input-number"
                addonBefore="¥"
                style={{ width: '100%' }}
              />
            </div>
            
            {changeAmount >= 0 && (
              <div className="change-amount">
                <span className="elder-text elder-text-base">找零金额</span>
                <span className="elder-text elder-text-xl elder-text-bold elder-text-warning">
                  ¥{changeAmount.toFixed(2)}
                </span>
              </div>
            )}
          </div>
        )}

        {/* 二维码支付 */}
        {(selectedMethod === 'wechat' || selectedMethod === 'alipay') && (
          <div className="qr-payment">
            {qrCodeUrl ? (
              <div className="qr-code-display">
                <QRCode value={qrCodeUrl} size={200} />
                <div className="qr-tips">
                  <QrcodeOutlined />
                  <span className="elder-text elder-text-sm elder-text-secondary">
                    请使用{paymentConfig[selectedMethod].name}扫码支付
                  </span>
                </div>
              </div>
            ) : (
              <div className="qr-placeholder">
                <QrcodeOutlined className="placeholder-icon" />
                <span className="elder-text elder-text-base elder-text-secondary">
                  点击确认支付生成二维码
                </span>
              </div>
            )}
          </div>
        )}

        {/* 储值卡支付 */}
        {selectedMethod === 'card' && (
          <div className="card-payment">
            {member ? (
              <div className="card-info">
                <div className="card-balance">
                  <WalletOutlined className="balance-icon" />
                  <div className="balance-text">
                    <span className="elder-text elder-text-base">当前余额</span>
                    <span className="elder-text elder-text-xl elder-text-bold elder-text-success">
                      ¥{member.balance.toFixed(2)}
                    </span>
                  </div>
                </div>
                
                {cardBalanceSufficient ? (
                  <div className="payment-result">
                    <CheckCircleOutlined className="success-icon" />
                    <span className="elder-text elder-text-sm elder-text-success">
                      余额充足，支付后余额：¥{(member.balance - totalAmount).toFixed(2)}
                    </span>
                  </div>
                ) : (
                  <div className="insufficient-balance">
                    <span className="elder-text elder-text-sm elder-text-error">
                      余额不足，请充值或选择其他支付方式
                    </span>
                  </div>
                )}
              </div>
            ) : (
              <div className="no-member">
                <span className="elder-text elder-text-base elder-text-secondary">
                  请先选择会员
                </span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 支付状态 */}
      {paymentStatus !== 'pending' && (
        <div className={`payment-status ${paymentStatus}`}>
          {paymentStatus === 'processing' && (
            <div className="status-processing">
              <Spin />
              <span className="elder-text elder-text-base">正在处理支付...</span>
            </div>
          )}
          
          {paymentStatus === 'success' && (
            <div className="status-success">
              <CheckCircleOutlined />
              <span className="elder-text elder-text-base elder-text-success">支付成功</span>
            </div>
          )}
          
          {paymentStatus === 'failed' && (
            <div className="status-failed">
              <span className="elder-text elder-text-base elder-text-error">支付失败，请重试</span>
            </div>
          )}
        </div>
      )}

      {/* 操作按钮 */}
      <div className="payment-actions">
        <ElderButton
          size="large"
          onClick={onPaymentCancel}
          disabled={isProcessing}
          style={{ flex: 1 }}
        >
          取消
        </ElderButton>
        
        <ElderButton
          type="primary"
          size="large"
          onClick={handlePaymentConfirm}
          loading={isProcessing}
          disabled={
            paymentStatus === 'success' ||
            (selectedMethod === 'cash' && cashReceived < totalAmount) ||
            (selectedMethod === 'card' && (!member || !cardBalanceSufficient))
          }
          style={{ flex: 2 }}
        >
          {paymentStatus === 'success' ? '支付完成' : '确认支付'}
        </ElderButton>
      </div>
    </ElderCard>
  );
};

export default PaymentMethods; 