// 支付方式组件样式
.payment-methods {
  .payment-amount {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
    border-radius: 12px;
    margin-bottom: 24px;
    
    .amount-label {
      margin-bottom: 8px;
    }
    
    .amount-value {
      font-weight: bold;
    }
  }
  
  .method-selection {
    margin-bottom: 24px;
    
    .selection-header {
      margin-bottom: 16px;
    }
    
    .method-options {
      .ant-radio-button-wrapper {
        &.method-option {
          height: auto;
          padding: 0;
          border: 2px solid #d9d9d9;
          border-radius: 12px !important;
          margin-bottom: 12px;
          margin-right: 12px;
          
          &:last-child {
            margin-right: 0;
          }
          
          &::before {
            display: none;
          }
          
          &.ant-radio-button-wrapper-checked {
            border-color: #1677ff;
            background-color: #f0f6ff;
            color: inherit;
            
            .option-content {
              .option-icon {
                color: #1677ff !important;
              }
              
              .option-text {
                .elder-text {
                  &:first-child {
                    color: #1677ff;
                  }
                }
              }
            }
          }
          
          &.disabled {
            background-color: #f5f5f5;
            border-color: #e8e8e8;
            cursor: not-allowed;
            
            .option-content {
              opacity: 0.5;
            }
          }
          
          .option-content {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            width: 100%;
            
            .option-icon {
              font-size: 28px;
              flex-shrink: 0;
            }
            
            .option-text {
              display: flex;
              flex-direction: column;
              gap: 4px;
              flex: 1;
            }
          }
        }
      }
    }
  }
  
  .payment-details {
    margin-bottom: 24px;
    
    .cash-payment {
      .cash-input {
        margin-bottom: 16px;
        
        .elder-text {
          display: block;
          margin-bottom: 8px;
        }
      }
      
      .change-amount {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #fffbe6;
        border: 1px solid #ffe58f;
        border-radius: 8px;
      }
    }
    
    .qr-payment {
      text-align: center;
      
      .qr-code-display {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        padding: 20px;
        background: #fafafa;
        border-radius: 12px;
        
        .qr-tips {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .anticon {
            font-size: 16px;
            color: #1677ff;
          }
        }
      }
      
      .qr-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        padding: 40px 20px;
        background: #fafafa;
        border: 2px dashed #d9d9d9;
        border-radius: 12px;
        
        .placeholder-icon {
          font-size: 48px;
          color: #d9d9d9;
        }
      }
    }
    
    .card-payment {
      .card-info {
        .card-balance {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 16px;
          background: #f6ffed;
          border: 1px solid #b7eb8f;
          border-radius: 8px;
          margin-bottom: 16px;
          
          .balance-icon {
            font-size: 32px;
            color: #52c41a;
          }
          
          .balance-text {
            display: flex;
            flex-direction: column;
            gap: 4px;
          }
        }
        
        .payment-result {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px 16px;
          background: #f6ffed;
          border: 1px solid #b7eb8f;
          border-radius: 6px;
          
          .success-icon {
            color: #52c41a;
            font-size: 16px;
          }
        }
        
        .insufficient-balance {
          padding: 12px 16px;
          background: #fff2f0;
          border: 1px solid #ffccc7;
          border-radius: 6px;
        }
      }
      
      .no-member {
        padding: 20px;
        text-align: center;
        background: #fafafa;
        border-radius: 8px;
      }
    }
  }
  
  .payment-status {
    margin-bottom: 24px;
    padding: 16px;
    border-radius: 8px;
    text-align: center;
    
    &.processing {
      background: #f0f6ff;
      border: 1px solid #adc6ff;
      
      .status-processing {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
      }
    }
    
    &.success {
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      
      .status-success {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        
        .anticon {
          color: #52c41a;
          font-size: 20px;
        }
      }
    }
    
    &.failed {
      background: #fff2f0;
      border: 1px solid #ffccc7;
      
      .status-failed {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
      }
    }
  }
  
  .payment-actions {
    display: flex;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .payment-methods {
    .payment-amount {
      padding: 16px;
      margin-bottom: 20px;
    }
    
    .method-selection {
      margin-bottom: 20px;
      
      .method-options {
        .ant-radio-button-wrapper {
          &.method-option {
            width: 100%;
            margin-right: 0;
            margin-bottom: 8px;
            
            .option-content {
              padding: 12px;
              
              .option-icon {
                font-size: 24px;
              }
            }
          }
        }
      }
    }
    
    .payment-details {
      margin-bottom: 20px;
      
      .qr-payment {
        .qr-code-display {
          padding: 16px;
          gap: 12px;
        }
        
        .qr-placeholder {
          padding: 30px 15px;
          gap: 12px;
          
          .placeholder-icon {
            font-size: 36px;
          }
        }
      }
      
      .card-payment {
        .card-info {
          .card-balance {
            padding: 12px;
            gap: 12px;
            
            .balance-icon {
              font-size: 28px;
            }
          }
        }
      }
    }
    
    .payment-status {
      margin-bottom: 20px;
      padding: 12px;
    }
    
    .payment-actions {
      gap: 8px;
    }
  }
}

@media (max-width: 480px) {
  .payment-methods {
    .payment-amount {
      padding: 12px;
      margin-bottom: 16px;
    }
    
    .method-selection {
      margin-bottom: 16px;
      
      .method-options {
        .ant-radio-button-wrapper {
          &.method-option {
            .option-content {
              padding: 10px;
              gap: 8px;
              
              .option-icon {
                font-size: 20px;
              }
              
              .option-text {
                gap: 2px;
              }
            }
          }
        }
      }
    }
    
    .payment-details {
      margin-bottom: 16px;
      
      .cash-payment {
        .cash-input {
          margin-bottom: 12px;
        }
        
        .change-amount {
          padding: 10px 12px;
        }
      }
      
      .card-payment {
        .card-info {
          .card-balance {
            flex-direction: column;
            text-align: center;
            padding: 10px;
            gap: 8px;
            
            .balance-icon {
              font-size: 24px;
            }
            
            .balance-text {
              align-items: center;
            }
          }
          
          .payment-result,
          .insufficient-balance {
            padding: 10px 12px;
          }
        }
        
        .no-member {
          padding: 16px;
        }
      }
    }
    
    .payment-status {
      margin-bottom: 16px;
      padding: 10px;
    }
    
    .payment-actions {
      flex-direction: column;
      gap: 8px;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .payment-methods {
    border: 2px solid #000000;
    
    .payment-amount {
      background: #ffffff;
      border: 2px solid #1677ff;
    }
    
    .method-selection {
      .method-options {
        .ant-radio-button-wrapper {
          &.method-option {
            border: 2px solid #000000;
            background: #ffffff;
            
            &.ant-radio-button-wrapper-checked {
              border-color: #0050b3;
              background-color: #e6f7ff;
            }
            
            &.disabled {
              background-color: #f0f0f0;
              border-color: #8c8c8c;
            }
            
            .option-content {
              .option-icon {
                color: #434343;
              }
              
              .option-text {
                .elder-text {
                  color: #000000;
                }
              }
            }
          }
        }
      }
    }
    
    .payment-details {
      .cash-payment {
        .change-amount {
          background: #fff7e6;
          border: 2px solid #d48806;
        }
      }
      
      .qr-payment {
        .qr-code-display {
          background: #ffffff;
          border: 2px solid #000000;
        }
        
        .qr-placeholder {
          background: #ffffff;
          border: 2px dashed #434343;
          
          .placeholder-icon {
            color: #8c8c8c;
          }
        }
      }
      
      .card-payment {
        .card-info {
          .card-balance {
            background: #ffffff;
            border: 2px solid #52c41a;
            
            .balance-icon {
              color: #0b7f00;
            }
          }
          
          .payment-result {
            background: #ffffff;
            border: 2px solid #52c41a;
            
            .success-icon {
              color: #0b7f00;
            }
          }
          
          .insufficient-balance {
            background: #ffffff;
            border: 2px solid #f5222d;
          }
        }
        
        .no-member {
          background: #ffffff;
          border: 2px solid #000000;
        }
      }
    }
    
    .payment-status {
      &.processing {
        background: #ffffff;
        border: 2px solid #1677ff;
      }
      
      &.success {
        background: #ffffff;
        border: 2px solid #52c41a;
        
        .status-success {
          .anticon {
            color: #0b7f00;
          }
        }
      }
      
      &.failed {
        background: #ffffff;
        border: 2px solid #f5222d;
      }
    }
  }
} 