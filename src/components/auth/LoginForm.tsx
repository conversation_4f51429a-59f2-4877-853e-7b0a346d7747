import React, { useState } from 'react';
import { Form, Input, Button, Checkbox, Card, message, Typography, Space } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { useAppDispatch } from '../../hooks/redux';
import { login } from '../../store/slices/authSlice';
import type { LoginCredentials } from '../../types/auth';
import './LoginForm.less';

const { Title, Text } = Typography;

interface LoginFormProps {
  onSuccess?: () => void;
  onForgotPassword?: () => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSuccess, onForgotPassword }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const dispatch = useAppDispatch();

  // 处理登录提交
  const handleSubmit = async (values: LoginCredentials & { remember: boolean }) => {
    try {
      setLoading(true);
      
      // 调用登录action
      await dispatch(login({
        username: values.username,
        password: values.password,
        remember: values.remember,
      })).unwrap();

      message.success('登录成功');
      onSuccess?.();
    } catch (error) {
      console.error('登录失败:', error);
      message.error(error instanceof Error ? error.message : '登录失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理忘记密码
  const handleForgotPassword = () => {
    onForgotPassword?.();
    message.info('请联系系统管理员重置密码');
  };

  return (
    <div className="login-container">
      <Card className="login-card" bordered={false}>
        <div className="login-header">
          <div className="logo">
            <div className="logo-icon">🍽️</div>
            <Title level={2} className="system-name">
              助老订餐系统
            </Title>
          </div>
          <Text type="secondary" className="subtitle">
            专为老年人设计的智能收银系统
          </Text>
        </div>

        <Form
          form={form}
          name="login"
          layout="vertical"
          size="large"
          onFinish={handleSubmit}
          autoComplete="off"
          className="login-form"
          initialValues={{
            remember: true,
            username: '',
            password: '',
          }}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="请输入用户名"
              autoComplete="username"
              className="login-input"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              autoComplete="current-password"
              className="login-input"
            />
          </Form.Item>

          <Form.Item>
            <div className="login-options">
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox className="remember-checkbox">记住密码</Checkbox>
              </Form.Item>
              <Button
                type="link"
                onClick={handleForgotPassword}
                className="forgot-password"
              >
                忘记密码？
              </Button>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<LoginOutlined />}
              className="login-button"
              block
            >
              登录系统
            </Button>
          </Form.Item>
        </Form>

        <div className="login-footer">
          <Space split={<span>|</span>}>
            <Text type="secondary">快捷登录：Ctrl + L</Text>
            <Text type="secondary">帮助：F1</Text>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default LoginForm; 