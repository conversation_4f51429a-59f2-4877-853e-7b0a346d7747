.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;

  .login-card {
    width: 100%;
    max-width: 420px;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;

    .ant-card-body {
      padding: 40px 32px;
    }
  }

  .login-header {
    text-align: center;
    margin-bottom: 32px;

    .logo {
      margin-bottom: 16px;

      .logo-icon {
        font-size: 48px;
        margin-bottom: 12px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
      }

      .system-name {
        margin: 0;
        color: #333;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .subtitle {
      font-size: 16px;
      color: #666;
      margin: 0;
    }
  }

  .login-form {
    .ant-form-item-label > label {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .login-input {
      height: 48px;
      border-radius: 8px;
      border: 2px solid #e1e5e9;
      transition: all 0.3s ease;

      &:hover {
        border-color: #667eea;
      }

      &:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .ant-input {
        font-size: 16px;
        height: 44px;
        border: none;
        background: transparent;

        &::placeholder {
          color: #999;
        }
      }

      .anticon {
        color: #667eea;
        font-size: 18px;
      }
    }

    .login-options {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .remember-checkbox {
        .ant-checkbox-inner {
          border-radius: 4px;
          border-color: #d9d9d9;
        }

        .ant-checkbox-checked .ant-checkbox-inner {
          background-color: #667eea;
          border-color: #667eea;
        }

        span {
          font-size: 14px;
          color: #666;
        }
      }

      .forgot-password {
        padding: 0;
        height: auto;
        font-size: 14px;
        color: #667eea;

        &:hover {
          color: #764ba2;
        }
      }
    }

    .login-button {
      height: 50px;
      border-radius: 10px;
      font-size: 18px;
      font-weight: 600;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
      }

      &:active {
        transform: translateY(0);
      }

      .anticon {
        font-size: 18px;
      }
    }
  }

  .login-footer {
    text-align: center;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;

    .ant-typography {
      font-size: 12px;
      color: #999;
    }
  }
}

// 适老化设计
.elderly-mode {
  .login-container {
    .login-card {
      max-width: 480px;

      .ant-card-body {
        padding: 48px 40px;
      }
    }

    .login-header {
      .logo {
        .logo-icon {
          font-size: 56px;
        }

        .system-name {
          font-size: 28px;
        }
      }

      .subtitle {
        font-size: 18px;
      }
    }

    .login-form {
      .ant-form-item-label > label {
        font-size: 18px;
      }

      .login-input {
        height: 56px;

        .ant-input {
          font-size: 18px;
          height: 52px;
        }

        .anticon {
          font-size: 20px;
        }
      }

      .login-options {
        .remember-checkbox {
          span {
            font-size: 16px;
          }
        }

        .forgot-password {
          font-size: 16px;
        }
      }

      .login-button {
        height: 58px;
        font-size: 20px;

        .anticon {
          font-size: 20px;
        }
      }
    }

    .login-footer {
      .ant-typography {
        font-size: 14px;
      }
    }
  }
}

// 高对比度模式
.high-contrast {
  .login-container {
    background: #000;

    .login-card {
      background: #fff;
      border: 3px solid #000;

      .ant-card-body {
        background: #fff;
      }
    }

    .login-header {
      .logo {
        .system-name {
          color: #000;
          background: none;
          -webkit-text-fill-color: initial;
        }
      }

      .subtitle {
        color: #000;
      }
    }

    .login-form {
      .ant-form-item-label > label {
        color: #000;
      }

      .login-input {
        background: #fff;
        border-color: #000;

        &:hover, &:focus {
          border-color: #000;
          box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
        }

        .ant-input {
          color: #000;
          background: #fff;

          &::placeholder {
            color: #666;
          }
        }

        .anticon {
          color: #000;
        }
      }

      .login-options {
        .remember-checkbox {
          .ant-checkbox-inner {
            border-color: #000;
          }

          .ant-checkbox-checked .ant-checkbox-inner {
            background-color: #000;
            border-color: #000;
          }

          span {
            color: #000;
          }
        }

        .forgot-password {
          color: #000;

          &:hover {
            color: #666;
          }
        }
      }

      .login-button {
        background: #000;
        border-color: #000;
        color: #fff;
        box-shadow: none;

        &:hover {
          background: #333;
          transform: none;
          box-shadow: none;
        }
      }
    }

    .login-footer {
      border-top-color: #000;

      .ant-typography {
        color: #000;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .login-container {
    padding: 16px;

    .login-card {
      .ant-card-body {
        padding: 32px 24px;
      }
    }

    .login-header {
      margin-bottom: 24px;

      .logo {
        .logo-icon {
          font-size: 40px;
        }

        .system-name {
          font-size: 20px;
        }
      }

      .subtitle {
        font-size: 14px;
      }
    }

    .login-form {
      .login-input {
        height: 44px;

        .ant-input {
          font-size: 16px;
          height: 40px;
        }
      }

      .login-button {
        height: 46px;
        font-size: 16px;
      }
    }
  }

  // 适老化移动端
  .elderly-mode .login-container {
    .login-card {
      .ant-card-body {
        padding: 40px 28px;
      }
    }

    .login-header {
      .logo {
        .logo-icon {
          font-size: 48px;
        }

        .system-name {
          font-size: 24px;
        }
      }

      .subtitle {
        font-size: 16px;
      }
    }

    .login-form {
      .login-input {
        height: 52px;

        .ant-input {
          font-size: 18px;
          height: 48px;
        }
      }

      .login-button {
        height: 54px;
        font-size: 18px;
      }
    }
  }
} 