import React, { useMemo } from 'react';
import { Divider, Space, Tag, Tooltip } from 'antd';
import { 
  CreditCardOutlined,
  GiftOutlined,
  UserOutlined,
  CalculatorOutlined
} from '@ant-design/icons';
import { ElderButton, ElderCard } from '../elder';
import type { OrderState } from '../../store/slices/orderSlice';
import './OrderSummary.less';

interface OrderSummaryProps {
  cart: OrderState['cart'];
  showMemberInfo?: boolean;
  showPaymentButton?: boolean;
  onPayment?: () => void;
  className?: string;
}

/**
 * 订单汇总组件
 * 
 * 功能特性：
 * - 订单金额计算显示
 * - 会员折扣信息
 * - 优惠券折扣
 * - 支付按钮
 * - 订单统计信息
 * - 适老化设计
 */
const OrderSummary: React.FC<OrderSummaryProps> = ({
  cart,
  showMemberInfo = false,
  showPaymentButton = true,
  onPayment,
  className = '',
}) => {
  
  // 计算订单汇总信息
  const summaryInfo = useMemo(() => {
    const itemCount = cart.items.reduce((sum, item) => sum + item.quantity, 0);
    const uniqueItemCount = cart.items.length;
    
    // 计算各种折扣
    const memberDiscountAmount = cart.memberDiscount ? cart.subtotal * cart.memberDiscount : 0;
    const totalDiscountAmount = cart.discount + memberDiscountAmount;
    
    return {
      itemCount,
      uniqueItemCount,
      subtotal: cart.subtotal,
      memberDiscountAmount,
      totalDiscountAmount,
      totalAmount: cart.totalAmount,
      memberDiscountRate: cart.memberDiscount || 0,
    };
  }, [cart]);

  // 格式化金额
  const formatAmount = (amount: number): string => {
    return `¥${amount.toFixed(2)}`;
  };

  // 获取折扣标签颜色
  const getDiscountColor = (rate: number): string => {
    if (rate >= 0.1) return 'gold';
    if (rate >= 0.05) return 'blue';
    return 'green';
  };

  return (
    <ElderCard className={`order-summary ${className}`} size="large">
      {/* 汇总标题 */}
      <div className="summary-header">
        <div className="header-title">
          <CalculatorOutlined className="summary-icon" />
          <span className="elder-text elder-text-lg elder-text-bold">
            订单汇总
          </span>
        </div>
        <div className="item-count">
          <span className="elder-text elder-text-sm elder-text-secondary">
            {summaryInfo.uniqueItemCount} 种商品，共 {summaryInfo.itemCount} 件
          </span>
        </div>
      </div>

      <Divider style={{ margin: '16px 0' }} />

      {/* 金额明细 */}
      <div className="amount-details">
        {/* 商品小计 */}
        <div className="amount-row">
          <span className="amount-label elder-text elder-text-base">
            商品小计：
          </span>
          <span className="amount-value elder-text elder-text-base elder-text-bold">
            {formatAmount(summaryInfo.subtotal)}
          </span>
        </div>

        {/* 会员折扣 */}
        {showMemberInfo && summaryInfo.memberDiscountAmount > 0 && (
          <div className="amount-row discount-row">
            <div className="discount-label">
              <UserOutlined className="discount-icon" />
              <span className="elder-text elder-text-base">
                会员折扣：
              </span>
              <Tag 
                color={getDiscountColor(summaryInfo.memberDiscountRate)}
              >
                {Math.round(summaryInfo.memberDiscountRate * 100)}% OFF
              </Tag>
            </div>
            <span className="amount-value elder-text elder-text-base elder-text-bold elder-text-success">
              -{formatAmount(summaryInfo.memberDiscountAmount)}
            </span>
          </div>
        )}

        {/* 其他优惠 */}
        {cart.discount > 0 && (
          <div className="amount-row discount-row">
            <div className="discount-label">
              <GiftOutlined className="discount-icon" />
              <span className="elder-text elder-text-base">
                优惠减免：
              </span>
            </div>
            <span className="amount-value elder-text elder-text-base elder-text-bold elder-text-success">
              -{formatAmount(cart.discount)}
            </span>
          </div>
        )}

        {/* 总优惠 */}
        {summaryInfo.totalDiscountAmount > 0 && (
          <div className="amount-row total-discount-row">
            <span className="amount-label elder-text elder-text-base">
              优惠合计：
            </span>
            <span className="amount-value elder-text elder-text-base elder-text-bold elder-text-success">
              -{formatAmount(summaryInfo.totalDiscountAmount)}
            </span>
          </div>
        )}
      </div>

      <Divider style={{ margin: '16px 0' }} />

      {/* 应付金额 */}
      <div className="total-amount">
        <div className="total-row">
          <span className="total-label elder-text elder-text-xl elder-text-bold">
            应付金额：
          </span>
          <span className="total-value elder-text elder-text-2xl elder-text-bold elder-text-error">
            {formatAmount(summaryInfo.totalAmount)}
          </span>
        </div>
        
        {summaryInfo.totalDiscountAmount > 0 && (
          <div className="savings-info">
            <span className="elder-text elder-text-sm elder-text-success">
              已节省 {formatAmount(summaryInfo.totalDiscountAmount)}
            </span>
          </div>
        )}
      </div>

      {/* 支付按钮 */}
      {showPaymentButton && (
        <div className="payment-section">
          <ElderButton
            type="primary"
            size="large"
            icon={<CreditCardOutlined />}
            onClick={onPayment}
            disabled={cart.items.length === 0}
            className="payment-btn"
            block
          >
            立即支付
          </ElderButton>
        </div>
      )}

      {/* 订单备注 */}
      <div className="order-notes">
        <div className="notes-row">
          <span className="elder-text elder-text-xs elder-text-secondary">
            • 价格已含税费
          </span>
        </div>
        {summaryInfo.memberDiscountAmount > 0 && (
          <div className="notes-row">
            <span className="elder-text elder-text-xs elder-text-secondary">
              • 会员折扣已自动应用
            </span>
          </div>
        )}
        <div className="notes-row">
          <span className="elder-text elder-text-xs elder-text-secondary">
            • 支付前请确认订单信息
          </span>
        </div>
      </div>
    </ElderCard>
  );
};

export default OrderSummary; 