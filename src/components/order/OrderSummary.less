// 订单汇总组件样式
.order-summary {
  .summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .summary-icon {
        font-size: 18px;
        color: #1677ff;
      }
      
      .elder-text {
        color: #262626;
      }
    }
    
    .item-count {
      .elder-text {
        color: #8c8c8c;
      }
    }
  }
  
  .amount-details {
    .amount-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .amount-label {
        color: #595959;
      }
      
      .amount-value {
        color: #262626;
      }
      
      &.discount-row {
        .discount-label {
          display: flex;
          align-items: center;
          gap: 6px;
          
          .discount-icon {
            font-size: 14px;
            color: #52c41a;
          }
          
          .ant-tag {
            margin: 0;
            font-size: 10px;
            padding: 1px 4px;
            line-height: 1.2;
          }
        }
      }
      
      &.total-discount-row {
        border-top: 1px dashed #d9d9d9;
        padding-top: 8px;
        margin-top: 8px;
      }
    }
  }
  
  .total-amount {
    .total-row {
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      margin-bottom: 8px;
      
      .total-label {
        color: #262626;
      }
      
      .total-value {
        color: #f5222d;
      }
    }
    
    .savings-info {
      text-align: right;
      
      .elder-text {
        color: #52c41a;
      }
    }
  }
  
  .payment-section {
    margin-top: 24px;
    
    .payment-btn {
      height: 56px;
      font-size: 18px;
      font-weight: 600;
      border-radius: 8px;
      background: linear-gradient(135deg, #1677ff 0%, #40a9ff 100%);
      border-color: #1677ff;
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
      
      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #0958d9 0%, #1677ff 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(22, 119, 255, 0.4);
      }
      
      &:disabled {
        background: #f5f5f5;
        border-color: #d9d9d9;
        color: #bfbfbf;
        box-shadow: none;
        transform: none;
      }
    }
  }
  
  .order-notes {
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
    
    .notes-row {
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .elder-text {
        color: #8c8c8c;
        line-height: 1.4;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .order-summary {
    .summary-header {
      margin-bottom: 14px;
      
      .header-title {
        gap: 6px;
        
        .summary-icon {
          font-size: 16px;
        }
        
        .elder-text {
          font-size: 16px;
        }
      }
      
      .item-count {
        .elder-text {
          font-size: 13px;
        }
      }
    }
    
    .amount-details {
      .amount-row {
        margin-bottom: 6px;
        
        .amount-label,
        .amount-value {
          font-size: 14px;
        }
        
        &.discount-row {
          .discount-label {
            gap: 4px;
            
            .discount-icon {
              font-size: 12px;
            }
          }
        }
        
        &.total-discount-row {
          padding-top: 6px;
          margin-top: 6px;
        }
      }
    }
    
    .total-amount {
      .total-row {
        margin-bottom: 6px;
        
        .total-label {
          font-size: 18px;
        }
        
        .total-value {
          font-size: 22px;
        }
      }
    }
    
    .payment-section {
      margin-top: 20px;
      
      .payment-btn {
        height: 52px;
        font-size: 16px;
      }
    }
    
    .order-notes {
      margin-top: 14px;
      padding-top: 10px;
      
      .notes-row {
        margin-bottom: 3px;
        
        .elder-text {
          font-size: 11px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .order-summary {
    .summary-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      margin-bottom: 12px;
    }
    
    .amount-details {
      .amount-row {
        margin-bottom: 5px;
        
        .amount-label,
        .amount-value {
          font-size: 13px;
        }
        
        &.discount-row {
          .discount-label {
            gap: 3px;
            
            .elder-text {
              font-size: 13px;
            }
            
            .discount-icon {
              font-size: 11px;
            }
          }
        }
        
        &.total-discount-row {
          padding-top: 5px;
          margin-top: 5px;
        }
      }
    }
    
    .total-amount {
      .total-row {
        margin-bottom: 5px;
        
        .total-label {
          font-size: 16px;
        }
        
        .total-value {
          font-size: 20px;
        }
      }
    }
    
    .payment-section {
      margin-top: 16px;
      
      .payment-btn {
        height: 48px;
        font-size: 15px;
      }
    }
    
    .order-notes {
      margin-top: 12px;
      padding-top: 8px;
      
      .notes-row {
        margin-bottom: 2px;
        
        .elder-text {
          font-size: 10px;
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .order-summary {
    .summary-header {
      .header-title {
        .summary-icon {
          color: #0050b3;
        }
        
        .elder-text {
          color: #000000;
        }
      }
      
      .item-count {
        .elder-text {
          color: #434343;
        }
      }
    }
    
    .amount-details {
      .amount-row {
        .amount-label {
          color: #434343;
        }
        
        .amount-value {
          color: #000000;
        }
        
        &.discount-row {
          .discount-label {
            .discount-icon {
              color: #0b7f00;
            }
          }
        }
        
        &.total-discount-row {
          border-top: 2px dashed #000000;
        }
      }
    }
    
    .total-amount {
      .total-row {
        .total-label {
          color: #000000;
        }
        
        .total-value {
          color: #d4380d;
        }
      }
      
      .savings-info {
        .elder-text {
          color: #0b7f00;
        }
      }
    }
    
    .payment-section {
      .payment-btn {
        background: #0050b3;
        border-color: #0050b3;
        color: #ffffff;
        
        &:hover:not(:disabled) {
          background: #003a8c;
          border-color: #003a8c;
        }
        
        &:disabled {
          background: #f5f5f5;
          border-color: #000000;
          color: #434343;
        }
      }
    }
    
    .order-notes {
      border-top: 2px solid #000000;
      
      .notes-row {
        .elder-text {
          color: #434343;
        }
      }
    }
  }
}

// 打印样式
@media print {
  .order-summary {
    .payment-section {
      display: none;
    }
    
    .order-notes {
      display: none;
    }
  }
} 