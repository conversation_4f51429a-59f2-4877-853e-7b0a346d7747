import React, { useState, useEffect, useCallback } from 'react';
import { 
  List, 
  Select, 
  DatePicker, 
  Input, 
  Tag, 
  Empty, 
  Space, 
  Pagination,
  Drawer,
  Descriptions,
  Button
} from 'antd';
import { 
  HistoryOutlined,
  SearchOutlined,
  EyeOutlined,
  PrinterOutlined,
  FilterOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { setLoading } from '../../store/slices/orderSlice';
import { ElderButton, ElderCard } from '../elder';
import type { Order } from '../../store/slices/orderSlice';
import './OrderHistory.less';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface OrderHistoryProps {
  className?: string;
  maxHeight?: number;
}

/**
 * 订单历史组件
 * 
 * 功能特性：
 * - 订单列表展示
 * - 订单搜索和筛选
 * - 订单详情查看
 * - 订单状态管理
 * - 分页功能
 * - 适老化设计
 */
const OrderHistory: React.FC<OrderHistoryProps> = ({
  className = '',
  maxHeight = 600,
}) => {
  const dispatch = useAppDispatch();
  
  // 获取订单状态
  const { orderHistory, loading } = useAppSelector(state => state.order);
  
  // 本地状态
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<[any, any] | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetail, setShowOrderDetail] = useState(false);

  // 筛选订单
  const filterOrders = useCallback(() => {
    let filtered = [...orderHistory];

    // 按关键词筛选
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(order =>
        order.orderNo.toLowerCase().includes(keyword) ||
        order.memberName?.toLowerCase().includes(keyword) ||
        order.items.some(item => item.name.toLowerCase().includes(keyword))
      );
    }

    // 按状态筛选
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.paymentStatus === statusFilter);
    }

    // 按日期范围筛选
    if (dateRange && dateRange[0] && dateRange[1]) {
      const startDate = dateRange[0].startOf('day');
      const endDate = dateRange[1].endOf('day');
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.createdAt);
        return orderDate >= startDate.toDate() && orderDate <= endDate.toDate();
      });
    }

    // 按创建时间倒序排列
    filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    setFilteredOrders(filtered);
    setCurrentPage(1); // 重置到第一页
  }, [orderHistory, searchKeyword, statusFilter, dateRange]);

  // 监听筛选条件变化
  useEffect(() => {
    filterOrders();
  }, [filterOrders]);

  // 获取当前页订单
  const currentOrders = filteredOrders.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // 格式化金额
  const formatAmount = (amount: number): string => {
    return `¥${amount.toFixed(2)}`;
  };

  // 格式化日期
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取支付状态标签
  const getPaymentStatusTag = (status: Order['paymentStatus']) => {
    const statusConfig = {
      pending: { color: 'orange', text: '待支付' },
      paid: { color: 'green', text: '已支付' },
      refunded: { color: 'red', text: '已退款' },
    };
    
    const config = statusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取支付方式文本
  const getPaymentMethodText = (method: Order['paymentMethod']): string => {
    const methodMap = {
      cash: '现金',
      wechat: '微信支付',
      alipay: '支付宝',
      card: '储值卡',
    };
    return methodMap[method] || method;
  };

  // 查看订单详情
  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowOrderDetail(true);
  };

  // 打印订单
  const handlePrintOrder = (order: Order) => {
    // TODO: 实现订单打印功能
    console.log('打印订单:', order.orderNo);
  };

  // 清空筛选条件
  const handleClearFilters = () => {
    setSearchKeyword('');
    setStatusFilter('all');
    setDateRange(null);
  };

  // 渲染订单项
  const renderOrderItem = (order: Order) => (
    <List.Item
      key={order.id}
      className="order-item"
      actions={[
        <ElderButton
          key="view"
          type="text"
          icon={<EyeOutlined />}
          onClick={() => handleViewOrder(order)}
          title="查看详情"
        />,
        <ElderButton
          key="print"
          type="text"
          icon={<PrinterOutlined />}
          onClick={() => handlePrintOrder(order)}
          title="打印订单"
        />,
      ]}
    >
      <List.Item.Meta
        title={
          <div className="order-title">
            <span className="order-no elder-text elder-text-base elder-text-bold">
              {order.orderNo}
            </span>
            {getPaymentStatusTag(order.paymentStatus)}
          </div>
        }
        description={
          <div className="order-info">
            <div className="order-row">
              <span className="elder-text elder-text-sm elder-text-secondary">
                时间：{formatDate(order.createdAt)}
              </span>
            </div>
            <div className="order-row">
              <span className="elder-text elder-text-sm elder-text-secondary">
                收银员：{order.cashierName}
              </span>
              {order.memberName && (
                <span className="elder-text elder-text-sm elder-text-secondary">
                  会员：{order.memberName}
                </span>
              )}
            </div>
            <div className="order-row">
              <span className="elder-text elder-text-sm elder-text-secondary">
                商品：{order.items.length} 种，共 {order.items.reduce((sum, item) => sum + item.quantity, 0)} 件
              </span>
            </div>
            <div className="order-row">
              <span className="elder-text elder-text-sm elder-text-secondary">
                支付方式：{getPaymentMethodText(order.paymentMethod)}
              </span>
            </div>
          </div>
        }
      />
      <div className="order-amount">
        <span className="elder-text elder-text-lg elder-text-bold elder-text-primary">
          {formatAmount(order.totalAmount)}
        </span>
      </div>
    </List.Item>
  );

  return (
    <div className={`order-history ${className}`} style={{ maxHeight }}>
      {/* 头部和筛选 */}
      <ElderCard className="history-header" size="default">
        <div className="header-title">
          <HistoryOutlined className="history-icon" />
          <span className="elder-text elder-text-lg elder-text-bold">
            订单历史 ({filteredOrders.length})
          </span>
        </div>

        <div className="search-filters">
          {/* 搜索框 */}
          <Input
            placeholder="搜索订单号、会员或商品名称"
            prefix={<SearchOutlined />}
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            className="search-input elder-input"
            allowClear
          />

          {/* 状态筛选 */}
          <Select
            value={statusFilter}
            onChange={setStatusFilter}
            className="status-filter elder-select"
            style={{ width: 120 }}
          >
            <Option value="all">全部状态</Option>
            <Option value="pending">待支付</Option>
            <Option value="paid">已支付</Option>
            <Option value="refunded">已退款</Option>
          </Select>

          {/* 日期筛选 */}
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            className="date-filter elder-date-picker"
            placeholder={['开始日期', '结束日期']}
          />

          {/* 操作按钮 */}
          <Space>
            <ElderButton
              icon={<FilterOutlined />}
              onClick={handleClearFilters}
              title="清空筛选"
            >
              清空
            </ElderButton>
            <ElderButton
              icon={<ReloadOutlined />}
              onClick={() => {
                dispatch(setLoading({ type: 'fetching', value: true }));
                // TODO: 重新加载订单数据
                setTimeout(() => {
                  dispatch(setLoading({ type: 'fetching', value: false }));
                }, 1000);
              }}
              loading={loading.fetching}
              title="刷新"
            />
          </Space>
        </div>
      </ElderCard>

      {/* 订单列表 */}
      <div className="order-list">
        {currentOrders.length > 0 ? (
          <>
            <List
              dataSource={currentOrders}
              renderItem={renderOrderItem}
              loading={loading.fetching}
              className="elder-order-list"
            />

            {/* 分页 */}
            <div className="pagination-wrapper">
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={filteredOrders.length}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }
                onChange={setCurrentPage}
                onShowSizeChange={(current, size) => {
                  setPageSize(size);
                  setCurrentPage(1);
                }}
                className="elder-pagination"
              />
            </div>
          </>
        ) : (
          <Empty
            description={
              <span className="elder-text elder-text-base elder-text-secondary">
                {orderHistory.length === 0 ? '暂无订单记录' : '没有符合条件的订单'}
              </span>
            }
            image={<HistoryOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
          />
        )}
      </div>

      {/* 订单详情抽屉 */}
      <Drawer
        title="订单详情"
        placement="right"
        width={600}
        open={showOrderDetail}
        onClose={() => setShowOrderDetail(false)}
        className="elder-drawer"
      >
        {selectedOrder && (
          <div className="order-detail">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="订单号">{selectedOrder.orderNo}</Descriptions.Item>
              <Descriptions.Item label="创建时间">{formatDate(selectedOrder.createdAt)}</Descriptions.Item>
              <Descriptions.Item label="支付状态">{getPaymentStatusTag(selectedOrder.paymentStatus)}</Descriptions.Item>
              <Descriptions.Item label="支付方式">{getPaymentMethodText(selectedOrder.paymentMethod)}</Descriptions.Item>
              <Descriptions.Item label="收银员">{selectedOrder.cashierName}</Descriptions.Item>
              {selectedOrder.memberName && (
                <Descriptions.Item label="会员">{selectedOrder.memberName}</Descriptions.Item>
              )}
              <Descriptions.Item label="商品小计">{formatAmount(selectedOrder.subtotal)}</Descriptions.Item>
              <Descriptions.Item label="优惠金额">{formatAmount(selectedOrder.discount)}</Descriptions.Item>
              <Descriptions.Item label="实付金额">{formatAmount(selectedOrder.totalAmount)}</Descriptions.Item>
            </Descriptions>

            <div className="order-items">
              <h4 className="elder-text elder-text-base elder-text-bold">订单商品</h4>
              <List
                dataSource={selectedOrder.items}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      title={item.name}
                      description={`${item.categoryName} | 单价: ${formatAmount(item.price)}`}
                    />
                    <div>
                      <span>x{item.quantity}</span>
                      <span style={{ marginLeft: 16, fontWeight: 'bold' }}>
                        {formatAmount(item.totalPrice)}
                      </span>
                    </div>
                  </List.Item>
                )}
              />
            </div>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default OrderHistory; 