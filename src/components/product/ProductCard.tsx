import React, { useState, useCallback } from 'react';
import { Card, Image, Tag, Rate, Tooltip, message } from 'antd';
import { 
  ShoppingCartOutlined,
  HeartOutlined,
  HeartFilled,
  InfoCircleOutlined,
  FireOutlined,
  StarOutlined,
  TagOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { addToCart } from '../../store/slices/orderSlice';
import { toggleFavorite } from '../../store/slices/productSlice';
import { ElderButton, ElderCard } from '../elder';
import type { Product } from '../../store/slices/productSlice';
import './ProductCard.less';

export interface ProductCardProps {
  product: Product;
  size?: 'small' | 'default' | 'large';
  showQuickAdd?: boolean;
  showFavorite?: boolean;
  showDetails?: boolean;
  onClick?: (product: Product) => void;
  className?: string;
}

/**
 * 商品卡片组件
 * 
 * 功能特性：
 * - 商品信息展示
 * - 快速添加到购物车
 * - 收藏/取消收藏
 * - 商品详情查看
 * - 库存状态显示
 * - 价格和折扣显示
 * - 适老化设计
 */
const ProductCard: React.FC<ProductCardProps> = ({
  product,
  size = 'default',
  showQuickAdd = true,
  showFavorite = true,
  showDetails = true,
  onClick,
  className = '',
}) => {
  const dispatch = useAppDispatch();
  
  // 获取收藏状态
  const { favoriteProducts } = useAppSelector(state => state.product);
  const isFavorite = favoriteProducts.includes(product.id);
  
  // 本地状态
  const [imageLoading, setImageLoading] = useState(true);
  const [addingToCart, setAddingToCart] = useState(false);

  // 处理添加到购物车
  const handleAddToCart = useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (product.stock <= 0) {
      message.warning('商品库存不足');
      return;
    }

    setAddingToCart(true);
    
    try {
      dispatch(addToCart({
        id: `${product.id}_${Date.now()}`,
        productId: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        unit: product.unit || '份',
        categoryId: product.categoryId || '',
        categoryName: product.categoryName || '其他',
      }));
      message.success(`已添加"${product.name}"到购物车`);
    } catch (error) {
      message.error('添加到购物车失败');
    } finally {
      setAddingToCart(false);
    }
  }, [dispatch, product]);

  // 处理收藏切换
  const handleToggleFavorite = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    dispatch(toggleFavorite(product.id));
    
    const action = isFavorite ? '取消收藏' : '收藏';
    message.success(`已${action}"${product.name}"`);
  }, [dispatch, product.id, product.name, isFavorite]);

  // 处理卡片点击
  const handleCardClick = useCallback(() => {
    onClick?.(product);
  }, [onClick, product]);

  // 格式化价格
  const formatPrice = (price: number): string => {
    return `¥${price.toFixed(2)}`;
  };

  // 渲染商品标签
  const renderProductTags = () => {
    const tags = [];
    
    if (product.isRecommended) {
      tags.push(
        <Tag key="recommended" color="gold" icon={<StarOutlined />} className="product-tag">
          推荐
        </Tag>
      );
    }
    
    if (product.isHot) {
      tags.push(
        <Tag key="hot" color="red" icon={<FireOutlined />} className="product-tag">
          热门
        </Tag>
      );
    }
    
    if (product.isNew) {
      tags.push(
        <Tag key="new" color="green" icon={<TagOutlined />} className="product-tag">
          新品
        </Tag>
      );
    }
    
    return tags;
  };

  // 渲染辣度指示器
  const renderSpicyLevel = () => {
    if (!product.spicyLevel) return null;
    
    const spicyLabels = ['不辣', '微辣', '中辣', '很辣'];
    const spicyColors = ['#52c41a', '#faad14', '#ff7875', '#f5222d'];
    
    return (
      <div className="spicy-level">
        <span className="spicy-dots">
          {Array.from({ length: 4 }, (_, index) => (
            <span
              key={index}
              className={`spicy-dot ${index < product.spicyLevel! ? 'active' : ''}`}
              style={{ 
                backgroundColor: index < product.spicyLevel! 
                  ? spicyColors[product.spicyLevel! - 1] 
                  : '#f0f0f0' 
              }}
            />
          ))}
        </span>
        <span className="elder-text elder-text-xs elder-text-secondary">
          {spicyLabels[product.spicyLevel - 1]}
        </span>
      </div>
    );
  };

  // 渲染准备时间
  const renderPreparationTime = () => {
    if (!product.preparationTime) return null;
    
    return (
      <div className="preparation-time">
        <ClockCircleOutlined />
        <span className="elder-text elder-text-xs elder-text-secondary">
          {product.preparationTime}分钟
        </span>
      </div>
    );
  };

  // 计算折扣信息
  const hasDiscount = product.discount && product.discount > 0;
  const discountPercent = hasDiscount ? Math.round(product.discount * 100) : 0;

  return (
    <ElderCard
      className={`product-card ${size} ${className} ${product.stock <= 0 ? 'out-of-stock' : ''}`}
      hoverable
      onClick={handleCardClick}
      size={size === 'large' ? 'large' : 'default'}
      cover={
        <div className="product-image-container">
          <Image
            src={product.image}
            alt={product.name}
            preview={false}
            fallback="/images/placeholder-dish.png"
            onLoad={() => setImageLoading(false)}
            onError={() => setImageLoading(false)}
            className="product-image"
          />
          
          {/* 库存不足遮罩 */}
          {product.stock <= 0 && (
            <div className="stock-overlay">
              <span className="elder-text elder-text-lg elder-text-bold">
                暂时缺货
              </span>
            </div>
          )}
          
          {/* 折扣标签 */}
          {hasDiscount && (
            <div className="discount-badge">
              <span className="elder-text elder-text-sm elder-text-bold">
                -{discountPercent}%
              </span>
            </div>
          )}
          
          {/* 收藏按钮 */}
          {showFavorite && (
            <div className="favorite-btn">
              <ElderButton
                type="text"
                icon={isFavorite ? <HeartFilled /> : <HeartOutlined />}
                onClick={handleToggleFavorite}
                className={`heart-btn ${isFavorite ? 'favorited' : ''}`}
                size="default"
              />
            </div>
          )}
        </div>
      }
      actions={
        showQuickAdd ? [
          <ElderButton
            key="add-cart"
            type="primary"
            icon={<ShoppingCartOutlined />}
            onClick={handleAddToCart}
            loading={addingToCart}
            disabled={product.stock <= 0}
            size={size === 'small' ? 'default' : 'large'}
            className="add-cart-btn"
          >
            {size === 'small' ? '加购' : '加入购物车'}
          </ElderButton>
        ] : undefined
      }
    >
      {/* 商品标签 */}
      <div className="product-tags">
        {renderProductTags()}
      </div>

      {/* 商品名称 */}
      <div className="product-name">
        <Tooltip title={product.name} placement="top">
          <span className="elder-text elder-text-base elder-text-bold">
            {product.name}
          </span>
        </Tooltip>
        {showDetails && (
          <Tooltip title={product.description} placement="bottom">
            <InfoCircleOutlined className="info-icon" />
          </Tooltip>
        )}
      </div>

      {/* 商品描述 */}
      <div className="product-description">
        <span className="elder-text elder-text-sm elder-text-secondary">
          {product.description}
        </span>
      </div>

      {/* 价格信息 */}
      <div className="product-price">
        {hasDiscount && product.originalPrice ? (
          <div className="price-with-discount">
            <span className="elder-text elder-text-lg elder-text-bold elder-text-error">
              {formatPrice(product.price)}
            </span>
            <span className="elder-text elder-text-sm elder-text-secondary original-price">
              {formatPrice(product.originalPrice)}
            </span>
          </div>
        ) : (
          <span className="elder-text elder-text-lg elder-text-bold elder-text-primary">
            {formatPrice(product.price)}
          </span>
        )}
        <span className="elder-text elder-text-xs elder-text-secondary">
          / {product.unit}
        </span>
      </div>

      {/* 评分和评价 */}
      {product.rating && (
        <div className="product-rating">
          <Rate disabled value={product.rating} allowHalf className="elder-rate" />
          <span className="elder-text elder-text-xs elder-text-secondary">
            {product.rating} ({product.reviewCount || 0})
          </span>
        </div>
      )}

      {/* 商品详细信息 */}
      {showDetails && (
        <div className="product-details">
          <div className="details-row">
            {renderSpicyLevel()}
            {renderPreparationTime()}
          </div>
          
          <div className="stock-info">
            <span className="elder-text elder-text-xs elder-text-secondary">
              库存: {product.stock} {product.unit}
            </span>
          </div>
        </div>
      )}
    </ElderCard>
  );
};

export default ProductCard; 