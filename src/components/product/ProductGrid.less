// 商品网格组件样式
.product-grid {
  // 商品容器
  .products-container {
    min-height: 400px;

    // 商品行
    .products-row {
      .product-item {
        margin-bottom: 16px;
        height: 100%;

        // 网格视图
        &.grid {
          .ant-card {
            height: 100%;
            display: flex;
            flex-direction: column;

            .ant-card-body {
              flex: 1;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
            }
          }
        }

        // 列表视图
        &.list {
          .ant-card {
            .ant-card-cover {
              float: left;
              width: 200px;
              margin-right: 16px;
            }

            .ant-card-body {
              overflow: hidden;
              min-height: 120px;
            }

            .ant-card-actions {
              clear: both;
            }
          }
        }
      }
    }

    // 不同网格尺寸
    &.small {
      .products-row {
        .product-item {
          margin-bottom: 12px;
        }
      }
    }

    &.medium {
      .products-row {
        .product-item {
          margin-bottom: 16px;
        }
      }
    }

    &.large {
      .products-row {
        .product-item {
          margin-bottom: 20px;
        }
      }
    }
  }

  // 骨架屏
  .product-skeleton {
    .skeleton-card {
      border-radius: var(--elder-border-radius);
      overflow: hidden;
      background: var(--elder-background-color);
      border: 1px solid var(--elder-border-color);

      .skeleton-image {
        width: 100%;
        height: 160px;
        background: var(--elder-background-color-secondary);
        animation: skeleton-pulse 1.5s ease-in-out infinite;
      }

      .skeleton-content {
        padding: 12px;

        .skeleton-title {
          height: 16px;
          background: var(--elder-background-color-secondary);
          border-radius: 4px;
          margin-bottom: 8px;
          animation: skeleton-pulse 1.5s ease-in-out infinite;
        }

        .skeleton-description {
          height: 12px;
          background: var(--elder-background-color-secondary);
          border-radius: 4px;
          margin-bottom: 8px;
          width: 80%;
          animation: skeleton-pulse 1.5s ease-in-out infinite;
        }

        .skeleton-price {
          height: 18px;
          background: var(--elder-background-color-secondary);
          border-radius: 4px;
          width: 60%;
          animation: skeleton-pulse 1.5s ease-in-out infinite;
        }
      }
    }
  }

  // 空状态
  .products-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    background: var(--elder-background-color);
    border-radius: var(--elder-border-radius);
    border: 1px dashed var(--elder-border-color);

    .ant-empty {
      .ant-empty-image {
        margin-bottom: 16px;
      }

      .empty-description {
        text-align: center;

        .elder-text {
          display: block;
          margin-bottom: 4px;

          &:first-child {
            font-size: var(--elder-font-size-lg);
            margin-bottom: 8px;
          }
        }
      }
    }
  }

  // 错误状态
  .products-error {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    background: var(--elder-error-background);
    border-radius: var(--elder-border-radius);
    border: 1px solid var(--elder-error-border);

    .ant-empty {
      .empty-description .elder-text {
        color: var(--elder-error-color);
      }
    }
  }

  // 分页组件
  .products-pagination {
    margin-top: 24px;
    padding: 16px 0;
    text-align: center;
    border-top: 1px solid var(--elder-border-color);

    .elder-pagination {
      .ant-pagination-item {
        min-width: 40px;
        height: 40px;
        line-height: 38px;
        border-radius: var(--elder-border-radius-small);
        font-size: var(--elder-font-size-base);

        a {
          color: var(--elder-text-color);
        }

        &.ant-pagination-item-active {
          background: var(--elder-primary-color);
          border-color: var(--elder-primary-color);

          a {
            color: var(--elder-white);
          }
        }

        &:hover {
          border-color: var(--elder-primary-color);

          a {
            color: var(--elder-primary-color);
          }
        }
      }

      .ant-pagination-prev,
      .ant-pagination-next,
      .ant-pagination-jump-prev,
      .ant-pagination-jump-next {
        min-width: 40px;
        height: 40px;
        line-height: 38px;
        border-radius: var(--elder-border-radius-small);

        .ant-pagination-item-link {
          border: none;
          background: transparent;
          color: var(--elder-text-color);
          font-size: 16px;
        }

        &:hover {
          border-color: var(--elder-primary-color);

          .ant-pagination-item-link {
            color: var(--elder-primary-color);
          }
        }

        &.ant-pagination-disabled {
          .ant-pagination-item-link {
            color: var(--elder-text-color-disabled);
          }

          &:hover {
            border-color: var(--elder-border-color);

            .ant-pagination-item-link {
              color: var(--elder-text-color-disabled);
            }
          }
        }
      }

      .ant-pagination-options {
        .ant-select {
          margin-left: 8px;

          .ant-select-selector {
            height: 40px;
            border-radius: var(--elder-border-radius-small);
            font-size: var(--elder-font-size-base);
          }

          .ant-select-selection-item {
            line-height: 38px;
          }
        }

        .ant-pagination-simple-pager {
          height: 40px;

          input {
            height: 40px;
            border-radius: var(--elder-border-radius-small);
            font-size: var(--elder-font-size-base);
            text-align: center;
          }
        }
      }

      .ant-pagination-total-text {
        color: var(--elder-text-color-secondary);
        font-size: var(--elder-font-size-sm);
        margin-right: 16px;
      }
    }
  }

  // 加载更多指示器
  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px;
    margin-top: 16px;
    background: var(--elder-background-color);
    border-radius: var(--elder-border-radius);
    border: 1px dashed var(--elder-border-color);

    .ant-spin {
      .ant-spin-dot {
        font-size: 16px;
        
        .ant-spin-dot-item {
          background-color: var(--elder-primary-color);
        }
      }
    }

    .elder-text {
      color: var(--elder-text-color-secondary);
    }
  }

  // 高对比度模式
  .elder-high-contrast & {
    .products-empty,
    .loading-more {
      border: 2px solid var(--elder-border-color);
    }

    .products-error {
      border: 2px solid var(--elder-error-border);
    }

    .products-pagination {
      border-top: 2px solid var(--elder-border-color);

      .elder-pagination {
        .ant-pagination-item {
          border: 2px solid var(--elder-border-color);

          &.ant-pagination-item-active {
            border: 2px solid var(--elder-primary-color);
          }
        }

        .ant-pagination-prev,
        .ant-pagination-next {
          border: 2px solid var(--elder-border-color);
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .products-container {
      &.list {
        .products-row {
          .product-item.list {
            .ant-card {
              .ant-card-cover {
                width: 120px;
                margin-right: 12px;
              }

              .ant-card-body {
                min-height: 100px;
                padding: 8px;
              }
            }
          }
        }
      }
    }

    .products-pagination {
      margin-top: 16px;
      padding: 12px 0;

      .elder-pagination {
        .ant-pagination-item,
        .ant-pagination-prev,
        .ant-pagination-next {
          min-width: 36px;
          height: 36px;
          line-height: 34px;
          font-size: var(--elder-font-size-sm);
        }

        .ant-pagination-options {
          .ant-select .ant-select-selector {
            height: 36px;
            font-size: var(--elder-font-size-sm);
          }

          .ant-pagination-simple-pager input {
            height: 36px;
            font-size: var(--elder-font-size-sm);
          }
        }

        .ant-pagination-total-text {
          font-size: var(--elder-font-size-xs);
          margin-right: 8px;
        }
      }
    }

    .loading-more {
      padding: 12px;
      margin-top: 12px;
    }
  }

  @media (max-width: 480px) {
    .products-container {
      &.list {
        .products-row {
          .product-item.list {
            .ant-card {
              .ant-card-cover {
                float: none;
                width: 100%;
                margin-right: 0;
                margin-bottom: 8px;
              }

              .ant-card-body {
                min-height: auto;
                padding: 6px;
              }
            }
          }
        }
      }

      .products-row {
        margin: 0 -6px;

        .product-item {
          padding: 0 6px;
          margin-bottom: 12px;
        }
      }
    }

    .products-empty {
      min-height: 200px;
      margin: 0 8px;

      .ant-empty {
        .empty-description .elder-text:first-child {
          font-size: var(--elder-font-size-base);
        }
      }
    }

    .products-pagination {
      margin-top: 12px;
      padding: 8px 0;

      .elder-pagination {
        .ant-pagination-item,
        .ant-pagination-prev,
        .ant-pagination-next {
          min-width: 32px;
          height: 32px;
          line-height: 30px;
          font-size: var(--elder-font-size-xs);
        }

        .ant-pagination-options {
          margin-top: 8px;

          .ant-select .ant-select-selector {
            height: 32px;
            font-size: var(--elder-font-size-xs);
          }

          .ant-pagination-simple-pager input {
            height: 32px;
            font-size: var(--elder-font-size-xs);
          }
        }

        .ant-pagination-total-text {
          display: block;
          text-align: center;
          margin: 0 0 8px 0;
        }
      }
    }

    .loading-more {
      padding: 8px;
      margin-top: 8px;
    }
  }
}

// 骨架屏动画
@keyframes skeleton-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
} 