import React, { useEffect, useCallback } from 'react';
import { Tabs, Space, Badge, Tooltip, Skeleton } from 'antd';
import { 
  AppstoreOutlined,
  UnorderedListOutlined,
  FilterOutlined,
  ReloadOutlined,
  FireOutlined,
  StarOutlined,
  TagOutlined
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { 
  setCurrentCategory, 
  setViewMode, 
  toggleFilters,
  fetchCategories,
  clearFilters,
  fetchProducts 
} from '../../store/slices/productSlice';
import { ElderButton } from '../elder';
import './CategoryNav.less';

export interface CategoryNavProps {
  className?: string;
  showViewControls?: boolean;
  showFilterToggle?: boolean;
  showQuickFilters?: boolean;
}

/**
 * 商品分类导航组件
 * 
 * 功能特性：
 * - 分类选择切换
 * - 视图模式切换（网格/列表）
 * - 筛选器开关
 * - 快速筛选按钮
 * - 刷新功能
 * - 适老化设计
 */
const CategoryNav: React.FC<CategoryNavProps> = ({
  className = '',
  showViewControls = true,
  showFilterToggle = true,
  showQuickFilters = true,
}) => {
  const dispatch = useAppDispatch();
  
  // 获取商品状态
  const { 
    categories, 
    currentCategory, 
    viewMode, 
    showFilters,
    filters,
    loading 
  } = useAppSelector(state => state.product);

  // 初始化分类数据
  useEffect(() => {
    if (categories.length === 0) {
      dispatch(fetchCategories());
    }
  }, [dispatch, categories.length]);

  // 处理分类切换
  const handleCategoryChange = useCallback((categoryId: string) => {
    const targetCategory = categoryId === 'all' ? null : categoryId;
    dispatch(setCurrentCategory(targetCategory));
    
    // 重新获取商品数据
    dispatch(fetchProducts({
      filters: { ...filters, categoryId: targetCategory || undefined },
    }));
  }, [dispatch, filters]);

  // 处理视图模式切换
  const handleViewModeChange = useCallback((mode: 'grid' | 'list') => {
    dispatch(setViewMode(mode));
  }, [dispatch]);

  // 处理筛选器切换
  const handleFilterToggle = useCallback(() => {
    dispatch(toggleFilters());
  }, [dispatch]);

  // 处理刷新
  const handleRefresh = useCallback(() => {
    dispatch(fetchCategories());
    dispatch(fetchProducts({}));
  }, [dispatch]);

  // 处理快速筛选
  const handleQuickFilter = useCallback((filterType: string) => {
    let newFilters = {};
    
    switch (filterType) {
      case 'recommended':
        newFilters = { isRecommended: true };
        break;
      case 'hot':
        newFilters = { isHot: true };
        break;
      case 'new':
        newFilters = { isNew: true };
        break;
      case 'inStock':
        newFilters = { inStock: true };
        break;
      default:
        break;
    }
    
    dispatch(fetchProducts({
      filters: { ...filters, ...newFilters },
    }));
  }, [dispatch, filters]);

  // 清除所有筛选
  const handleClearFilters = useCallback(() => {
    dispatch(clearFilters());
    dispatch(fetchProducts({}));
  }, [dispatch]);

  // 准备分类标签页数据
  const categoryTabs = [
    {
      key: 'all',
      label: (
        <div className="category-tab">
          <AppstoreOutlined />
          <span className="elder-text elder-text-base">全部</span>
        </div>
      ),
    },
    ...categories.map(category => ({
      key: category.id,
      label: (
        <div className="category-tab">
          <span className="category-icon">{category.icon}</span>
          <span className="elder-text elder-text-base">{category.name}</span>
          <Badge 
            count={category.productCount} 
            size="small" 
            className="product-count"
            overflowCount={999}
          />
        </div>
      ),
    }))
  ];

  // 活跃筛选器数量
  const activeFiltersCount = Object.keys(filters).length;

  return (
    <div className={`category-nav ${className}`}>
      {/* 分类选择器 */}
      <div className="category-tabs">
        {loading.categories ? (
          <Skeleton.Button active style={{ width: '100%', height: 48 }} />
        ) : (
          <Tabs
            activeKey={currentCategory || 'all'}
            onChange={handleCategoryChange}
            size="large"
            className="elder-tabs"
            items={categoryTabs}
            tabBarGutter={8}
          />
        )}
      </div>

      {/* 控制工具栏 */}
      <div className="nav-controls">
        {/* 视图模式切换 */}
        {showViewControls && (
          <div className="view-controls">
            <Tooltip title="网格视图">
              <ElderButton
                type={viewMode === 'grid' ? 'primary' : 'default'}
                icon={<AppstoreOutlined />}
                onClick={() => handleViewModeChange('grid')}
                size="large"
                className="view-btn"
              />
            </Tooltip>
            <Tooltip title="列表视图">
              <ElderButton
                type={viewMode === 'list' ? 'primary' : 'default'}
                icon={<UnorderedListOutlined />}
                onClick={() => handleViewModeChange('list')}
                size="large"
                className="view-btn"
              />
            </Tooltip>
          </div>
        )}

        {/* 筛选器开关 */}
        {showFilterToggle && (
          <div className="filter-controls">
            <ElderButton
              type={showFilters ? 'primary' : 'default'}
              icon={<FilterOutlined />}
              onClick={handleFilterToggle}
              size="large"
              className="filter-btn"
            >
              筛选
              {activeFiltersCount > 0 && (
                <Badge count={activeFiltersCount} size="small" offset={[8, -8]} />
              )}
            </ElderButton>
          </div>
        )}

        {/* 刷新按钮 */}
        <div className="refresh-controls">
          <Tooltip title="刷新数据">
            <ElderButton
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading.categories || loading.products}
              size="large"
              className="refresh-btn"
            />
          </Tooltip>
        </div>
      </div>

      {/* 快速筛选按钮 */}
      {showQuickFilters && (
        <div className="quick-filters">
          <Space wrap size={8}>
            <ElderButton
              size="default"
              icon={<StarOutlined />}
              onClick={() => handleQuickFilter('recommended')}
              className={filters.isRecommended ? 'active' : ''}
            >
              推荐
            </ElderButton>
            
            <ElderButton
              size="default"
              icon={<FireOutlined />}
              onClick={() => handleQuickFilter('hot')}
              className={filters.isHot ? 'active' : ''}
            >
              热门
            </ElderButton>
            
            <ElderButton
              size="default"
              icon={<TagOutlined />}
              onClick={() => handleQuickFilter('new')}
              className={filters.isNew ? 'active' : ''}
            >
              新品
            </ElderButton>
            
            <ElderButton
              size="default"
              onClick={() => handleQuickFilter('inStock')}
              className={filters.inStock ? 'active' : ''}
            >
              有库存
            </ElderButton>

            {activeFiltersCount > 0 && (
              <ElderButton
                size="default"
                type="text"
                onClick={handleClearFilters}
                className="clear-filters"
              >
                清除筛选
              </ElderButton>
            )}
          </Space>
        </div>
      )}
    </div>
  );
};

export default CategoryNav; 