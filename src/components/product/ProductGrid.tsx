import React, { useEffect, useCallback, useMemo } from 'react';
import { Row, Col, Spin, Empty, Pagination, Skeleton } from 'antd';
import { AppstoreOutlined } from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { 
  fetchProducts, 
  setPage, 
  setPageSize 
} from '../../store/slices/productSlice';
import ProductCard from './ProductCard';
import type { Product } from '../../store/slices/productSlice';
import './ProductGrid.less';

export interface ProductGridProps {
  className?: string;
  onProductClick?: (product: Product) => void;
  showPagination?: boolean;
  pageSize?: number;
}

/**
 * 商品网格展示组件
 * 
 * 功能特性：
 * - 响应式网格布局
 * - 列表视图支持
 * - 分页导航
 * - 加载状态处理
 * - 空状态展示
 * - 图片懒加载
 * - 适老化设计
 */
const ProductGrid: React.FC<ProductGridProps> = ({
  className = '',
  onProductClick,
  showPagination = true,
  pageSize = 20,
}) => {
  const dispatch = useAppDispatch();
  
  // 获取商品状态
  const { 
    products, 
    pagination, 
    loading, 
    error,
    viewMode,
    gridSize,
    currentCategory,
    searchKeyword,
    filters,
    sortBy,
    sortOrder 
  } = useAppSelector(state => state.product);

  // 初始化商品数据
  useEffect(() => {
    dispatch(fetchProducts({
      keyword: searchKeyword,
      filters: { ...filters, categoryId: currentCategory || undefined },
      sortBy,
      sortOrder,
      page: 1,
      pageSize: pageSize,
    }));
  }, [dispatch, currentCategory, searchKeyword, filters, sortBy, sortOrder, pageSize]);

  // 处理分页变化
  const handlePageChange = useCallback((page: number, size?: number) => {
    dispatch(setPage(page));
    
    if (size && size !== pagination.pageSize) {
      dispatch(setPageSize(size));
    }
    
    dispatch(fetchProducts({
      keyword: searchKeyword,
      filters: { ...filters, categoryId: currentCategory || undefined },
      sortBy,
      sortOrder,
      page,
      pageSize: size || pagination.pageSize,
    }));
  }, [dispatch, searchKeyword, filters, currentCategory, sortBy, sortOrder, pagination.pageSize]);

  // 计算网格布局配置
  const gridConfig = useMemo(() => {
    const configs = {
      small: {
        grid: { xs: 12, sm: 8, md: 6, lg: 4, xl: 4, xxl: 3 },
        list: { xs: 24, sm: 24, md: 12, lg: 12, xl: 8, xxl: 6 },
      },
      medium: {
        grid: { xs: 24, sm: 12, md: 8, lg: 6, xl: 6, xxl: 4 },
        list: { xs: 24, sm: 24, md: 24, lg: 12, xl: 12, xxl: 8 },
      },
      large: {
        grid: { xs: 24, sm: 24, md: 12, lg: 8, xl: 6, xxl: 6 },
        list: { xs: 24, sm: 24, md: 24, lg: 24, xl: 12, xxl: 12 },
      },
    };
    
    return configs[gridSize];
  }, [gridSize]);

  // 获取当前列配置
  const colConfig = viewMode === 'grid' ? gridConfig.grid : gridConfig.list;

  // 渲染商品卡片
  const renderProductCard = useCallback((product: Product) => {
    // 映射gridSize到ProductCard的size
    const cardSize = gridSize === 'medium' ? 'default' : gridSize;
    
    return (
      <Col key={product.id} {...colConfig}>
        <ProductCard
          product={product}
          size={cardSize}
          onClick={onProductClick}
          className={`product-item ${viewMode}`}
        />
      </Col>
    );
  }, [colConfig, gridSize, onProductClick, viewMode]);

  // 渲染加载骨架屏
  const renderSkeleton = () => {
    const skeletonCount = Math.min(pageSize, 12);
    
    return (
      <Row gutter={[16, 16]} className="product-skeleton">
        {Array.from({ length: skeletonCount }, (_, index) => (
          <Col key={index} {...colConfig}>
            <Skeleton.Node
              active
              style={{
                width: '100%',
                height: viewMode === 'grid' ? 300 : 150,
              }}
            >
              <div className="skeleton-card">
                <div className="skeleton-image" />
                <div className="skeleton-content">
                  <div className="skeleton-title" />
                  <div className="skeleton-description" />
                  <div className="skeleton-price" />
                </div>
              </div>
            </Skeleton.Node>
          </Col>
        ))}
      </Row>
    );
  };

  // 渲染空状态
  const renderEmpty = () => {
    let description = '暂无商品数据';
    
    if (searchKeyword) {
      description = `未找到与"${searchKeyword}"相关的商品`;
    } else if (currentCategory) {
      const category = products.length > 0 ? products[0].categoryName : '该分类';
      description = `${category}暂无商品`;
    }
    
    return (
      <div className="products-empty">
        <Empty
          image={<AppstoreOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
          description={
            <div className="empty-description">
              <span className="elder-text elder-text-base elder-text-secondary">
                {description}
              </span>
              {searchKeyword && (
                <span className="elder-text elder-text-sm elder-text-secondary">
                  试试其他关键词或浏览分类商品
                </span>
              )}
            </div>
          }
        />
      </div>
    );
  };

  return (
    <div className={`product-grid ${className}`}>
      {/* 商品列表 */}
      <div className={`products-container ${viewMode} ${gridSize}`}>
        {loading.products ? (
          renderSkeleton()
        ) : error ? (
          <div className="products-error">
            <Empty
              description={
                <span className="elder-text elder-text-base elder-text-error">
                  {error}
                </span>
              }
            />
          </div>
        ) : products.length > 0 ? (
          <Row gutter={[16, 16]} className="products-row">
            {products.map(renderProductCard)}
          </Row>
        ) : (
          renderEmpty()
        )}
      </div>

      {/* 分页组件 */}
      {showPagination && products.length > 0 && (
        <div className="products-pagination">
          <Pagination
            current={pagination.page}
            total={pagination.total}
            pageSize={pagination.pageSize}
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => (
              <span className="elder-text elder-text-sm elder-text-secondary">
                第 {range[0]}-{range[1]} 条，共 {total} 条商品
              </span>
            )}
            pageSizeOptions={['12', '20', '36', '48']}
            className="elder-pagination"
            size="default"
          />
        </div>
      )}

      {/* 加载更多指示器（当有更多数据时） */}
      {loading.products && products.length > 0 && (
        <div className="loading-more">
          <Spin size="small" />
          <span className="elder-text elder-text-sm elder-text-secondary">
            加载中...
          </span>
        </div>
      )}
    </div>
  );
};

export default ProductGrid; 