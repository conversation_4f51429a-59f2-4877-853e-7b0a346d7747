// 分类导航组件样式
.category-nav {
  background: var(--elder-background-color);
  border-radius: var(--elder-border-radius);
  overflow: hidden;

  // 分类选择器
  .category-tabs {
    .elder-tabs {
      .ant-tabs-nav {
        margin-bottom: 0;
        
        &::before {
          border-bottom: 1px solid var(--elder-border-color);
        }
      }

      .ant-tabs-tab {
        padding: 12px 16px;
        margin: 0 4px;
        min-width: 80px;
        justify-content: center;

        .category-tab {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;

          .category-icon {
            font-size: 20px;
            line-height: 1;
          }

          .ant-typography,
          .elder-text {
            font-size: var(--elder-font-size-base);
            font-weight: var(--elder-font-weight-normal);
            line-height: 1.2;
            margin: 0;
          }

          .product-count {
            .ant-badge-count {
              font-size: 10px;
              min-width: 16px;
              height: 16px;
              line-height: 14px;
              border-radius: 8px;
            }
          }
        }

        &.ant-tabs-tab-active {
          .category-tab {
            .elder-text {
              color: var(--elder-primary-color);
              font-weight: var(--elder-font-weight-bold);
            }
          }
        }

        &:hover {
          .category-tab {
            .elder-text {
              color: var(--elder-primary-color);
            }
          }
        }
      }

      .ant-tabs-ink-bar {
        background: var(--elder-primary-color);
        height: 3px;
      }
    }
  }

  // 控制工具栏
  .nav-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-top: 1px solid var(--elder-border-color);
    background: var(--elder-background-color-secondary);

    .view-controls,
    .filter-controls,
    .refresh-controls {
      display: flex;
      gap: 8px;
    }

    .view-btn,
    .filter-btn,
    .refresh-btn {
      min-width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;

      .anticon {
        font-size: 16px;
      }
    }

    .filter-btn {
      position: relative;

      .ant-badge {
        .ant-badge-count {
          right: -8px;
          top: -8px;
        }
      }
    }
  }

  // 快速筛选按钮
  .quick-filters {
    padding: 8px 16px;
    border-top: 1px solid var(--elder-border-color);
    background: var(--elder-background-color);

    .ant-space {
      width: 100%;
      flex-wrap: wrap;
    }

    .ant-btn {
      border-radius: var(--elder-border-radius-small);
      font-size: var(--elder-font-size-sm);
      height: 32px;
      min-width: 60px;

      &.active {
        background: var(--elder-primary-color);
        border-color: var(--elder-primary-color);
        color: var(--elder-white);
        
        &:hover {
          background: var(--elder-primary-hover-color);
          border-color: var(--elder-primary-hover-color);
        }
      }

      &.clear-filters {
        color: var(--elder-text-color-secondary);
        border-color: transparent;
        
        &:hover {
          color: var(--elder-error-color);
          background: var(--elder-error-background);
        }
      }
    }
  }

  // 高对比度模式适配
  .elder-high-contrast & {
    border: 2px solid var(--elder-border-color);

    .category-tabs {
      .elder-tabs {
        .ant-tabs-tab {
          border: 1px solid transparent;

          &.ant-tabs-tab-active {
            border-color: var(--elder-primary-color);
            background: var(--elder-primary-background);
          }
        }
      }
    }

    .nav-controls {
      border-top: 2px solid var(--elder-border-color);
    }

    .quick-filters {
      border-top: 2px solid var(--elder-border-color);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .category-tabs {
      .elder-tabs {
        .ant-tabs-tab {
          padding: 8px 12px;
          min-width: 60px;

          .category-tab {
            .category-icon {
              font-size: 16px;
            }

            .elder-text {
              font-size: var(--elder-font-size-sm);
            }
          }
        }
      }
    }

    .nav-controls {
      flex-wrap: wrap;
      gap: 8px;
      padding: 8px 12px;

      .view-controls,
      .filter-controls,
      .refresh-controls {
        gap: 4px;
      }

      .view-btn,
      .filter-btn,
      .refresh-btn {
        min-width: 36px;
        height: 36px;

        .anticon {
          font-size: 14px;
        }
      }
    }

    .quick-filters {
      padding: 6px 12px;

      .ant-btn {
        font-size: var(--elder-font-size-xs);
        height: 28px;
        min-width: 50px;
      }
    }
  }

  @media (max-width: 480px) {
    .category-tabs {
      .elder-tabs {
        .ant-tabs-nav {
          .ant-tabs-nav-wrap {
            .ant-tabs-nav-list {
              .ant-tabs-tab {
                padding: 6px 8px;
                min-width: 50px;

                .category-tab {
                  gap: 2px;

                  .category-icon {
                    font-size: 14px;
                  }

                  .elder-text {
                    font-size: var(--elder-font-size-xs);
                  }

                  .product-count {
                    display: none; // 小屏幕隐藏商品数量
                  }
                }
              }
            }
          }
        }
      }
    }

    .nav-controls {
      .view-controls {
        order: 1;
        flex: 1;
      }

      .filter-controls {
        order: 2;
        flex: 1;
      }

      .refresh-controls {
        order: 3;
        width: 100%;
        justify-content: center;
      }
    }
  }
} 