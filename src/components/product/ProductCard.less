// 商品卡片组件样式
.product-card {
  position: relative;
  border-radius: var(--elder-border-radius);
  transition: all 0.3s ease;
  overflow: hidden;

  // 基础卡片样式
  .ant-card-body {
    padding: 12px;
  }

  // 商品图片容器
  .product-image-container {
    position: relative;
    aspect-ratio: 4/3;
    overflow: hidden;
    background: var(--elder-background-color-secondary);

    .product-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    // 库存不足遮罩
    .stock-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2;

      .elder-text {
        color: var(--elder-white);
        text-align: center;
        padding: 8px;
        background: rgba(0, 0, 0, 0.8);
        border-radius: var(--elder-border-radius-small);
      }
    }

    // 折扣标签
    .discount-badge {
      position: absolute;
      top: 8px;
      left: 8px;
      background: var(--elder-error-color);
      color: var(--elder-white);
      padding: 4px 8px;
      border-radius: var(--elder-border-radius-small);
      z-index: 3;

      .elder-text {
        color: inherit;
      }
    }

    // 收藏按钮
    .favorite-btn {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 3;

      .heart-btn {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.9);
        color: var(--elder-text-color-secondary);
        border: none;
        transition: all 0.3s ease;

        &:hover {
          background: var(--elder-white);
          color: var(--elder-error-color);
          transform: scale(1.1);
        }

        &.favorited {
          color: var(--elder-error-color);
          background: var(--elder-white);
        }

        .anticon {
          font-size: 16px;
        }
      }
    }
  }

  // 商品标签
  .product-tags {
    margin-bottom: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .product-tag {
      font-size: var(--elder-font-size-xs);
      padding: 2px 6px;
      border-radius: var(--elder-border-radius-small);
      line-height: 1.2;

      .anticon {
        font-size: 10px;
        margin-right: 2px;
      }
    }
  }

  // 商品名称
  .product-name {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;

    .elder-text {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .info-icon {
      margin-left: 4px;
      color: var(--elder-text-color-secondary);
      font-size: 12px;
      opacity: 0.7;
      transition: opacity 0.3s ease;

      &:hover {
        opacity: 1;
        color: var(--elder-primary-color);
      }
    }
  }

  // 商品描述
  .product-description {
    margin-bottom: 8px;

    .elder-text {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 1.4;
    }
  }

  // 价格信息
  .product-price {
    display: flex;
    align-items: baseline;
    gap: 8px;
    margin-bottom: 8px;

    .price-with-discount {
      display: flex;
      align-items: baseline;
      gap: 8px;

      .original-price {
        text-decoration: line-through;
      }
    }
  }

  // 评分
  .product-rating {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;

    .elder-rate {
      .ant-rate-star {
        font-size: 12px;
        margin-right: 2px;
      }
    }
  }

  // 商品详细信息
  .product-details {
    .details-row {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 4px;

      .spicy-level {
        display: flex;
        align-items: center;
        gap: 4px;

        .spicy-dots {
          display: flex;
          gap: 2px;

          .spicy-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            transition: background-color 0.3s ease;
          }
        }
      }

      .preparation-time {
        display: flex;
        align-items: center;
        gap: 2px;

        .anticon {
          font-size: 10px;
        }
      }
    }

    .stock-info {
      margin-top: 4px;
    }
  }

  // 操作按钮
  .ant-card-actions {
    background: var(--elder-background-color);
    border-top: 1px solid var(--elder-border-color);

    li {
      margin: 8px 0;

      .add-cart-btn {
        width: 100%;
        border-radius: var(--elder-border-radius-small);
        font-weight: var(--elder-font-weight-medium);
      }
    }
  }

  // 悬停效果
  &:hover {
    box-shadow: var(--elder-box-shadow-elevated);
    transform: translateY(-2px);

    .product-image-container {
      .product-image {
        transform: scale(1.05);
      }
    }
  }

  // 缺货状态
  &.out-of-stock {
    opacity: 0.7;

    .product-name .elder-text,
    .product-description .elder-text {
      color: var(--elder-text-color-disabled);
    }

    &:hover {
      transform: none;
      box-shadow: var(--elder-box-shadow);
    }
  }

  // 尺寸变体
  &.small {
    .ant-card-body {
      padding: 8px;
    }

    .product-image-container {
      aspect-ratio: 1;
    }

    .product-description {
      .elder-text {
        -webkit-line-clamp: 1;
      }
    }

    .product-tags .product-tag {
      font-size: 10px;
      padding: 1px 4px;
    }

    .product-details .details-row {
      gap: 8px;
    }
  }

  &.large {
    .ant-card-body {
      padding: 16px;
    }

    .product-image-container {
      aspect-ratio: 3/2;
    }

    .product-tags .product-tag {
      font-size: var(--elder-font-size-sm);
      padding: 4px 8px;
    }

    .product-details .details-row {
      gap: 16px;
    }
  }

  // 列表视图样式
  &.list {
    .product-image-container {
      aspect-ratio: 16/9;
    }

    .ant-card-body {
      .product-tags {
        margin-bottom: 6px;
      }

      .product-description .elder-text {
        -webkit-line-clamp: 1;
      }

      .product-details {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .details-row {
          margin-bottom: 0;
        }
      }
    }

    .ant-card-actions {
      li .add-cart-btn {
        font-size: var(--elder-font-size-sm);
        height: 36px;
      }
    }
  }

  // 高对比度模式
  .elder-high-contrast & {
    border: 2px solid var(--elder-border-color);

    .product-image-container {
      border: 1px solid var(--elder-border-color);

      .favorite-btn .heart-btn {
        border: 1px solid var(--elder-border-color);
      }
    }

    &:hover {
      border-color: var(--elder-primary-color);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .product-image-container {
      .favorite-btn .heart-btn {
        width: 28px;
        height: 28px;

        .anticon {
          font-size: 14px;
        }
      }

      .discount-badge {
        top: 6px;
        left: 6px;
        padding: 2px 6px;
      }
    }

    .product-tags .product-tag {
      font-size: 10px;
      padding: 1px 4px;
    }

    .product-rating .elder-rate {
      .ant-rate-star {
        font-size: 10px;
      }
    }

    .product-details .details-row {
      gap: 8px;

      .spicy-level .spicy-dots .spicy-dot {
        width: 4px;
        height: 4px;
      }
    }

    .ant-card-actions li .add-cart-btn {
      font-size: var(--elder-font-size-xs);
      height: 32px;
    }
  }

  @media (max-width: 480px) {
    &.small {
      .product-tags {
        display: none; // 小屏幕小卡片隐藏标签
      }

      .product-description {
        display: none; // 小屏幕小卡片隐藏描述
      }
    }

    .product-image-container {
      aspect-ratio: 1;

      .favorite-btn {
        top: 4px;
        right: 4px;

        .heart-btn {
          width: 24px;
          height: 24px;

          .anticon {
            font-size: 12px;
          }
        }
      }

      .discount-badge {
        top: 4px;
        left: 4px;
        font-size: 10px;
        padding: 1px 4px;
      }
    }

    .ant-card-body {
      padding: 6px;
    }

    .product-name .info-icon {
      display: none; // 小屏幕隐藏信息图标
    }

    .product-rating {
      margin-bottom: 4px;
    }

    .product-details .details-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
    }
  }
} 