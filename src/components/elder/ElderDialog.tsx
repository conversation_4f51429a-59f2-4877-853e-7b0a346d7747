import React from 'react';
import { Modal, ConfigProvider } from 'antd';
import type { ModalProps } from 'antd';
import { elderTheme } from '../../styles/theme';
import { ElderButton } from './ElderButton';
import '../../styles/elder.less';

// 适老化对话框属性接口
export interface ElderDialogProps extends ModalProps {
  /** 对话框尺寸 - 适老化版本 */
  size?: 'default' | 'large' | 'extra-large';
  /** 是否启用高对比度模式 */
  highContrast?: boolean;
  /** 标题大小 */
  titleSize?: 'default' | 'large' | 'extra-large';
  /** 内容字体大小 */
  contentSize?: 'default' | 'large' | 'extra-large';
  /** 确认按钮文本 */
  confirmText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 确认按钮类型 */
  confirmType?: 'primary' | 'danger' | 'default';
  /** 是否显示确认按钮 */
  showConfirm?: boolean;
  /** 是否显示取消按钮 */
  showCancel?: boolean;
  /** 确认按钮回调 */
  onConfirm?: () => void;
  /** 取消按钮回调 */
  onCancel?: () => void;
}

// 对话框尺寸映射
const sizeMap = {
  default: {
    width: 520,
    padding: 32,
    borderRadius: 12,
  },
  large: {
    width: 680,
    padding: 40,
    borderRadius: 16,
  },
  'extra-large': {
    width: 800,
    padding: 48,
    borderRadius: 20,
  },
};

// 字体尺寸映射
const fontSizeMap = {
  default: 22,
  large: 24,
  'extra-large': 28,
};

// 标题字体尺寸映射
const titleFontSizeMap = {
  default: 28,
  large: 32,
  'extra-large': 36,
};

/**
 * 适老化对话框组件
 * 基于Ant Design Modal进行适老化增强
 * 
 * 特性：
 * - 大字体标题和内容
 * - 大尺寸按钮
 * - 高对比度颜色支持
 * - 键盘导航友好
 * - 无障碍访问优化
 * - 清晰的按钮布局
 */
export const ElderDialog: React.FC<ElderDialogProps> = ({
  children,
  size = 'default',
  highContrast = false,
  titleSize = 'default',
  contentSize = 'default',
  confirmText = '确定',
  cancelText = '取消',
  confirmType = 'primary',
  showConfirm = true,
  showCancel = true,
  onConfirm,
  onCancel,
  className = '',
  style = {},
  title,
  footer,
  ...restProps
}) => {
  // 获取尺寸配置
  const sizeConfig = sizeMap[size];
  const titleFontSize = titleFontSizeMap[titleSize];
  const contentFontSize = fontSizeMap[contentSize];
  
  // 组合className
  const combinedClassName = [
    'elder-modal',
    `elder-modal-${size}`,
    highContrast ? 'elder-high-contrast' : '',
    className,
  ].filter(Boolean).join(' ');
  
  // 组合样式
  const combinedStyle = {
    ...style,
  };
  
  // 高对比度主题
  const theme = highContrast 
    ? {
        ...elderTheme,
        token: {
          ...elderTheme.token,
          colorText: '#000000',
          colorTextHeading: '#000000',
          colorBgContainer: '#ffffff',
          colorBgMask: 'rgba(0, 0, 0, 0.8)',
          colorBorder: '#000000',
          colorBorderSecondary: '#434343',
        },
        components: {
          ...elderTheme.components,
          Modal: {
            ...elderTheme.components?.Modal,
            titleFontSize: titleFontSize,
            fontSize: contentFontSize,
            borderRadius: sizeConfig.borderRadius,
            paddingLG: sizeConfig.padding,
          },
        },
      }
    : {
        ...elderTheme,
        components: {
          ...elderTheme.components,
          Modal: {
            ...elderTheme.components?.Modal,
            titleFontSize: titleFontSize,
            fontSize: contentFontSize,
            borderRadius: sizeConfig.borderRadius,
            paddingLG: sizeConfig.padding,
          },
        },
      };

  // 渲染标题
  const renderTitle = () => {
    if (!title) return undefined;
    
    return (
      <div className="elder-modal-title">
        <span 
          className="elder-text elder-text-bold"
          style={{ fontSize: titleFontSize }}
        >
          {title}
        </span>
      </div>
    );
  };

  // 渲染底部按钮
  const renderFooter = () => {
    if (footer === null) return null;
    if (footer !== undefined) return footer;
    
    // 映射confirmType到ElderButton支持的类型
    const buttonType = confirmType === 'danger' ? 'primary' : confirmType;
    const isDanger = confirmType === 'danger';
    
    return (
      <div className="elder-modal-footer">
        <div className="elder-flex" style={{ gap: 16, justifyContent: 'flex-end' }}>
          {showCancel && (
            <ElderButton
              size={size === 'extra-large' ? 'extra-large' : 'large'}
              onClick={onCancel}
              highContrast={highContrast}
            >
              {cancelText}
            </ElderButton>
          )}
          {showConfirm && (
            <ElderButton
              type={buttonType}
              danger={isDanger}
              size={size === 'extra-large' ? 'extra-large' : 'large'}
              onClick={onConfirm}
              highContrast={highContrast}
            >
              {confirmText}
            </ElderButton>
          )}
        </div>
      </div>
    );
  };

  return (
    <ConfigProvider theme={theme}>
      <Modal
        className={combinedClassName}
        style={combinedStyle}
        width={sizeConfig.width}
        title={renderTitle()}
        footer={renderFooter()}
        // 无障碍访问属性
        centered
        maskClosable={false}
        keyboard
        focusTriggerAfterClose
        {...restProps}
      >
        <div 
          className="elder-modal-content"
          style={{ fontSize: contentFontSize, lineHeight: 1.6 }}
        >
          {children}
        </div>
      </Modal>
    </ConfigProvider>
  );
};

// 确认对话框
export const ElderConfirmDialog: React.FC<ElderDialogProps> = (props) => (
  <ElderDialog
    confirmType="primary"
    showCancel={true}
    showConfirm={true}
    {...props}
  />
);

// 警告对话框
export const ElderWarningDialog: React.FC<ElderDialogProps> = (props) => (
  <ElderDialog
    confirmType="danger"
    confirmText="确定"
    cancelText="取消"
    {...props}
  />
);

// 信息对话框
export const ElderInfoDialog: React.FC<ElderDialogProps> = (props) => (
  <ElderDialog
    showCancel={false}
    confirmText="知道了"
    {...props}
  />
);

// 错误对话框
export const ElderErrorDialog: React.FC<ElderDialogProps> = (props) => (
  <ElderDialog
    showCancel={false}
    confirmType="danger"
    confirmText="知道了"
    {...props}
  />
);

// 大尺寸对话框
export const ElderLargeDialog: React.FC<ElderDialogProps> = (props) => (
  <ElderDialog size="large" {...props} />
);

// 超大尺寸对话框
export const ElderExtraLargeDialog: React.FC<ElderDialogProps> = (props) => (
  <ElderDialog size="extra-large" {...props} />
);

// 高对比度对话框
export const ElderHighContrastDialog: React.FC<ElderDialogProps> = (props) => (
  <ElderDialog highContrast {...props} />
);

// 简单确认函数
export const elderConfirm = (config: {
  title?: string;
  content?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
  type?: 'info' | 'warning' | 'error' | 'confirm';
  size?: 'default' | 'large' | 'extra-large';
  highContrast?: boolean;
}) => {
  const {
    title = '确认',
    content,
    onConfirm,
    onCancel,
    confirmText = '确定',
    cancelText = '取消',
    size = 'default',
    highContrast = false,
  } = config;

  return Modal.confirm({
    title: (
      <span style={{ fontSize: titleFontSizeMap[size] }}>
        {title}
      </span>
    ),
    content: (
      <div style={{ fontSize: fontSizeMap[size], lineHeight: 1.6 }}>
        {content}
      </div>
    ),
    onOk: onConfirm,
    onCancel: onCancel,
    okText: confirmText,
    cancelText: cancelText,
    centered: true,
    maskClosable: false,
    className: `elder-modal elder-modal-${size} ${highContrast ? 'elder-high-contrast' : ''}`,
    width: sizeMap[size].width,
    okButtonProps: {
      size: 'large',
      style: { fontSize: fontSizeMap[size], height: 48, padding: '0 32px' },
    },
    cancelButtonProps: {
      size: 'large',
      style: { fontSize: fontSizeMap[size], height: 48, padding: '0 32px' },
    },
  });
};

export default ElderDialog; 