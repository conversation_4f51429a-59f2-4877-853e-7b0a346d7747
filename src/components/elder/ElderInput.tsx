import React, { forwardRef } from 'react';
import { Input, ConfigProvider } from 'antd';
import type { InputProps } from 'antd';
import { elderTheme } from '../../styles/theme';
import '../../styles/elder.less';

// 适老化输入框属性接口
export interface ElderInputProps extends Omit<InputProps, 'size'> {
  /** 输入框尺寸 - 适老化版本 */
  size?: 'default' | 'large' | 'extra-large';
  /** 是否启用高对比度模式 */
  highContrast?: boolean;
  /** 是否显示字符计数 */
  showCount?: boolean;
  /** 错误提示文本 */
  errorMessage?: string;
  /** 帮助文本 */
  helpText?: string;
  /** 是否启用清空按钮 */
  clearable?: boolean;
  /** 标签文本 */
  label?: string;
  /** 是否必填 */
  required?: boolean;
}

// 输入框尺寸映射
const sizeMap = {
  default: {
    height: 48,
    fontSize: 22,
    padding: '16px',
  },
  large: {
    height: 56,
    fontSize: 24,
    padding: '20px',
  },
  'extra-large': {
    height: 64,
    fontSize: 28,
    padding: '24px',
  },
};

/**
 * 适老化输入框组件
 * 基于Ant Design Input进行适老化增强
 * 
 * 特性：
 * - 大字体和大尺寸设计
 * - 明显的边框和焦点指示
 * - 高对比度颜色支持
 * - 清晰的错误状态显示
 * - 键盘导航友好
 * - 无障碍访问优化
 */
export const ElderInput = forwardRef<any, ElderInputProps>(
  (
    {
      size = 'default',
      highContrast = false,
      showCount = false,
      errorMessage,
      helpText,
      clearable = true,
      label,
      required = false,
      className = '',
      style = {},
      placeholder,
      ...restProps
    },
    ref
  ) => {
    // 获取尺寸配置
    const sizeConfig = sizeMap[size];
    
    // 组合className
    const combinedClassName = [
      'elder-input',
      `elder-input-${size}`,
      errorMessage ? 'elder-input-error' : '',
      highContrast ? 'elder-high-contrast' : '',
      className,
    ].filter(Boolean).join(' ');
    
    // 组合样式
    const combinedStyle = {
      height: sizeConfig.height,
      fontSize: sizeConfig.fontSize,
      padding: sizeConfig.padding,
      lineHeight: 1.5,
      ...style,
    };
    
    // 高对比度主题
    const theme = highContrast 
      ? {
          ...elderTheme,
          token: {
            ...elderTheme.token,
            colorText: '#000000',
            colorBgContainer: '#ffffff',
            colorBorder: '#000000',
            colorBorderSecondary: '#434343',
            colorPrimary: '#0050b3',
            colorError: '#a8071a',
          },
        }
      : elderTheme;

    // 处理占位符文本
    const enhancedPlaceholder = placeholder || (label ? `请输入${label}` : '');

    return (
      <div className="elder-input-wrapper">
        {/* 标签 */}
        {label && (
          <label className="elder-input-label">
            <span className="elder-text elder-text-lg elder-text-bold">
              {label}
              {required && <span className="elder-text-error"> *</span>}
            </span>
          </label>
        )}
        
        {/* 输入框 */}
        <ConfigProvider theme={theme}>
          <Input
            ref={ref}
            className={combinedClassName}
            style={combinedStyle}
            placeholder={enhancedPlaceholder}
            showCount={showCount}
            allowClear={clearable}
            // 无障碍访问属性
            aria-label={label || placeholder}
            aria-required={required}
            aria-invalid={!!errorMessage}
            aria-describedby={
              errorMessage || helpText 
                ? `${restProps.id || 'elder-input'}-description`
                : undefined
            }
            {...restProps}
          />
        </ConfigProvider>
        
        {/* 帮助文本 */}
        {helpText && !errorMessage && (
          <div 
            id={`${restProps.id || 'elder-input'}-description`}
            className="elder-input-help"
          >
            <span className="elder-text elder-text-sm elder-text-secondary">
              {helpText}
            </span>
          </div>
        )}
        
        {/* 错误信息 */}
        {errorMessage && (
          <div 
            id={`${restProps.id || 'elder-input'}-description`}
            className="elder-input-error-message"
            role="alert"
          >
            <span className="elder-text elder-text-sm elder-text-error">
              {errorMessage}
            </span>
          </div>
        )}
      </div>
    );
  }
);

ElderInput.displayName = 'ElderInput';

// 密码输入框
export const ElderPasswordInput = forwardRef<any, ElderInputProps>(
  (props, ref) => (
    <ElderInput ref={ref} type="password" {...props} />
  )
);

// 数字输入框
export const ElderNumberInput = forwardRef<any, ElderInputProps>(
  (props, ref) => (
    <ElderInput ref={ref} type="number" {...props} />
  )
);

// 搜索输入框
export const ElderSearchInput = forwardRef<any, ElderInputProps>(
  (props, ref) => (
    <ElderInput ref={ref} type="search" {...props} />
  )
);

// 大尺寸输入框
export const ElderLargeInput = forwardRef<any, ElderInputProps>(
  (props, ref) => (
    <ElderInput ref={ref} size="large" {...props} />
  )
);

// 超大尺寸输入框
export const ElderExtraLargeInput = forwardRef<any, ElderInputProps>(
  (props, ref) => (
    <ElderInput ref={ref} size="extra-large" {...props} />
  )
);

// 高对比度输入框
export const ElderHighContrastInput = forwardRef<any, ElderInputProps>(
  (props, ref) => (
    <ElderInput ref={ref} highContrast {...props} />
  )
);

// 必填输入框
export const ElderRequiredInput = forwardRef<any, ElderInputProps>(
  (props, ref) => (
    <ElderInput ref={ref} required {...props} />
  )
);

export default ElderInput; 