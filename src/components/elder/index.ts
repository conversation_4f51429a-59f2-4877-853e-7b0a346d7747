// 适老化UI组件库统一导出
export { 
  ElderButt<PERSON>,
  ElderPrimaryButton,
  ElderDangerButton,
  ElderGhostButton,
  ElderLargeButton,
  ElderExtraLargeButton,
  ElderHighContrastButton,
  ElderSoundButton,
  type ElderButtonProps 
} from './ElderButton';

export {
  ElderInput,
  ElderPasswordInput,
  ElderNumberInput,
  ElderSearchInput,
  ElderLargeInput,
  ElderExtraLargeInput,
  ElderHighContrastInput,
  ElderRequiredInput,
  type ElderInputProps
} from './ElderInput';

export {
  ElderCard,
  ElderClickableCard,
  ElderLargeCard,
  ElderExtraLargeCard,
  ElderHighContrastCard,
  ElderSuccessCard,
  ElderWarningCard,
  ElderErrorCard,
  ElderInfoCard,
  ElderSimpleCard,
  type ElderCardProps
} from './ElderCard';

export {
  ElderDialog,
  ElderConfirmDialog,
  ElderWarningDialog,
  ElderInfoDialog,
  ElderErrorDialog,
  ElderLargeDialog,
  ElderExtraLargeDialog,
  ElderHighContrastDialog,
  elderConfirm,
  type ElderDialogProps
} from './ElderDialog';

// 主题和样式导出
export { 
  elderTheme, 
  elderHighContrastTheme,
  elderColors,
  elderTypography,
  elderSpacing 
} from '../../styles/theme';

// 组件库版本信息
export const ELDER_UI_VERSION = '1.0.0';

// 适老化设计规范常量
export const ELDER_DESIGN_TOKENS = {
  // 最小字体大小
  MIN_FONT_SIZE: 18,
  
  // 最小触摸目标尺寸
  MIN_TOUCH_TARGET: 44,
  
  // 推荐的行高
  RECOMMENDED_LINE_HEIGHT: 1.5,
  
  // 对比度要求
  CONTRAST_RATIO: {
    AA: 4.5,    // WCAG AA级别
    AAA: 7.1,   // WCAG AAA级别
  },
  
  // 动画时长（适老化建议较长）
  ANIMATION_DURATION: {
    FAST: '0.2s',
    NORMAL: '0.3s',
    SLOW: '0.4s',
  },
  
  // 焦点指示器样式
  FOCUS_INDICATOR: {
    width: '3px',
    style: 'solid',
    color: '#1677ff',
    offset: '2px',
  },
};

// 适老化设计检查函数
export const elderDesignCheck = {
  // 检查字体大小是否符合适老化标准
  checkFontSize: (fontSize: number): boolean => {
    return fontSize >= ELDER_DESIGN_TOKENS.MIN_FONT_SIZE;
  },
  
  // 检查触摸目标大小是否符合标准
  checkTouchTarget: (width: number, height: number): boolean => {
    return width >= ELDER_DESIGN_TOKENS.MIN_TOUCH_TARGET && 
           height >= ELDER_DESIGN_TOKENS.MIN_TOUCH_TARGET;
  },
  
  // 检查对比度（简化版本，实际应用中需要更复杂的计算）
  checkContrast: (foreground: string, background: string): boolean => {
    // 这里应该实现真正的对比度计算
    // 简化版本，仅做示例
    return true;
  },
  
  // 检查行高是否合适
  checkLineHeight: (lineHeight: number): boolean => {
    return lineHeight >= ELDER_DESIGN_TOKENS.RECOMMENDED_LINE_HEIGHT;
  },
};

// 适老化工具函数
export const elderUtils = {
  // 根据屏幕尺寸调整字体大小
  getResponsiveFontSize: (baseSize: number, screenWidth: number): number => {
    if (screenWidth < 768) {
      return Math.max(baseSize + 2, ELDER_DESIGN_TOKENS.MIN_FONT_SIZE);
    }
    return Math.max(baseSize, ELDER_DESIGN_TOKENS.MIN_FONT_SIZE);
  },
  
  // 获取适老化颜色（高对比度版本）
  getElderColor: (color: string, highContrast: boolean = false): string => {
    if (!highContrast) return color;
    
    // 高对比度模式下的颜色映射
    const contrastMap: Record<string, string> = {
      '#1677ff': '#0050b3',
      '#52c41a': '#237804',
      '#fa8c16': '#ad4e00',
      '#f5222d': '#a8071a',
      '#595959': '#000000',
      '#8c8c8c': '#434343',
    };
    
    return contrastMap[color] || color;
  },
  
  // 生成适老化无障碍属性
  getA11yProps: (label?: string, required?: boolean, invalid?: boolean) => ({
    'aria-label': label,
    'aria-required': required,
    'aria-invalid': invalid,
    role: 'button',
    tabIndex: 0,
  }),
  
  // 播放触觉反馈（如果设备支持）
  playHapticFeedback: (type: 'light' | 'medium' | 'heavy' = 'medium') => {
    if ('vibrate' in navigator) {
      const patterns = {
        light: [50],
        medium: [100],
        heavy: [200],
      };
      navigator.vibrate(patterns[type]);
    }
  },
  
  // 播放音频反馈
  playAudioFeedback: (frequency: number = 800, duration: number = 100) => {
    if (typeof window !== 'undefined' && window.AudioContext) {
      try {
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration / 1000);
      } catch (error) {
        console.warn('Audio feedback not supported:', error);
      }
    }
  },
};

// 适老化CSS类名生成器
export const elderClassName = {
  // 生成响应式字体类名
  text: (size: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' = 'base') => 
    `elder-text elder-text-${size}`,
  
  // 生成间距类名
  margin: (size: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' = 'base', direction?: 't' | 'b' | 'l' | 'r') => 
    direction ? `elder-m${direction}-${size}` : `elder-m-${size}`,
  
  // 生成内边距类名
  padding: (size: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' = 'base', direction?: 't' | 'b' | 'l' | 'r') => 
    direction ? `elder-p${direction}-${size}` : `elder-p-${size}`,
  
  // 生成布局类名
  flex: (type: 'center' | 'between' | 'column' | 'wrap' = 'center') => 
    type === 'center' ? 'elder-flex elder-flex-center' : `elder-flex elder-flex-${type}`,
  
  // 生成高对比度类名
  highContrast: () => 'elder-high-contrast',
  
  // 生成触摸友好类名
  touchTarget: () => 'elder-touch-target',
  
  // 生成焦点样式类名
  focus: () => 'elder-focus',
};

// 导入组件用于默认导出
import { ElderButton } from './ElderButton';
import { ElderInput } from './ElderInput';
import { ElderCard } from './ElderCard';
import { ElderDialog } from './ElderDialog';
import { elderTheme } from '../../styles/theme';

// 默认导出组件集合
export default {
  ElderButton,
  ElderInput,
  ElderCard,
  ElderDialog,
  elderTheme,
  elderUtils,
  elderClassName,
  ELDER_DESIGN_TOKENS,
}; 