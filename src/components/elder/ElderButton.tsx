import React, { forwardRef } from 'react';
import { Button, ConfigProvider } from 'antd';
import type { ButtonProps } from 'antd';
import { elderTheme } from '../../styles/theme';
import '../../styles/elder.less';

// 适老化按钮属性接口
export interface ElderButtonProps extends Omit<ButtonProps, 'size'> {
  /** 按钮尺寸 - 适老化版本 */
  size?: 'default' | 'large' | 'extra-large';
  /** 是否启用高对比度模式 */
  highContrast?: boolean;
  /** 是否启用触摸友好模式 */
  touchFriendly?: boolean;
  /** 自定义图标大小 */
  iconSize?: number;
  /** 是否启用声音反馈 */
  soundFeedback?: boolean;
  /** 点击声音反馈回调 */
  onSoundFeedback?: () => void;
}

// 按钮尺寸映射
const sizeMap = {
  default: {
    height: 48,
    fontSize: 22,
    padding: '16px 32px',
  },
  large: {
    height: 56,
    fontSize: 24,
    padding: '24px 48px',
  },
  'extra-large': {
    height: 64,
    fontSize: 28,
    padding: '24px 56px',
  },
};

/**
 * 适老化按钮组件
 * 基于Ant Design Button进行适老化增强
 * 
 * 特性：
 * - 大字体和大尺寸设计
 * - 高对比度颜色支持
 * - 键盘导航友好
 * - 触摸友好的最小点击区域
 * - 声音反馈支持
 * - 无障碍访问优化
 */
export const ElderButton = forwardRef<HTMLButtonElement, ElderButtonProps>(
  (
    {
      children,
      size = 'default',
      highContrast = false,
      touchFriendly = true,
      iconSize,
      soundFeedback = false,
      onSoundFeedback,
      className = '',
      style = {},
      onClick,
      ...restProps
    },
    ref
  ) => {
    // 获取尺寸配置
    const sizeConfig = sizeMap[size];
    
    // 处理点击事件
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      // 触发声音反馈
      if (soundFeedback && onSoundFeedback) {
        onSoundFeedback();
      }
      
      // 触发原始点击事件
      if (onClick) {
        onClick(e);
      }
    };
    
    // 组合className
    const combinedClassName = [
      'elder-button',
      `elder-button-${size}`,
      touchFriendly ? 'elder-touch-target' : '',
      highContrast ? 'elder-high-contrast' : '',
      className,
    ].filter(Boolean).join(' ');
    
    // 组合样式
    const combinedStyle = {
      height: sizeConfig.height,
      fontSize: sizeConfig.fontSize,
      padding: sizeConfig.padding,
      minWidth: touchFriendly ? 44 : 'auto',
      minHeight: touchFriendly ? 44 : 'auto',
      ...style,
    };
    
    // 高对比度主题
    const theme = highContrast 
      ? {
          ...elderTheme,
          token: {
            ...elderTheme.token,
            colorText: '#000000',
            colorBgContainer: '#ffffff',
            colorBorder: '#000000',
            colorPrimary: '#0050b3',
          },
        }
      : elderTheme;

    return (
      <ConfigProvider theme={theme}>
        <Button
          ref={ref}
          className={combinedClassName}
          style={combinedStyle}
          onClick={handleClick}
          // 无障碍访问属性
          aria-label={typeof children === 'string' ? children : undefined}
          tabIndex={0}
          {...restProps}
        >
          {/* 如果有图标，调整图标大小 */}
          {React.Children.map(children, (child) => {
            if (React.isValidElement(child) && child.props.className?.includes('anticon')) {
              return React.cloneElement(child as React.ReactElement<any>, {
                style: {
                  fontSize: iconSize || sizeConfig.fontSize * 0.8,
                  ...child.props.style,
                },
              });
            }
            return child;
          })}
        </Button>
      </ConfigProvider>
    );
  }
);

ElderButton.displayName = 'ElderButton';

// 预设按钮变体
export const ElderPrimaryButton = forwardRef<HTMLButtonElement, ElderButtonProps>(
  (props, ref) => (
    <ElderButton ref={ref} type="primary" {...props} />
  )
);

export const ElderDangerButton = forwardRef<HTMLButtonElement, ElderButtonProps>(
  (props, ref) => (
    <ElderButton ref={ref} danger {...props} />
  )
);

export const ElderGhostButton = forwardRef<HTMLButtonElement, ElderButtonProps>(
  (props, ref) => (
    <ElderButton ref={ref} ghost {...props} />
  )
);

export const ElderLargeButton = forwardRef<HTMLButtonElement, ElderButtonProps>(
  (props, ref) => (
    <ElderButton ref={ref} size="large" {...props} />
  )
);

export const ElderExtraLargeButton = forwardRef<HTMLButtonElement, ElderButtonProps>(
  (props, ref) => (
    <ElderButton ref={ref} size="extra-large" {...props} />
  )
);

// 高对比度按钮变体
export const ElderHighContrastButton = forwardRef<HTMLButtonElement, ElderButtonProps>(
  (props, ref) => (
    <ElderButton ref={ref} highContrast {...props} />
  )
);

// 带声音反馈的按钮变体
export const ElderSoundButton = forwardRef<HTMLButtonElement, ElderButtonProps>(
  (props, ref) => (
    <ElderButton 
      ref={ref} 
      soundFeedback 
      onSoundFeedback={() => {
        // 简单的音频反馈实现
        if (typeof window !== 'undefined' && window.AudioContext) {
          const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();
          
          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);
          
          oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
          
          oscillator.start(audioContext.currentTime);
          oscillator.stop(audioContext.currentTime + 0.1);
        }
      }}
      {...props} 
    />
  )
);

export default ElderButton; 