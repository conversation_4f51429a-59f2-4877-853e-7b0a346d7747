import React from 'react';
import { Card, ConfigProvider } from 'antd';
import type { CardProps } from 'antd';
import { elderTheme } from '../../styles/theme';
import '../../styles/elder.less';

// 适老化卡片属性接口
export interface ElderCardProps extends Omit<CardProps, 'size'> {
  /** 卡片尺寸 - 适老化版本 */
  size?: 'default' | 'large' | 'extra-large';
  /** 是否启用高对比度模式 */
  highContrast?: boolean;
  /** 是否启用阴影效果 */
  shadow?: boolean;
  /** 是否可点击 */
  clickable?: boolean;
  /** 是否显示边框 */
  bordered?: boolean;
  /** 自定义标题大小 */
  titleSize?: 'default' | 'large' | 'extra-large';
  /** 卡片状态 */
  status?: 'default' | 'success' | 'warning' | 'error' | 'info';
}

// 卡片尺寸映射
const sizeMap = {
  default: {
    padding: 24,
    fontSize: 22,
    titleFontSize: 24,
    borderRadius: 12,
  },
  large: {
    padding: 32,
    fontSize: 24,
    titleFontSize: 28,
    borderRadius: 16,
  },
  'extra-large': {
    padding: 40,
    fontSize: 28,
    titleFontSize: 32,
    borderRadius: 20,
  },
};

// 标题尺寸映射
const titleSizeMap = {
  default: 24,
  large: 28,
  'extra-large': 32,
};

// 状态颜色映射
const statusColorMap = {
  default: {
    borderColor: '#d9d9d9',
    backgroundColor: '#ffffff',
  },
  success: {
    borderColor: '#52c41a',
    backgroundColor: '#f6ffed',
  },
  warning: {
    borderColor: '#fa8c16',
    backgroundColor: '#fff7e6',
  },
  error: {
    borderColor: '#f5222d',
    backgroundColor: '#fff2f0',
  },
  info: {
    borderColor: '#1677ff',
    backgroundColor: '#e6f4ff',
  },
};

/**
 * 适老化卡片组件
 * 基于Ant Design Card进行适老化增强
 * 
 * 特性：
 * - 大字体和大间距设计
 * - 高对比度颜色支持
 * - 增强的视觉层次
 * - 可点击状态反馈
 * - 状态指示颜色
 * - 无障碍访问优化
 */
export const ElderCard: React.FC<ElderCardProps> = ({
  children,
  size = 'default',
  highContrast = false,
  shadow = true,
  clickable = false,
  bordered = true,
  titleSize = 'default',
  status = 'default',
  className = '',
  style = {},
  title,
  ...restProps
}) => {
  // 获取尺寸配置
  const sizeConfig = sizeMap[size];
  const actualTitleSize = titleSizeMap[titleSize];
  const statusColors = statusColorMap[status];
  
  // 组合className
  const combinedClassName = [
    'elder-card',
    `elder-card-${size}`,
    `elder-card-${status}`,
    clickable ? 'elder-card-clickable' : '',
    shadow ? 'elder-card-shadow' : '',
    highContrast ? 'elder-high-contrast' : '',
    className,
  ].filter(Boolean).join(' ');
  
  // 组合样式
  const combinedStyle = {
    borderRadius: sizeConfig.borderRadius,
    border: bordered ? `2px solid ${statusColors.borderColor}` : 'none',
    backgroundColor: statusColors.backgroundColor,
    fontSize: sizeConfig.fontSize,
    cursor: clickable ? 'pointer' : 'default',
    transition: 'all 0.3s ease',
    ...style,
  };
  
  // 高对比度主题
  const theme = highContrast 
    ? {
        ...elderTheme,
        token: {
          ...elderTheme.token,
          colorText: '#000000',
          colorTextHeading: '#000000',
          colorBgContainer: '#ffffff',
          colorBorder: '#000000',
          colorBorderSecondary: '#434343',
        },
        components: {
          ...elderTheme.components,
          Card: {
            ...elderTheme.components?.Card,
            headerBg: '#ffffff',
            paddingLG: sizeConfig.padding,
            borderRadiusLG: sizeConfig.borderRadius,
            headerFontSize: actualTitleSize,
            fontSize: sizeConfig.fontSize,
          },
        },
      }
    : {
        ...elderTheme,
        components: {
          ...elderTheme.components,
          Card: {
            ...elderTheme.components?.Card,
            paddingLG: sizeConfig.padding,
            borderRadiusLG: sizeConfig.borderRadius,
            headerFontSize: actualTitleSize,
            fontSize: sizeConfig.fontSize,
          },
        },
      };

  // 渲染标题
  const renderTitle = () => {
    if (!title) return undefined;
    
    return (
      <div className="elder-card-title">
        <span 
          className="elder-text elder-text-bold"
          style={{ fontSize: actualTitleSize }}
        >
          {title}
        </span>
      </div>
    );
  };

  return (
    <ConfigProvider theme={theme}>
      <Card
        className={combinedClassName}
        style={combinedStyle}
        title={renderTitle()}
        bordered={false} // 我们自己处理边框
        // 无障碍访问属性
        role={clickable ? 'button' : 'article'}
        tabIndex={clickable ? 0 : undefined}
        aria-label={typeof title === 'string' ? title : undefined}
        {...restProps}
      >
        <div className="elder-card-content">
          {children}
        </div>
      </Card>
    </ConfigProvider>
  );
};

// 可点击卡片
export const ElderClickableCard: React.FC<ElderCardProps> = (props) => (
  <ElderCard clickable {...props} />
);

// 大尺寸卡片
export const ElderLargeCard: React.FC<ElderCardProps> = (props) => (
  <ElderCard size="large" {...props} />
);

// 超大尺寸卡片
export const ElderExtraLargeCard: React.FC<ElderCardProps> = (props) => (
  <ElderCard size="extra-large" {...props} />
);

// 高对比度卡片
export const ElderHighContrastCard: React.FC<ElderCardProps> = (props) => (
  <ElderCard highContrast {...props} />
);

// 成功状态卡片
export const ElderSuccessCard: React.FC<ElderCardProps> = (props) => (
  <ElderCard status="success" {...props} />
);

// 警告状态卡片
export const ElderWarningCard: React.FC<ElderCardProps> = (props) => (
  <ElderCard status="warning" {...props} />
);

// 错误状态卡片
export const ElderErrorCard: React.FC<ElderCardProps> = (props) => (
  <ElderCard status="error" {...props} />
);

// 信息状态卡片
export const ElderInfoCard: React.FC<ElderCardProps> = (props) => (
  <ElderCard status="info" {...props} />
);

// 简单卡片（无边框无阴影）
export const ElderSimpleCard: React.FC<ElderCardProps> = (props) => (
  <ElderCard bordered={false} shadow={false} {...props} />
);

export default ElderCard; 