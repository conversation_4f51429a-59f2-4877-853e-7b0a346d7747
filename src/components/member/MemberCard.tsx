import React from 'react';
import { Empty } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { ElderCard } from '../elder';
import './MemberCard.less';

export interface MemberCardProps {
  member: any | null;
  showActions?: boolean;
  onPointsRecharge?: () => void;
  onBalanceRecharge?: () => void;
  className?: string;
  size?: 'default' | 'small' | 'large';
}

/**
 * 会员卡片组件 - 简化版本
 */
const MemberCard: React.FC<MemberCardProps> = ({
  member,
  className = '',
}) => {
  // 如果没有会员信息
  if (!member) {
    return (
      <ElderCard className={`member-card no-member ${className}`}>
        <Empty 
          description="未选择会员"
          image={<UserOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />}
        />
      </ElderCard>
    );
  }

  return (
    <ElderCard className={`member-card ${className}`}>
      <div className="member-info">
        <UserOutlined style={{ fontSize: 24, marginRight: 8 }} />
        <div>
          <div className="elder-text elder-text-lg elder-text-bold">
            {member.name}
          </div>
          <div className="elder-text elder-text-sm elder-text-secondary">
            {member.phone}
          </div>
        </div>
      </div>
    </ElderCard>
  );
};

export default MemberCard; 