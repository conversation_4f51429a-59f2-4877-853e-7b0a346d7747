import React from 'react';
import { Empty } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { ElderCard } from '../elder';
import './MemberSearch.less';

export interface MemberSearchProps {
  onMemberFound?: (member: any) => void;
  onMemberClear?: () => void;
  className?: string;
  placeholder?: string;
  size?: 'large' | 'middle' | 'small';
}

/**
 * 会员搜索组件 - 简化版本
 */
const MemberSearch: React.FC<MemberSearchProps> = ({
  className = '',
}) => {
  return (
    <ElderCard 
      title="会员查询" 
      className={`member-search ${className}`}
      extra={<UserOutlined />}
    >
      <Empty 
        description="功能开发中"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    </ElderCard>
  );
};

export default MemberSearch; 