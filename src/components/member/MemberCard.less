// 会员信息卡片样式
.member-card {
  &.no-member {
    .no-member-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      text-align: center;
      
      .no-member-icon {
        font-size: 48px;
        color: #d9d9d9;
        margin-bottom: 16px;
      }
      
      .no-member-text {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }
  }
  
  .member-header {
    display: flex;
    gap: 16px;
    align-items: center;
    
    .member-avatar {
      flex-shrink: 0;
    }
    
    .member-basic {
      flex: 1;
      
      .member-name {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
        
        .level-tag {
          font-size: 12px;
          border: none;
        }
      }
      
      .member-contact {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
    }
  }
  
  .member-points {
    .points-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .points-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
      
      .discount-info {
        flex-shrink: 0;
      }
    }
    
    .level-progress {
      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }
      
      .elder-progress {
        height: 8px;
        
        .ant-progress-bg {
          border-radius: 4px;
        }
        
        .ant-progress-inner {
          border-radius: 4px;
          background-color: #f5f5f5;
        }
      }
    }
  }
  
  .member-balance {
    .balance-info {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .balance-icon {
        font-size: 24px;
        color: #52c41a;
      }
      
      .balance-details {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
    }
  }
  
  .member-actions {
    display: flex;
    gap: 12px;
    
    .action-btn {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 16px;
      background: #ffffff;
      border: 1px solid #d9d9d9;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #1677ff;
        background-color: #f0f6ff;
      }
      
      &.points-btn {
        &:hover {
          border-color: #faad14;
          background-color: #fffbe6;
          
          .anticon {
            color: #faad14;
          }
        }
      }
      
      &.balance-btn {
        &:hover {
          border-color: #52c41a;
          background-color: #f6ffed;
          
          .anticon {
            color: #52c41a;
          }
        }
      }
      
      .anticon {
        font-size: 20px;
        color: #8c8c8c;
        transition: color 0.3s ease;
      }
    }
  }
  
  .member-stats {
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    
    .stats-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 不同尺寸的样式
.member-card {
  &.small {
    .member-header {
      gap: 12px;
      
      .member-basic {
        .member-name {
          gap: 8px;
          margin-bottom: 6px;
        }
        
        .member-contact {
          gap: 2px;
        }
      }
    }
    
    .member-points {
      .points-header {
        margin-bottom: 12px;
      }
    }
    
    .member-actions {
      gap: 8px;
      
      .action-btn {
        padding: 12px;
        gap: 6px;
        
        .anticon {
          font-size: 16px;
        }
      }
    }
  }
  
  &.large {
    .member-header {
      gap: 20px;
      
      .member-basic {
        .member-name {
          gap: 16px;
          margin-bottom: 12px;
        }
        
        .member-contact {
          gap: 6px;
        }
      }
    }
    
    .member-points {
      .points-header {
        margin-bottom: 20px;
      }
    }
    
    .member-actions {
      gap: 16px;
      
      .action-btn {
        padding: 20px;
        gap: 12px;
        
        .anticon {
          font-size: 24px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .member-card {
    .member-header {
      flex-direction: column;
      text-align: center;
      gap: 12px;
      
      .member-basic {
        .member-name {
          justify-content: center;
        }
        
        .member-contact {
          align-items: center;
        }
      }
    }
    
    .member-points {
      .points-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
        
        .points-info {
          align-items: center;
        }
      }
    }
    
    .member-balance {
      .balance-info {
        justify-content: center;
        
        .balance-details {
          align-items: center;
        }
      }
    }
    
    .member-actions {
      flex-direction: column;
    }
    
    .member-stats {
      .stats-row {
        flex-direction: column;
        gap: 4px;
        text-align: center;
      }
    }
  }
}

@media (max-width: 480px) {
  .member-card {
    &.no-member {
      .no-member-content {
        padding: 30px 15px;
        
        .no-member-icon {
          font-size: 36px;
          margin-bottom: 12px;
        }
      }
    }
    
    .member-actions {
      .action-btn {
        padding: 12px;
        gap: 6px;
        
        .anticon {
          font-size: 16px;
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .member-card {
    border: 2px solid #000000;
    
    &.no-member {
      .no-member-content {
        .no-member-icon {
          color: #434343;
        }
      }
    }
    
    .member-header {
      .member-basic {
        .member-name {
          .level-tag {
            border: 1px solid #000000;
          }
        }
      }
    }
    
    .member-points {
      .level-progress {
        .elder-progress {
          .ant-progress-inner {
            background-color: #e6e6e6;
            border: 1px solid #000000;
          }
        }
      }
    }
    
    .member-balance {
      .balance-info {
        .balance-icon {
          color: #0b7f00;
        }
      }
    }
    
    .member-actions {
      .action-btn {
        border: 2px solid #000000;
        background: #ffffff;
        
        &:hover {
          background-color: #f5f5f5;
        }
        
        &.points-btn {
          &:hover {
            background-color: #fff7e6;
            border-color: #d48806;
          }
        }
        
        &.balance-btn {
          &:hover {
            background-color: #f0f9e8;
            border-color: #389e0d;
          }
        }
        
        .anticon {
          color: #434343;
        }
      }
    }
    
    .member-stats {
      border-top: 2px solid #000000;
    }
  }
} 