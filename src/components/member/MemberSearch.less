// 会员查询组件样式
.member-search {
  .search-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    
    .header-icon {
      font-size: 20px;
      color: #1677ff;
    }
  }
  
  .search-controls {
    display: flex;
    gap: 12px;
    align-items: flex-end;
    margin-bottom: 16px;
    
    .search-type-select {
      flex-shrink: 0;
    }
    
    .search-input {
      flex: 1;
      
      .clear-btn {
        color: #bfbfbf;
        
        &:hover {
          color: #8c8c8c;
        }
      }
    }
    
    .search-btn {
      flex-shrink: 0;
    }
  }
  
  .member-preview {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    
    .preview-header {
      margin-bottom: 12px;
    }
    
    .preview-content {
      .member-info {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        
        .member-name {
          color: #262626;
        }
        
        .member-level {
          background: #52c41a;
          color: #ffffff;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
        }
      }
      
      .member-details {
        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
  
  .search-tips {
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 12px;
    
    .tips-row {
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .member-search {
    .search-controls {
      flex-direction: column;
      gap: 12px;
      
      .search-type-select,
      .search-input,
      .search-btn {
        width: 100%;
      }
    }
    
    .member-preview {
      padding: 12px;
      
      .preview-content {
        .member-info {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
        
        .member-details {
          .detail-row {
            flex-direction: column;
            gap: 4px;
          }
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .member-search {
    .search-header {
      .header-icon {
        color: #0050b3;
      }
    }
    
    .member-preview {
      background: #ffffff;
      border: 2px solid #52c41a;
      
      .preview-content {
        .member-info {
          .member-level {
            background: #0b7f00;
            border: 1px solid #000000;
          }
        }
      }
    }
    
    .search-tips {
      background: #ffffff;
      border: 2px solid #000000;
    }
  }
} 