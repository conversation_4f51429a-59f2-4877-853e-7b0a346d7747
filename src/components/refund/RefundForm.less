.refund-form-card {
  margin: 16px;
  min-height: 600px;

  .ant-card-head {
    border-bottom: 2px solid #f0f0f0;
    
    .ant-card-head-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
    font-size: 16px;
  }
}

.refund-form-content {
  padding: 20px 0;

  .order-info-section {
    margin-bottom: 24px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      border-left: 4px solid #1890ff;
      padding-left: 12px;
    }

    .order-info {
      background: #fafafa;
      border-radius: 8px;
      padding: 16px;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          color: #666;
          margin-right: 8px;
          min-width: 80px;
        }

        .value {
          color: #333;
          font-weight: 500;
        }
      }
    }
  }

  .refund-form {
    .ant-form-item-label > label {
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
  }

  .refund-items-section {
    margin: 24px 0;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      border-left: 4px solid #52c41a;
      padding-left: 12px;
    }

    .refund-items-table {
      .ant-table-thead > tr > th {
        background: #f8f9fa;
        font-weight: 600;
        color: #333;
        border-bottom: 2px solid #e9ecef;
      }

      .order-item-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .item-image {
          width: 40px;
          height: 40px;
          object-fit: cover;
          border-radius: 4px;
          border: 1px solid #e9ecef;
        }

        .item-name {
          font-weight: 500;
          color: #333;
        }
      }

      .price {
        color: #ff4d4f;
        font-weight: 600;
      }

      .refund-quantity-input {
        width: 80px;
        
        .ant-input-number-input {
          text-align: center;
          font-weight: 500;
        }
      }

      .reason-select {
        width: 120px;
        
        .ant-select-selector {
          border-radius: 4px;
        }
      }

      .refund-amount {
        color: #52c41a;
        font-weight: 600;
        font-size: 14px;
      }

      // 表格行悬停效果
      .ant-table-tbody > tr:hover > td {
        background: #f8f9fa;
      }

      // 移动端适配
      @media (max-width: 768px) {
        .ant-table-content {
          overflow-x: auto;
        }
      }
    }
  }

  .refund-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    margin: 24px 0;
    color: white;

    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
        padding-top: 12px;
        border-top: 1px solid rgba(255, 255, 255, 0.3);
      }

      .label {
        font-size: 14px;
        opacity: 0.9;
      }

      .value {
        font-weight: 600;
        font-size: 16px;

        &.total-amount {
          font-size: 20px;
          color: #ffd700;
        }
      }
    }
  }
}

.refund-confirm-content {
  padding: 12px 0;

  p {
    margin: 8px 0;
    font-size: 14px;
    color: #333;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 适老化设计
.elderly-mode {
  .refund-form-card {
    .ant-card-head-title {
      font-size: 20px;
    }

    .refund-form-content {
      .order-info-section {
        .order-info {
          .info-item {
            .label {
              font-size: 16px;
              min-width: 100px;
            }

            .value {
              font-size: 16px;
            }
          }
        }
      }

      .refund-items-table {
        .ant-table-thead > tr > th {
          font-size: 16px;
          height: 54px;
        }

        .ant-table-tbody > tr > td {
          font-size: 16px;
          height: 60px;
        }

        .order-item-info {
          .item-image {
            width: 48px;
            height: 48px;
          }

          .item-name {
            font-size: 16px;
          }
        }

        .price, .refund-amount {
          font-size: 16px;
        }

        .refund-quantity-input {
          width: 90px;
          height: 40px;

          .ant-input-number-input {
            font-size: 16px;
            height: 38px;
          }
        }

        .reason-select {
          width: 140px;
          
          .ant-select-selector {
            height: 40px;
            
            .ant-select-selection-item {
              font-size: 14px;
              line-height: 38px;
            }
          }
        }
      }

      .refund-summary {
        padding: 24px;

        .summary-item {
          margin-bottom: 16px;

          .label {
            font-size: 16px;
          }

          .value {
            font-size: 18px;

            &.total-amount {
              font-size: 24px;
            }
          }
        }
      }
    }

    .ant-card-extra {
      .ant-btn {
        height: 44px;
        font-size: 16px;
        border-radius: 8px;
        min-width: 80px;
      }
    }
  }

  .refund-confirm-content {
    p {
      font-size: 16px;
      margin: 12px 0;
    }
  }
}

// 高对比度模式
.high-contrast {
  .refund-form-card {
    border: 2px solid #000;

    .refund-form-content {
      .order-info-section {
        .order-info {
          background: #000;
          border: 2px solid #fff;

          .info-item {
            .label {
              color: #fff;
            }

            .value {
              color: #ff0;
            }
          }
        }
      }

      .refund-items-table {
        .ant-table-thead > tr > th {
          background: #000;
          color: #fff;
          border: 1px solid #fff;
        }

        .ant-table-tbody > tr > td {
          background: #fff;
          color: #000;
          border: 1px solid #000;
        }

        .order-item-info {
          .item-image {
            border: 2px solid #000;
          }
        }

        .price {
          color: #f00;
        }

        .refund-amount {
          color: #008000;
        }
      }

      .refund-summary {
        background: #000;
        border: 2px solid #fff;
        color: #fff;

        .summary-item {
          .value.total-amount {
            color: #ff0;
          }
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .refund-form-card {
    margin: 8px;

    .refund-form-content {
      .order-info-section {
        .order-info {
          grid-template-columns: 1fr;
          gap: 8px;
        }
      }

      .refund-items-section {
        .refund-items-table {
          .ant-table-content {
            overflow-x: auto;
          }

          .refund-quantity-input {
            width: 60px;
          }

          .reason-select {
            width: 100px;
          }
        }
      }

      .refund-summary {
        padding: 16px;

        .summary-item {
          .value.total-amount {
            font-size: 18px;
          }
        }
      }
    }

    .ant-card-extra {
      .ant-space {
        flex-direction: column;
        width: 100%;

        .ant-btn {
          width: 100%;
        }
      }
    }
  }
} 