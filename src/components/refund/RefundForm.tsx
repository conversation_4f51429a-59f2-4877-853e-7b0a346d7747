import React, { useState, useEffect } from 'react';
import { Button, Card, Form, Input, Select, InputNumber, Table, message, Space, Modal } from 'antd';
import { MinusCircleOutlined, PlusOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { refundService, type RefundRequestInput } from '../../services/refundService';
import type { Order, OrderItem } from '../../store/slices/orderSlice';
import './RefundForm.less';

const { TextArea } = Input;
const { Option } = Select;
const { confirm } = Modal;

// 退款申请表单接口
interface RefundFormData {
  orderId: string;
  refundReason: string;
  refundItems: RefundRequestInput[];
  note?: string;
}

// 组件属性接口
interface RefundFormProps {
  order?: Order;
  onSubmit?: (refundData: any) => void;
  onCancel?: () => void;
  visible?: boolean;
}

const RefundForm: React.FC<RefundFormProps> = ({
  order,
  onSubmit,
  onCancel,
  visible = true,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectedItems, setSelectedItems] = useState<RefundRequestInput[]>([]);
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);

  // 退款原因选项
  const refundReasons = [
    { value: 'quality_issue', label: '菜品质量问题' },
    { value: 'wrong_order', label: '上错菜品' },
    { value: 'customer_cancel', label: '客户要求取消' },
    { value: 'service_issue', label: '服务问题' },
    { value: 'other', label: '其他原因' },
  ];

  // 退款商品原因选项
  const itemRefundReasons = [
    { value: 'quality_poor', label: '菜品质量不佳' },
    { value: 'taste_bad', label: '口味不符' },
    { value: 'wrong_dish', label: '上错菜' },
    { value: 'cold_food', label: '菜品已凉' },
    { value: 'customer_dislike', label: '客户不喜欢' },
    { value: 'other', label: '其他' },
  ];

  // 订单商品表格列定义
  const orderColumns: ColumnsType<OrderItem> = [
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      render: (text: string, record: OrderItem) => (
        <div className="order-item-info">
          {record.image && (
            <img src={record.image} alt={text} className="item-image" />
          )}
          <span className="item-name">{text}</span>
        </div>
      ),
    },
    {
      title: '单价',
      dataIndex: 'price',
      key: 'price',
      width: 80,
      render: (price: number) => <span className="price">¥{price.toFixed(2)}</span>,
    },
    {
      title: '已购数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      align: 'center',
    },
    {
      title: '退款数量',
      key: 'refundQuantity',
      width: 120,
      render: (_: any, record: OrderItem) => {
        const selectedItem = selectedItems.find(item => item.productId === record.productId);
        return (
          <InputNumber
            min={0}
            max={record.quantity}
            value={selectedItem?.refundQuantity || 0}
            onChange={(value) => handleRefundQuantityChange(record.productId, value || 0)}
            className="refund-quantity-input"
          />
        );
      },
    },
    {
      title: '退款原因',
      key: 'refundReason',
      width: 140,
      render: (_: any, record: OrderItem) => {
        const selectedItem = selectedItems.find(item => item.productId === record.productId);
        return (
          <Select
            placeholder="选择退款原因"
            value={selectedItem?.reason}
            onChange={(value) => handleRefundReasonChange(record.productId, value)}
            className="reason-select"
            size="small"
            disabled={!selectedItem || selectedItem.refundQuantity === 0}
          >
            {itemRefundReasons.map(reason => (
              <Option key={reason.value} value={reason.value}>
                {reason.label}
              </Option>
            ))}
          </Select>
        );
      },
    },
    {
      title: '退款金额',
      key: 'refundAmount',
      width: 100,
      align: 'right',
      render: (_: any, record: OrderItem) => {
        const selectedItem = selectedItems.find(item => item.productId === record.productId);
        const refundAmount = selectedItem ? record.price * selectedItem.refundQuantity : 0;
        return <span className="refund-amount">¥{refundAmount.toFixed(2)}</span>;
      },
    },
  ];

  // 初始化订单商品
  useEffect(() => {
    if (order?.items) {
      setOrderItems(order.items);
      // 初始化选中项目
      const initialItems: RefundRequestInput[] = order.items.map(item => ({
        productId: item.productId,
        refundQuantity: 0,
        reason: '',
      }));
      setSelectedItems(initialItems);
    }
  }, [order]);

  // 处理退款数量变化
  const handleRefundQuantityChange = (productId: string, quantity: number) => {
    setSelectedItems(prev => 
      prev.map(item => 
        item.productId === productId 
          ? { ...item, refundQuantity: quantity }
          : item
      )
    );
  };

  // 处理退款原因变化
  const handleRefundReasonChange = (productId: string, reason: string) => {
    setSelectedItems(prev => 
      prev.map(item => 
        item.productId === productId 
          ? { ...item, reason }
          : item
      )
    );
  };

  // 计算总退款金额
  const calculateTotalRefund = () => {
    return selectedItems.reduce((total, item) => {
      const orderItem = orderItems.find(oi => oi.productId === item.productId);
      if (orderItem && item.refundQuantity > 0) {
        return total + (orderItem.price * item.refundQuantity);
      }
      return total;
    }, 0);
  };

  // 获取有效的退款项目
  const getValidRefundItems = () => {
    return selectedItems.filter(item => item.refundQuantity > 0 && item.reason);
  };

  // 表单提交处理
  const handleSubmit = async (values: RefundFormData) => {
    const validRefundItems = getValidRefundItems();
    
    if (validRefundItems.length === 0) {
      message.warning('请至少选择一个商品进行退款');
      return;
    }

    const totalRefund = calculateTotalRefund();
    
    confirm({
      title: '确认提交退款申请',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div className="refund-confirm-content">
          <p>退款商品数量：{validRefundItems.length} 项</p>
          <p>退款总金额：¥{totalRefund.toFixed(2)}</p>
          <p>退款原因：{refundReasons.find(r => r.value === values.refundReason)?.label}</p>
        </div>
      ),
      onOk: async () => {
        try {
          setLoading(true);
          
          const refundData = {
            orderId: order!.id,
            refundItems: validRefundItems,
            refundReason: values.refundReason,
            note: values.note,
          };
          
          const result = await refundService.createRefundRequest(refundData);
          
          message.success('退款申请提交成功');
          onSubmit?.(result);
          form.resetFields();
          setSelectedItems([]);
        } catch (error) {
          console.error('提交退款申请失败:', error);
          message.error(error instanceof Error ? error.message : '提交退款申请失败');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    setSelectedItems([]);
    onCancel?.();
  };

  if (!order) {
    return (
      <Card title="退款申请" className="refund-form-card">
        <div className="empty-state">
          <p>请先选择要退款的订单</p>
        </div>
      </Card>
    );
  }

  const totalRefund = calculateTotalRefund();
  const validRefundItems = getValidRefundItems();

  return (
    <Card 
      title="退款申请" 
      className="refund-form-card"
      extra={
        <Space>
          <Button onClick={handleCancel}>取消</Button>
          <Button 
            type="primary" 
            onClick={() => form.submit()}
            loading={loading}
            disabled={validRefundItems.length === 0}
          >
            提交申请
          </Button>
        </Space>
      }
    >
      <div className="refund-form-content">
        {/* 订单信息 */}
        <div className="order-info-section">
          <h3>订单信息</h3>
          <div className="order-info">
            <div className="info-item">
              <span className="label">订单号：</span>
              <span className="value">{order.orderNo}</span>
            </div>
            <div className="info-item">
              <span className="label">订单金额：</span>
              <span className="value">¥{order.totalAmount.toFixed(2)}</span>
            </div>
            <div className="info-item">
              <span className="label">支付方式：</span>
              <span className="value">{order.paymentMethod}</span>
            </div>
            <div className="info-item">
              <span className="label">下单时间：</span>
              <span className="value">{new Date(order.createdAt).toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* 退款表单 */}
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="refund-form"
        >
          {/* 退款原因 */}
          <Form.Item
            name="refundReason"
            label="退款原因"
            rules={[{ required: true, message: '请选择退款原因' }]}
          >
            <Select placeholder="请选择退款原因">
              {refundReasons.map(reason => (
                <Option key={reason.value} value={reason.value}>
                  {reason.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* 商品退款明细 */}
          <div className="refund-items-section">
            <h3>退款商品明细</h3>
            <Table
              columns={orderColumns}
              dataSource={orderItems}
              rowKey="productId"
              pagination={false}
              size="small"
              className="refund-items-table"
              scroll={{ x: 700 }}
            />
          </div>

          {/* 退款汇总 */}
          <div className="refund-summary">
            <div className="summary-item">
              <span className="label">退款商品数量：</span>
              <span className="value">{validRefundItems.length} 项</span>
            </div>
            <div className="summary-item">
              <span className="label">退款总金额：</span>
              <span className="value total-amount">¥{totalRefund.toFixed(2)}</span>
            </div>
          </div>

          {/* 备注 */}
          <Form.Item
            name="note"
            label="备注说明"
          >
            <TextArea
              rows={3}
              placeholder="请输入退款相关的备注说明（选填）"
              maxLength={200}
              showCount
            />
          </Form.Item>
        </Form>
      </div>
    </Card>
  );
};

export default RefundForm; 