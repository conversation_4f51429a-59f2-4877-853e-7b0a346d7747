import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import 'dayjs/locale/zh-cn';

import App from './App';
import { store } from './store';
import './index.css';

// 设置 dayjs 为中文
import dayjs from 'dayjs';
dayjs.locale('zh-cn');

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <ConfigProvider 
        locale={zhCN}
        theme={{
          token: {
            // 适老化主题配置
            fontSize: 18,
            colorPrimary: '#1976D2',
            colorSuccess: '#4CAF50',
            colorWarning: '#FFC107',
            colorError: '#F44336',
            borderRadius: 8,
            wireframe: false,
          },
          components: {
            Button: {
              defaultBorderColor: '#1976D2',
              defaultColor: '#1976D2',
              defaultGhostBorderColor: '#1976D2',
              defaultGhostColor: '#1976D2',
              primaryShadow: '0 2px 8px rgba(25, 118, 210, 0.2)',
              defaultShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            },
            Table: {
              fontSize: 16,
              cellPaddingBlock: 12,
              cellPaddingInline: 16,
            },
            Input: {
              fontSize: 16,
              paddingBlock: 12,
              paddingInline: 16,
            },
            Select: {
              fontSize: 16,
            },
            Card: {
              paddingLG: 24,
            },
          },
        }}
      >
        <App />
      </ConfigProvider>
    </Provider>
  </React.StrictMode>
);

// 在开发环境下启用性能测量
if (process.env.NODE_ENV === 'development') {
  // 性能监控
  import('./utils/reportWebVitals').then(({ reportWebVitals }) => {
    reportWebVitals(console.log);
  });
} 