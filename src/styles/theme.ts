import type { ThemeConfig } from 'antd';

// 适老化颜色系统
export const elderColors = {
  // 主色系 - 高对比度蓝色
  primary: {
    1: '#e6f4ff',
    2: '#bae0ff',
    3: '#91caff',
    4: '#69b1ff',
    5: '#4096ff',
    6: '#1677ff', // 主色
    7: '#0958d9',
    8: '#003eb3',
    9: '#002c8c',
    10: '#001d66',
  },
  
  // 成功色系 - 高对比度绿色
  success: {
    1: '#f6ffed',
    2: '#d9f7be',
    3: '#b7eb8f',
    4: '#95de64',
    5: '#73d13d',
    6: '#52c41a', // 成功色
    7: '#389e0d',
    8: '#237804',
    9: '#135200',
    10: '#092b00',
  },
  
  // 警告色系 - 高对比度橙色
  warning: {
    1: '#fff7e6',
    2: '#ffe7ba',
    3: '#ffd591',
    4: '#ffc069',
    5: '#ffa940',
    6: '#fa8c16', // 警告色
    7: '#d46b08',
    8: '#ad4e00',
    9: '#873800',
    10: '#612500',
  },
  
  // 错误色系 - 高对比度红色
  error: {
    1: '#fff2f0',
    2: '#ffccc7',
    3: '#ffa39e',
    4: '#ff7875',
    5: '#ff4d4f',
    6: '#f5222d', // 错误色
    7: '#cf1322',
    8: '#a8071a',
    9: '#820014',
    10: '#5c0011',
  },
  
  // 中性色系 - 高对比度灰色
  neutral: {
    1: '#ffffff',
    2: '#fafafa',
    3: '#f5f5f5',
    4: '#f0f0f0',
    5: '#d9d9d9',
    6: '#bfbfbf',
    7: '#8c8c8c',
    8: '#595959',
    9: '#434343',
    10: '#262626',
    11: '#1f1f1f',
    12: '#141414',
    13: '#000000',
  },
};

// 适老化字体系统
export const elderTypography = {
  // 字体大小 - 最小18px
  fontSize: {
    xs: 18,    // 最小字体
    sm: 20,    // 小字体
    base: 22,  // 基础字体
    lg: 24,    // 大字体
    xl: 28,    // 超大字体
    '2xl': 32, // 标题字体
    '3xl': 36, // 大标题
    '4xl': 42, // 超大标题
  },
  
  // 行高 - 增强可读性
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
    loose: 2,
  },
  
  // 字重
  fontWeight: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
};

// 适老化间距系统
export const elderSpacing = {
  xs: 4,
  sm: 8,
  base: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 80,
};

// Ant Design适老化主题配置
export const elderTheme: ThemeConfig = {
  token: {
    // 颜色配置
    colorPrimary: elderColors.primary[6],
    colorSuccess: elderColors.success[6],
    colorWarning: elderColors.warning[6],
    colorError: elderColors.error[6],
    colorInfo: elderColors.primary[6],
    
    // 文本颜色 - 高对比度
    colorText: elderColors.neutral[13],
    colorTextSecondary: elderColors.neutral[8],
    colorTextTertiary: elderColors.neutral[7],
    colorTextQuaternary: elderColors.neutral[6],
    
    // 背景颜色
    colorBgBase: elderColors.neutral[1],
    colorBgContainer: elderColors.neutral[1],
    colorBgElevated: elderColors.neutral[1],
    colorBgLayout: elderColors.neutral[2],
    
    // 边框颜色 - 增强可见性
    colorBorder: elderColors.neutral[5],
    colorBorderSecondary: elderColors.neutral[4],
    
    // 字体配置
    fontSize: elderTypography.fontSize.base,
    fontSizeHeading1: elderTypography.fontSize['4xl'],
    fontSizeHeading2: elderTypography.fontSize['3xl'],
    fontSizeHeading3: elderTypography.fontSize['2xl'],
    fontSizeHeading4: elderTypography.fontSize.xl,
    fontSizeHeading5: elderTypography.fontSize.lg,
    fontSizeLG: elderTypography.fontSize.lg,
    fontSizeSM: elderTypography.fontSize.sm,
    fontSizeXL: elderTypography.fontSize.xl,
    
    // 行高配置
    lineHeight: elderTypography.lineHeight.normal,
    lineHeightHeading1: elderTypography.lineHeight.tight,
    lineHeightHeading2: elderTypography.lineHeight.tight,
    lineHeightHeading3: elderTypography.lineHeight.tight,
    lineHeightHeading4: elderTypography.lineHeight.normal,
    lineHeightHeading5: elderTypography.lineHeight.normal,
    lineHeightLG: elderTypography.lineHeight.relaxed,
    lineHeightSM: elderTypography.lineHeight.normal,
    
    // 间距配置
    margin: elderSpacing.base,
    marginLG: elderSpacing.lg,
    marginSM: elderSpacing.sm,
    marginXL: elderSpacing.xl,
    marginXS: elderSpacing.xs,
    marginXXL: elderSpacing['2xl'],
    marginXXS: elderSpacing.xs / 2,
    
    padding: elderSpacing.base,
    paddingLG: elderSpacing.lg,
    paddingSM: elderSpacing.sm,
    paddingXL: elderSpacing.xl,
    paddingXS: elderSpacing.xs,
    paddingXXS: elderSpacing.xs / 2,
    
    // 圆角配置 - 适度圆角
    borderRadius: 8,
    borderRadiusLG: 12,
    borderRadiusSM: 6,
    borderRadiusXS: 4,
    
    // 尺寸配置 - 增大控件尺寸
    controlHeight: 48,
    controlHeightLG: 56,
    controlHeightSM: 40,
    controlHeightXS: 32,
    
    // 阴影配置 - 增强层次感
    boxShadow: '0 4px 12px 0 rgba(0, 0, 0, 0.15)',
    boxShadowSecondary: '0 2px 8px 0 rgba(0, 0, 0, 0.10)',
    boxShadowTertiary: '0 1px 4px 0 rgba(0, 0, 0, 0.08)',
    
    // 动画配置 - 减缓动画
    motionDurationFast: '0.2s',
    motionDurationMid: '0.3s',
    motionDurationSlow: '0.4s',
  },
  
  components: {
    // Button组件适老化配置
    Button: {
      fontSizeLG: elderTypography.fontSize.lg,
      paddingInlineLG: elderSpacing.xl,
      borderRadiusLG: 8,
      fontWeight: elderTypography.fontWeight.medium,
    },
    
    // Input组件适老化配置
    Input: {
      fontSize: elderTypography.fontSize.base,
      paddingBlock: elderSpacing.base,
      paddingInline: elderSpacing.base,
      borderRadius: 8,
      lineHeight: elderTypography.lineHeight.normal,
    },
    
    // Card组件适老化配置
    Card: {
      paddingLG: elderSpacing.lg,
      fontSizeLG: elderTypography.fontSize.base,
      borderRadiusLG: 12,
      headerHeight: 64,
      headerFontSize: elderTypography.fontSize.lg,
      headerFontSizeSM: elderTypography.fontSize.base,
    },
    
    // Modal组件适老化配置
    Modal: {
      titleFontSize: elderTypography.fontSize.xl,
      fontSize: elderTypography.fontSize.base,
      borderRadius: 12,
      paddingLG: elderSpacing.xl,
    },
    
    // Table组件适老化配置
    Table: {
      fontSize: elderTypography.fontSize.base,
      cellPaddingBlock: elderSpacing.base,
      cellPaddingInline: elderSpacing.base,
      headerBg: elderColors.neutral[2],
      headerSortHoverBg: elderColors.neutral[3],
    },
    
    // Form组件适老化配置
    Form: {
      labelFontSize: elderTypography.fontSize.base,
      itemMarginBottom: elderSpacing.lg,
      verticalLabelPadding: `0 0 ${elderSpacing.sm}px`,
    },
    
    // Select组件适老化配置
    Select: {
      fontSize: elderTypography.fontSize.base,
      optionPadding: `${elderSpacing.base}px ${elderSpacing.base}px`,
      optionLineHeight: elderTypography.lineHeight.normal,
    },
    
    // Message组件适老化配置
    Message: {
      fontSize: elderTypography.fontSize.base,
      borderRadius: 8,
    },
    
    // Notification组件适老化配置
    Notification: {
      fontSize: elderTypography.fontSize.base,
      borderRadius: 12,
    },
  },
};

// 高对比度主题（可选）
export const elderHighContrastTheme: ThemeConfig = {
  ...elderTheme,
  token: {
    ...elderTheme.token,
    colorText: '#000000',
    colorTextSecondary: '#262626',
    colorBgBase: '#ffffff',
    colorBgContainer: '#ffffff',
    colorBorder: '#000000',
    colorBorderSecondary: '#434343',
    colorPrimary: '#0050b3',
    colorSuccess: '#237804',
    colorWarning: '#ad4e00',
    colorError: '#a8071a',
  },
};

export default elderTheme; 