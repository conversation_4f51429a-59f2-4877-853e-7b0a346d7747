// 登录凭据接口
export interface LoginCredentials {
  username: string;
  password: string;
  remember?: boolean;
}

// 用户角色类型
export type UserRole = 'admin' | 'cashier' | 'manager';

// 用户信息接口
export interface User {
  id: string;
  username: string;
  nickname: string;
  role: UserRole;
  permissions: string[];
  avatar?: string;
  email?: string;
  phone?: string;
  createdAt: string;
  lastLoginAt?: string;
}

// 登录响应接口
export interface LoginResponse {
  token: string;
  user: User;
  expiresIn: number;
}

// 权限定义
export interface Permission {
  id: string;
  name: string;
  code: string;
  module: string;
  description?: string;
}

// 角色权限映射
export interface RolePermission {
  role: UserRole;
  permissions: string[];
}

// 登录历史记录
export interface LoginHistory {
  id: string;
  userId: string;
  username: string;
  loginTime: string;
  ip?: string;
  userAgent?: string;
  success: boolean;
  reason?: string;
} 