// Store状态类型定义
import { RootState } from '../store';

// 重新导出RootState类型
export type { RootState };

// 状态选择器类型
export type StateSelector<T> = (state: RootState) => T;

// Action类型
export interface BaseAction {
  type: string;
  payload?: any;
}

// 异步Action状态
export interface AsyncActionState {
  loading: boolean;
  error: string | null;
  success: boolean;
}

// 列表状态类型
export interface ListState<T> {
  items: T[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 实体状态类型
export interface EntityState<T> {
  byId: Record<string, T>;
  allIds: string[];
  loading: boolean;
  error: string | null;
}

// 表单状态类型
export interface FormState<T> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  submitting: boolean;
  submitError: string | null;
  isDirty: boolean;
  isValid: boolean;
}

// UI状态类型
export interface UIState {
  sidebarCollapsed: boolean;
  mobileMenuOpen: boolean;
  modalStack: string[];
  activeTab: string | null;
  scrollPosition: number;
}

// 应用主题类型
export interface ThemeConfig {
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  fontSize: number;
  borderRadius: number;
}

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

// 网络状态类型
export interface NetworkState {
  isOnline: boolean;
  connectionType?: 'wifi' | 'cellular' | 'ethernet' | 'other';
  effectiveType?: '2g' | '3g' | '4g' | '5g';
  downlink?: number;
  rtt?: number;
}

// 本地化类型
export interface LocaleState {
  language: string;
  region: string;
  currency: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
}

// 权限类型
export type Permission = 
  | 'order:create'
  | 'order:update'
  | 'order:delete'
  | 'order:view'
  | 'member:create'
  | 'member:update'
  | 'member:delete'
  | 'member:view'
  | 'product:create'
  | 'product:update'
  | 'product:delete'
  | 'product:view'
  | 'report:view'
  | 'system:config'
  | 'user:manage';

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  stack?: string;
}

// 缓存状态类型
export interface CacheState<T> {
  data: T | null;
  lastFetch: number | null;
  expireTime: number | null;
  isStale: boolean;
}

// WebSocket状态类型
export interface WebSocketState {
  connected: boolean;
  reconnecting: boolean;
  lastHeartbeat: number | null;
  messageQueue: any[];
}

// 同步状态类型
export interface SyncState {
  lastSync: number | null;
  syncInProgress: boolean;
  pendingChanges: number;
  conflicts: any[];
}

// 设置状态类型
export interface SettingsState {
  appearance: {
    theme: 'light' | 'dark' | 'auto';
    fontSize: 'small' | 'medium' | 'large';
    colorScheme: 'default' | 'high-contrast';
  };
  behavior: {
    autoSave: boolean;
    confirmActions: boolean;
    soundEnabled: boolean;
    notificationsEnabled: boolean;
  };
  accessibility: {
    screenReader: boolean;
    highContrast: boolean;
    largeText: boolean;
    reducedMotion: boolean;
  };
}

// 性能监控状态类型
export interface PerformanceState {
  loadTime: number | null;
  renderTime: number | null;
  apiLatency: Record<string, number>;
  memoryUsage: number | null;
  errorCount: number;
}

// 用户偏好类型
export interface UserPreferences {
  layout: 'compact' | 'comfortable' | 'spacious';
  itemsPerPage: number;
  defaultView: 'grid' | 'list';
  sortOrder: 'asc' | 'desc';
  groupBy: string | null;
  filters: Record<string, any>;
} 