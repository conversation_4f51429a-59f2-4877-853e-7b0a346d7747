// API请求和响应类型定义

// 基础响应结构
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  code: number;
  timestamp: string;
}

// 分页响应结构
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 登录请求
export interface LoginRequest {
  username: string;
  password: string;
  rememberMe?: boolean;
}

// 登录响应
export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: {
    id: string;
    username: string;
    nickname: string;
    role: 'admin' | 'cashier' | 'manager';
    permissions: string[];
    avatar?: string;
  };
}

// 商品信息
export interface Product {
  id: string;
  name: string;
  price: number;
  unit: string;
  categoryId: string;
  categoryName: string;
  description?: string;
  image?: string;
  specifications?: string;
  status: 'active' | 'inactive';
  stock: number;
  minStock: number;
  createdAt: string;
  updatedAt: string;
}

// 商品分类
export interface Category {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  sort: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 订单相关类型
export interface CreateOrderRequest {
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
  }>;
  memberId?: string;
  paymentMethod: 'cash' | 'wechat' | 'alipay' | 'card';
  discount?: number;
  note?: string;
}

export interface OrderResponse {
  id: string;
  orderNo: string;
  items: Array<{
    id: string;
    productId: string;
    name: string;
    price: number;
    quantity: number;
    totalPrice: number;
  }>;
  subtotal: number;
  discount: number;
  totalAmount: number;
  paymentMethod: string;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  memberId?: string;
  memberName?: string;
  cashierId: string;
  cashierName: string;
  createdAt: string;
  updatedAt: string;
  note?: string;
}

// 会员相关类型
export interface MemberRequest {
  name: string;
  phone: string;
  email?: string;
  gender?: 'male' | 'female';
  birthday?: string;
}

export interface MemberResponse {
  id: string;
  memberNo: string;
  name: string;
  phone: string;
  email?: string;
  gender?: 'male' | 'female';
  birthday?: string;
  level: 'bronze' | 'silver' | 'gold' | 'diamond';
  points: number;
  balance: number;
  totalSpent: number;
  discount: number;
  status: 'active' | 'inactive' | 'suspended';
  joinDate: string;
  lastVisit?: string;
  note?: string;
}

// 充值请求
export interface RechargeRequest {
  memberId: string;
  amount: number;
  paymentMethod: 'cash' | 'wechat' | 'alipay' | 'bank';
  note?: string;
}

// 退款请求
export interface RefundRequest {
  orderId: string;
  reason: string;
  amount?: number;
  items?: Array<{
    productId: string;
    quantity: number;
  }>;
}

// 财务报表类型
export interface SalesReport {
  date: string;
  totalAmount: number;
  totalOrders: number;
  totalItems: number;
  avgOrderAmount: number;
  paymentMethods: Record<string, number>;
  topProducts: Array<{
    productId: string;
    name: string;
    quantity: number;
    amount: number;
  }>;
}

// 错误响应
export interface ApiError {
  code: number;
  message: string;
  details?: any;
  timestamp: string;
}

// 文件上传响应
export interface UploadResponse {
  url: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
}

// 系统配置
export interface SystemConfig {
  id: string;
  key: string;
  value: string;
  description?: string;
  category: string;
  updatedAt: string;
} 