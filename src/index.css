/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  font-size: 18px;
  line-height: 1.5;
  color: #212121;
  background-color: #FAFAFA;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

/* 适老化设计 - 确保最小字体大小 */
.ant-typography {
  font-size: 18px !important;
}

.ant-btn {
  min-height: 48px !important;
  font-size: 18px !important;
  font-weight: 500 !important;
}

.ant-input {
  min-height: 48px !important;
  font-size: 18px !important;
}

.ant-select {
  min-height: 48px !important;
  font-size: 18px !important;
}

.ant-table {
  font-size: 16px !important;
}

.ant-table-thead > tr > th {
  font-size: 18px !important;
  font-weight: 600 !important;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border-radius: 12px !important;
}

.ant-modal {
  font-size: 18px !important;
}

.ant-modal-title {
  font-size: 24px !important;
  font-weight: 600 !important;
}

/* 高对比度模式 */
.high-contrast {
  --primary-color: #000000;
  --background-color: #FFFFFF;
  --text-color: #000000;
  --border-color: #000000;
}

.high-contrast .ant-btn {
  border: 2px solid #000000 !important;
  color: #000000 !important;
}

.high-contrast .ant-btn-primary {
  background-color: #000000 !important;
  color: #FFFFFF !important;
}

/* 大字体模式 */
.large-font {
  font-size: 22px !important;
}

.large-font .ant-btn {
  font-size: 22px !important;
  min-height: 56px !important;
}

.large-font .ant-input {
  font-size: 22px !important;
  min-height: 56px !important;
}

.large-font .ant-select {
  font-size: 22px !important;
  min-height: 56px !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body {
    font-size: 12px !important;
    color: #000000 !important;
    background-color: #FFFFFF !important;
  }
}

/* 响应式设计 */
@media (max-width: 1366px) {
  body {
    font-size: 16px;
  }
  
  .ant-btn {
    min-height: 44px !important;
    font-size: 16px !important;
  }
  
  .ant-input {
    min-height: 44px !important;
    font-size: 16px !important;
  }
}

@media (max-width: 1024px) {
  body {
    font-size: 18px;
  }
  
  .ant-btn {
    min-height: 48px !important;
    font-size: 18px !important;
  }
}

@media (max-width: 768px) {
  body {
    font-size: 20px;
  }
  
  .ant-btn {
    min-height: 52px !important;
    font-size: 20px !important;
  }
  
  .ant-input {
    min-height: 52px !important;
    font-size: 20px !important;
  }
} 