describe('工具函数测试', () => {
  test('基础数学计算', () => {
    expect(1 + 1).toBe(2);
    expect(2 * 3).toBe(6);
  });

  test('字符串操作', () => {
    const testString = 'Hello World';
    expect(testString.toLowerCase()).toBe('hello world');
    expect(testString.length).toBe(11);
  });

  test('数组操作', () => {
    const testArray = [1, 2, 3, 4, 5];
    expect(testArray.length).toBe(5);
    expect(testArray.includes(3)).toBe(true);
    expect(testArray.filter(x => x > 3)).toEqual([4, 5]);
  });

  test('对象操作', () => {
    const testObject = { name: '张三', age: 30 };
    expect(testObject.name).toBe('张三');
    expect(Object.keys(testObject)).toEqual(['name', 'age']);
  });
}); 