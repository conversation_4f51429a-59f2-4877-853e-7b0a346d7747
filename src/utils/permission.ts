import type { User, UserRole } from '../types/auth';
import { authService } from '../services/authService';

// 权限常量定义
export const PERMISSIONS = {
  // 订单权限
  ORDER_CREATE: 'order.create',
  ORDER_QUERY: 'order.query',
  ORDER_UPDATE: 'order.update',
  ORDER_DELETE: 'order.delete',
  ORDER_ALL: 'order.*',

  // 支付权限
  PAYMENT_PROCESS: 'payment.process',
  PAYMENT_REFUND: 'payment.refund',
  PAYMENT_QUERY: 'payment.query',
  PAYMENT_ALL: 'payment.*',

  // 会员权限
  MEMBER_CREATE: 'member.create',
  MEMBER_QUERY: 'member.query',
  MEMBER_UPDATE: 'member.update',
  MEMBER_DELETE: 'member.delete',
  MEMBER_ALL: 'member.*',

  // 商品权限
  PRODUCT_CREATE: 'product.create',
  PRODUCT_QUERY: 'product.query',
  PRODUCT_UPDATE: 'product.update',
  PRODUCT_DELETE: 'product.delete',
  PRODUCT_ALL: 'product.*',

  // 退款权限
  REFUND_CREATE: 'refund.create',
  REFUND_APPROVE: 'refund.approve',
  REFUND_PROCESS: 'refund.process',
  REFUND_QUERY: 'refund.query',
  REFUND_ALL: 'refund.*',

  // 财务权限
  FINANCE_QUERY: 'finance.query',
  FINANCE_REPORT: 'finance.report',
  FINANCE_EXPORT: 'finance.export',
  FINANCE_ALL: 'finance.*',

  // 系统权限
  SYSTEM_SETTINGS: 'system.settings',
  SYSTEM_BACKUP: 'system.backup',
  SYSTEM_LOG: 'system.log',
  SYSTEM_ALL: 'system.*',

  // 用户管理权限
  USER_CREATE: 'user.create',
  USER_QUERY: 'user.query',
  USER_UPDATE: 'user.update',
  USER_DELETE: 'user.delete',
  USER_ALL: 'user.*',

  // 超级权限
  SUPER_ADMIN: '*',
} as const;

// 角色权限映射
export const ROLE_PERMISSIONS: Record<UserRole, string[]> = {
  admin: [PERMISSIONS.SUPER_ADMIN], // 管理员有所有权限
  manager: [
    PERMISSIONS.ORDER_ALL,
    PERMISSIONS.PAYMENT_ALL,
    PERMISSIONS.MEMBER_ALL,
    PERMISSIONS.PRODUCT_ALL,
    PERMISSIONS.REFUND_ALL,
    PERMISSIONS.FINANCE_QUERY,
    PERMISSIONS.FINANCE_REPORT,
    PERMISSIONS.SYSTEM_SETTINGS,
    PERMISSIONS.USER_QUERY,
  ],
  cashier: [
    PERMISSIONS.ORDER_CREATE,
    PERMISSIONS.ORDER_QUERY,
    PERMISSIONS.PAYMENT_PROCESS,
    PERMISSIONS.MEMBER_QUERY,
    PERMISSIONS.PRODUCT_QUERY,
    PERMISSIONS.REFUND_CREATE,
  ],
};

/**
 * 检查用户是否具有特定权限
 */
export function hasPermission(user: User | null, permission: string): boolean {
  if (!user || !user.permissions) {
    return false;
  }

  return authService.hasPermission(user.permissions, permission);
}

/**
 * 检查用户是否具有任一权限
 */
export function hasAnyPermission(user: User | null, permissions: string[]): boolean {
  if (!user || !user.permissions) {
    return false;
  }

  return permissions.some(permission => 
    authService.hasPermission(user.permissions, permission)
  );
}

/**
 * 检查用户是否具有所有权限
 */
export function hasAllPermissions(user: User | null, permissions: string[]): boolean {
  if (!user || !user.permissions) {
    return false;
  }

  return permissions.every(permission => 
    authService.hasPermission(user.permissions, permission)
  );
}

/**
 * 检查用户角色
 */
export function hasRole(user: User | null, role: UserRole): boolean {
  return user?.role === role;
}

/**
 * 检查用户是否具有任一角色
 */
export function hasAnyRole(user: User | null, roles: UserRole[]): boolean {
  if (!user) {
    return false;
  }

  return roles.indexOf(user.role) >= 0;
}

/**
 * 检查是否为管理员
 */
export function isAdmin(user: User | null): boolean {
  return hasRole(user, 'admin');
}

/**
 * 检查是否为管理层（管理员或店长）
 */
export function isManager(user: User | null): boolean {
  return hasAnyRole(user, ['admin', 'manager']);
}

/**
 * 获取用户可访问的菜单项
 */
export function getAccessibleMenus(user: User | null) {
  const menus = [
    {
      key: 'dashboard',
      label: '工作台',
      permissions: [],
      icon: 'DashboardOutlined',
    },
    {
      key: 'order',
      label: '订单管理',
      permissions: [PERMISSIONS.ORDER_QUERY],
      icon: 'ShoppingCartOutlined',
      children: [
        {
          key: 'order-list',
          label: '订单列表',
          permissions: [PERMISSIONS.ORDER_QUERY],
        },
        {
          key: 'order-create',
          label: '新建订单',
          permissions: [PERMISSIONS.ORDER_CREATE],
        },
      ],
    },
    {
      key: 'payment',
      label: '支付管理',
      permissions: [PERMISSIONS.PAYMENT_PROCESS],
      icon: 'CreditCardOutlined',
    },
    {
      key: 'member',
      label: '会员管理',
      permissions: [PERMISSIONS.MEMBER_QUERY],
      icon: 'UserOutlined',
      children: [
        {
          key: 'member-list',
          label: '会员列表',
          permissions: [PERMISSIONS.MEMBER_QUERY],
        },
        {
          key: 'member-create',
          label: '新增会员',
          permissions: [PERMISSIONS.MEMBER_CREATE],
        },
      ],
    },
    {
      key: 'product',
      label: '商品管理',
      permissions: [PERMISSIONS.PRODUCT_QUERY],
      icon: 'AppstoreOutlined',
      children: [
        {
          key: 'product-list',
          label: '商品列表',
          permissions: [PERMISSIONS.PRODUCT_QUERY],
        },
        {
          key: 'product-create',
          label: '新增商品',
          permissions: [PERMISSIONS.PRODUCT_CREATE],
        },
        {
          key: 'product-category',
          label: '分类管理',
          permissions: [PERMISSIONS.PRODUCT_UPDATE],
        },
      ],
    },
    {
      key: 'refund',
      label: '退款管理',
      permissions: [PERMISSIONS.REFUND_QUERY],
      icon: 'RollbackOutlined',
      children: [
        {
          key: 'refund-list',
          label: '退款列表',
          permissions: [PERMISSIONS.REFUND_QUERY],
        },
        {
          key: 'refund-create',
          label: '申请退款',
          permissions: [PERMISSIONS.REFUND_CREATE],
        },
        {
          key: 'refund-approve',
          label: '退款审批',
          permissions: [PERMISSIONS.REFUND_APPROVE],
        },
      ],
    },
    {
      key: 'finance',
      label: '财务管理',
      permissions: [PERMISSIONS.FINANCE_QUERY],
      icon: 'LineChartOutlined',
      children: [
        {
          key: 'finance-report',
          label: '财务报表',
          permissions: [PERMISSIONS.FINANCE_REPORT],
        },
        {
          key: 'finance-closing',
          label: '日结管理',
          permissions: [PERMISSIONS.FINANCE_QUERY],
        },
      ],
    },
    {
      key: 'system',
      label: '系统管理',
      permissions: [PERMISSIONS.SYSTEM_SETTINGS],
      icon: 'SettingOutlined',
      children: [
        {
          key: 'system-settings',
          label: '系统设置',
          permissions: [PERMISSIONS.SYSTEM_SETTINGS],
        },
        {
          key: 'user-management',
          label: '用户管理',
          permissions: [PERMISSIONS.USER_QUERY],
        },
        {
          key: 'operation-log',
          label: '操作日志',
          permissions: [PERMISSIONS.SYSTEM_LOG],
        },
      ],
    },
  ];

  // 过滤用户有权限访问的菜单
  return filterMenusByPermissions(menus, user);
}

/**
 * 根据权限过滤菜单
 */
function filterMenusByPermissions(menus: any[], user: User | null): any[] {
  return menus.filter(menu => {
    // 如果没有权限要求，所有人都可访问
    if (!menu.permissions || menu.permissions.length === 0) {
      return true;
    }

    // 检查是否有任一权限
    const hasAccess = hasAnyPermission(user, menu.permissions);
    
    if (hasAccess && menu.children) {
      // 递归过滤子菜单
      menu.children = filterMenusByPermissions(menu.children, user);
    }

    return hasAccess;
  });
}

/**
 * 检查路由权限
 */
export function checkRoutePermission(route: string, user: User | null): boolean {
  // 路由权限映射
  const routePermissions: Record<string, string[]> = {
    '/dashboard': [],
    '/order': [PERMISSIONS.ORDER_QUERY],
    '/order/create': [PERMISSIONS.ORDER_CREATE],
    '/payment': [PERMISSIONS.PAYMENT_PROCESS],
    '/member': [PERMISSIONS.MEMBER_QUERY],
    '/member/create': [PERMISSIONS.MEMBER_CREATE],
    '/product': [PERMISSIONS.PRODUCT_QUERY],
    '/product/create': [PERMISSIONS.PRODUCT_CREATE],
    '/refund': [PERMISSIONS.REFUND_QUERY],
    '/refund/create': [PERMISSIONS.REFUND_CREATE],
    '/refund/approve': [PERMISSIONS.REFUND_APPROVE],
    '/finance': [PERMISSIONS.FINANCE_QUERY],
    '/finance/report': [PERMISSIONS.FINANCE_REPORT],
    '/system': [PERMISSIONS.SYSTEM_SETTINGS],
    '/system/users': [PERMISSIONS.USER_QUERY],
    '/system/logs': [PERMISSIONS.SYSTEM_LOG],
  };

  const requiredPermissions = routePermissions[route];
  
  // 如果路由没有权限要求，允许访问
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true;
  }

  // 检查用户是否有任一所需权限
  return hasAnyPermission(user, requiredPermissions);
}

/**
 * 路由守卫函数
 */
export function routeGuard(route: string, user: User | null): boolean {
  // 登录页面和公共页面不需要权限验证
  const publicRoutes = ['/login', '/forgot-password', '/help'];
  
  if (publicRoutes.indexOf(route) >= 0) {
    return true;
  }

  // 检查是否已登录
  if (!user) {
    return false;
  }

  // 检查路由权限
  return checkRoutePermission(route, user);
}

/**
 * 获取用户权限标签
 */
export function getPermissionLabel(permission: string): string {
  const permissionLabels: Record<string, string> = {
    [PERMISSIONS.ORDER_CREATE]: '创建订单',
    [PERMISSIONS.ORDER_QUERY]: '查询订单',
    [PERMISSIONS.ORDER_UPDATE]: '修改订单',
    [PERMISSIONS.ORDER_DELETE]: '删除订单',
    [PERMISSIONS.PAYMENT_PROCESS]: '处理支付',
    [PERMISSIONS.PAYMENT_REFUND]: '处理退款',
    [PERMISSIONS.MEMBER_QUERY]: '查询会员',
    [PERMISSIONS.MEMBER_CREATE]: '创建会员',
    [PERMISSIONS.PRODUCT_QUERY]: '查询商品',
    [PERMISSIONS.PRODUCT_CREATE]: '创建商品',
    [PERMISSIONS.REFUND_CREATE]: '申请退款',
    [PERMISSIONS.REFUND_APPROVE]: '审批退款',
    [PERMISSIONS.FINANCE_QUERY]: '查询财务',
    [PERMISSIONS.FINANCE_REPORT]: '财务报表',
    [PERMISSIONS.SYSTEM_SETTINGS]: '系统设置',
    [PERMISSIONS.USER_QUERY]: '查询用户',
    [PERMISSIONS.SUPER_ADMIN]: '超级管理员',
  };

  return permissionLabels[permission] || permission;
}

/**
 * 获取角色标签
 */
export function getRoleLabel(role: UserRole): string {
  const roleLabels: Record<UserRole, string> = {
    admin: '系统管理员',
    manager: '店长',
    cashier: '收银员',
  };

  return roleLabels[role];
} 