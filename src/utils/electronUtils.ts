/**
 * Electron工具函数
 * 
 * 提供跨平台功能和Electron环境检测工具
 */

import { electronService } from '../services/electronService';

/**
 * 平台检测工具
 */
export const PlatformUtils = {
  /**
   * 检查是否为Windows平台
   */
  isWindows(): boolean {
    return electronService.getPlatformInfo().isWindows;
  },

  /**
   * 检查是否为macOS平台
   */
  isMacOS(): boolean {
    return electronService.getPlatformInfo().isMacOS;
  },

  /**
   * 检查是否为Linux平台
   */
  isLinux(): boolean {
    return electronService.getPlatformInfo().isLinux;
  },

  /**
   * 获取平台名称
   */
  getPlatformName(): string {
    const platform = electronService.getPlatformInfo();
    if (platform.isWindows) return 'Windows';
    if (platform.isMacOS) return 'macOS';
    if (platform.isLinux) return 'Linux';
    return 'Unknown';
  },

  /**
   * 获取适合当前平台的键盘快捷键修饰符
   */
  getModifierKey(): string {
    return this.isMacOS() ? 'Cmd' : 'Ctrl';
  },

  /**
   * 格式化快捷键显示文本
   */
  formatShortcut(key: string): string {
    const modifier = this.getModifierKey();
    return `${modifier}+${key}`;
  }
};

/**
 * 文件操作工具
 */
export const FileUtils = {
  /**
   * 安全地读取文件
   */
  async readFile(filePath: string): Promise<string | null> {
    try {
      const result = await electronService.readFile(filePath);
      return result.success ? result.data || null : null;
    } catch (error) {
      console.error('读取文件失败:', error);
      return null;
    }
  },

  /**
   * 安全地写入文件
   */
  async writeFile(filePath: string, data: string): Promise<boolean> {
    try {
      const result = await electronService.writeFile(filePath, data);
      return result.success;
    } catch (error) {
      console.error('写入文件失败:', error);
      return false;
    }
  },

  /**
   * 显示文件选择对话框
   */
  async selectFile(options?: {
    title?: string;
    filters?: Array<{ name: string; extensions: string[] }>;
    multiSelect?: boolean;
  }): Promise<string[]> {
    try {
      const dialogOptions: any = {
        title: options?.title || '选择文件',
        properties: ['openFile']
      };

      if (options?.multiSelect) {
        dialogOptions.properties.push('multiSelections');
      }

      if (options?.filters) {
        dialogOptions.filters = options.filters;
      }

      const result = await electronService.showOpenDialog(dialogOptions);
      return result.canceled ? [] : result.filePaths;
    } catch (error) {
      console.error('文件选择失败:', error);
      return [];
    }
  },

  /**
   * 显示文件保存对话框
   */
  async saveFile(options?: {
    title?: string;
    defaultPath?: string;
    filters?: Array<{ name: string; extensions: string[] }>;
  }): Promise<string | null> {
    try {
      const dialogOptions: {
        title?: string;
        defaultPath?: string;
        filters?: Array<{ name: string; extensions: string[] }>;
      } = {
        title: options?.title || '保存文件'
      };

      if (options?.defaultPath) {
        dialogOptions.defaultPath = options.defaultPath;
      }

      if (options?.filters) {
        dialogOptions.filters = options.filters;
      }

      const result = await electronService.showSaveDialog(dialogOptions);
      return result.canceled ? null : result.filePath || null;
    } catch (error) {
      console.error('文件保存失败:', error);
      return null;
    }
  }
};

/**
 * 窗口操作工具
 */
export const WindowUtils = {
  /**
   * 最小化到系统托盘
   */
  async minimizeToTray(): Promise<void> {
    try {
      await electronService.minimizeToTray();
    } catch (error) {
      console.error('最小化到托盘失败:', error);
    }
  },

  /**
   * 设置窗口置顶
   */
  async setAlwaysOnTop(flag: boolean): Promise<void> {
    try {
      await electronService.setAlwaysOnTop(flag);
    } catch (error) {
      console.error('设置窗口置顶失败:', error);
    }
  },

  /**
   * 打开外部链接
   */
  async openExternal(url: string): Promise<void> {
    try {
      await electronService.openExternal(url);
    } catch (error) {
      console.error('打开外部链接失败:', error);
    }
  },

  /**
   * 在文件管理器中显示文件
   */
  async showInFolder(path: string): Promise<void> {
    try {
      await electronService.showItemInFolder(path);
    } catch (error) {
      console.error('在文件管理器中显示文件失败:', error);
    }
  }
};

/**
 * 打印工具
 */
export const PrintUtils = {
  /**
   * 打印小票
   */
  async printReceipt(content?: any): Promise<boolean> {
    try {
      const result = await electronService.printReceipt(content);
      return result.success;
    } catch (error) {
      console.error('打印失败:', error);
      return false;
    }
  },

  /**
   * 打印页面
   */
  async printPage(): Promise<boolean> {
    try {
      if (electronService.isElectronEnvironment()) {
        const result = await electronService.printReceipt();
        return result.success;
      } else {
        window.print();
        return true;
      }
    } catch (error) {
      console.error('打印页面失败:', error);
      return false;
    }
  }
};

/**
 * 消息工具
 */
export const MessageUtils = {
  /**
   * 显示信息消息
   */
  async showInfo(message: string, title?: string): Promise<void> {
    try {
      await electronService.showMessageBox({
        type: 'info',
        title: title || '信息',
        message
      });
    } catch (error) {
      console.error('显示信息消息失败:', error);
      alert(message); // 降级到浏览器alert
    }
  },

  /**
   * 显示警告消息
   */
  async showWarning(message: string, title?: string): Promise<void> {
    try {
      await electronService.showMessageBox({
        type: 'warning',
        title: title || '警告',
        message
      });
    } catch (error) {
      console.error('显示警告消息失败:', error);
      alert(message);
    }
  },

  /**
   * 显示错误消息
   */
  async showError(message: string, title?: string): Promise<void> {
    try {
      await electronService.showMessageBox({
        type: 'error',
        title: title || '错误',
        message
      });
    } catch (error) {
      console.error('显示错误消息失败:', error);
      alert(message);
    }
  },

  /**
   * 显示确认对话框
   */
  async showConfirm(message: string, title?: string): Promise<boolean> {
    try {
      const result = await electronService.showMessageBox({
        type: 'question',
        title: title || '确认',
        message,
        buttons: ['确定', '取消']
      });
      return result.response === 0;
    } catch (error) {
      console.error('显示确认对话框失败:', error);
      return confirm(message);
    }
  }
};

/**
 * 更新工具
 */
export const UpdateUtils = {
  /**
   * 检查应用更新
   */
  async checkForUpdates(): Promise<void> {
    try {
      await electronService.checkForUpdates();
    } catch (error) {
      console.error('检查更新失败:', error);
    }
  },

  /**
   * 获取应用版本
   */
  async getVersion(): Promise<string> {
    try {
      return await electronService.getAppVersion();
    } catch (error) {
      console.error('获取版本失败:', error);
      return '1.0.0';
    }
  },

  /**
   * 获取系统信息
   */
  async getSystemInfo(): Promise<{
    platform: string;
    arch: string;
    version: string;
    electronVersion: string;
    chromeVersion: string;
  }> {
    try {
      return await electronService.getSystemInfo();
    } catch (error) {
      console.error('获取系统信息失败:', error);
      return {
        platform: 'unknown',
        arch: 'unknown',
        version: 'unknown',
        electronVersion: 'N/A',
        chromeVersion: 'unknown'
      };
    }
  }
};

/**
 * 环境检测工具
 */
export const EnvironmentUtils = {
  /**
   * 检查是否在Electron环境中
   */
  isElectron(): boolean {
    return electronService.isElectronEnvironment();
  },

  /**
   * 检查是否为开发环境
   */
  isDevelopment(): boolean {
    return electronService.getEnvInfo().isDev;
  },

  /**
   * 检查是否为生产环境
   */
  isProduction(): boolean {
    return !this.isDevelopment();
  },

  /**
   * 获取环境信息
   */
  getEnvInfo(): { NODE_ENV: string; isDev: boolean } {
    return electronService.getEnvInfo();
  }
};

/**
 * 快捷键处理工具
 */
export const ShortcutUtils = {
  /**
   * 注册菜单动作监听器
   */
  onMenuAction(callback: (action: string, ...args: any[]) => void): () => void {
    return electronService.onMenuAction(callback);
  },

  /**
   * 移除菜单监听器
   */
  removeMenuListener(): void {
    electronService.removeMenuListener();
  },

  /**
   * 检查键盘事件是否匹配快捷键
   */
  matchShortcut(event: KeyboardEvent, shortcut: string): boolean {
    const keys = shortcut.toLowerCase().split('+');
    const modifiers = keys.slice(0, -1);
    const key = keys[keys.length - 1];

    if (!key) {
      return false;
    }

    // 检查修饰键
    for (const modifier of modifiers) {
      switch (modifier) {
        case 'ctrl':
        case 'cmdorctrl':
          if (!event.ctrlKey && !(PlatformUtils.isMacOS() && event.metaKey)) {
            return false;
          }
          break;
        case 'cmd':
        case 'meta':
          if (!event.metaKey) return false;
          break;
        case 'alt':
          if (!event.altKey) return false;
          break;
        case 'shift':
          if (!event.shiftKey) return false;
          break;
      }
    }

    // 检查主键
    return event.key.toLowerCase() === key.toLowerCase();
  }
};

/**
 * 数据导出工具
 */
export const ExportUtils = {
  /**
   * 导出JSON数据
   */
  async exportJSON(data: any, filename: string = 'export.json'): Promise<boolean> {
    try {
      const jsonString = JSON.stringify(data, null, 2);
      return await FileUtils.writeFile(filename, jsonString);
    } catch (error) {
      console.error('导出JSON失败:', error);
      return false;
    }
  },

  /**
   * 导出CSV数据
   */
  async exportCSV(data: any[], headers: string[], filename: string = 'export.csv'): Promise<boolean> {
    try {
      const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => {
          const value = row[header] || '';
          // 处理包含逗号或引号的值
          return typeof value === 'string' && (value.includes(',') || value.includes('"'))
            ? `"${value.replace(/"/g, '""')}"`
            : value;
        }).join(','))
      ].join('\n');

      return await FileUtils.writeFile(filename, csvContent);
    } catch (error) {
      console.error('导出CSV失败:', error);
      return false;
    }
  }
};

// 导出所有工具
export const ElectronUtils = {
  Platform: PlatformUtils,
  File: FileUtils,
  Window: WindowUtils,
  Print: PrintUtils,
  Message: MessageUtils,
  Update: UpdateUtils,
  Environment: EnvironmentUtils,
  Shortcut: ShortcutUtils,
  Export: ExportUtils
}; 