import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';

import { api } from '../services/api';
import authSlice from './slices/authSlice';
import orderSlice from './slices/orderSlice';
import memberSlice from './slices/memberSlice';
import aiSlice from './slices/aiSlice';
import systemSlice from './slices/systemSlice';
import productSlice from './slices/productSlice';

export const store = configureStore({
  reducer: {
    // API切片
    [api.reducerPath]: api.reducer,
    
    // 业务状态切片
    auth: authSlice,
    order: orderSlice,
    member: memberSlice,
    ai: aiSlice,
    system: systemSlice,
    product: productSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['ai.camera.stream'], // MediaStream对象不可序列化
      },
    }).concat(api.middleware),
  devTools: process.env.NODE_ENV !== 'production',
});

// 设置API缓存更新监听器
setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch; 