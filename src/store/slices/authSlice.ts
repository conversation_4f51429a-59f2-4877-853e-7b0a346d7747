import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { authService } from '../../services/authService';
import type { LoginCredentials } from '../../types/auth';

// 认证状态接口
export interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  user: {
    id: string;
    username: string;
    nickname: string;
    role: 'admin' | 'cashier' | 'manager';
    permissions: string[];
  } | null;
  loading: boolean;
  error: string | null;
}

// 初始状态
const initialState: AuthState = {
  isAuthenticated: false,
  token: localStorage.getItem('auth_token'),
  user: null,
  loading: false,
  error: null,
};

// 异步登录action
export const login = createAsyncThunk(
  'auth/login',
  async (credentials: LoginCredentials) => {
    const response = await authService.login(credentials);
    // 保存用户信息到localStorage
    localStorage.setItem('auth_user', JSON.stringify(response.user));
    return response;
  }
);

// 异步登出action
export const logout = createAsyncThunk(
  'auth/logout',
  async () => {
    await authService.logout();
  }
);

// 创建认证切片
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // 开始登录
    loginStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    
    // 登录成功
    loginSuccess: (state, action: PayloadAction<{
      token: string;
      user: AuthState['user'];
    }>) => {
      state.loading = false;
      state.isAuthenticated = true;
      state.token = action.payload.token;
      state.user = action.payload.user;
      state.error = null;
      
      // 保存token到localStorage
      if (action.payload.token) {
        localStorage.setItem('auth_token', action.payload.token);
      }
    },
    
    // 登录失败
    loginFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.isAuthenticated = false;
      state.token = null;
      state.user = null;
      state.error = action.payload;
      
      // 清除localStorage中的token
      localStorage.removeItem('auth_token');
    },
    
    // 登出
    logoutAction: (state) => {
      state.isAuthenticated = false;
      state.token = null;
      state.user = null;
      state.loading = false;
      state.error = null;
      
      // 清除localStorage中的token
      localStorage.removeItem('auth_token');
    },
    
    // 更新用户信息
    updateUser: (state, action: PayloadAction<Partial<AuthState['user']>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
    
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
    
    // 恢复认证状态(从localStorage)
    restoreAuth: (state, action: PayloadAction<{
      token: string;
      user: AuthState['user'];
    }>) => {
      state.isAuthenticated = true;
      state.token = action.payload.token;
      state.user = action.payload.user;
    },
  },
  extraReducers: (builder) => {
    builder
      // 登录异步操作
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.user = action.payload.user;
        state.error = null;
        
        // 保存token到localStorage
        localStorage.setItem('auth_token', action.payload.token);
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.isAuthenticated = false;
        state.token = null;
        state.user = null;
        state.error = action.error.message || '登录失败';
        
        // 清除localStorage中的token
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
      })
      // 登出异步操作
      .addCase(logout.pending, (state) => {
        state.loading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.loading = false;
        state.isAuthenticated = false;
        state.token = null;
        state.user = null;
        state.error = null;
        
        // 清除localStorage
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
      })
      .addCase(logout.rejected, (state) => {
        state.loading = false;
        // 即使登出失败也清除本地状态
        state.isAuthenticated = false;
        state.token = null;
        state.user = null;
        
        // 清除localStorage
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
      });
  },
});

export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logoutAction,
  updateUser,
  clearError,
  restoreAuth,
} = authSlice.actions;

export default authSlice.reducer; 