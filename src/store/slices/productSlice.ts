import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  categoryId: string;
  categoryName: string;
  image: string;
  images?: string[];
  unit: string;
  stock: number;
  specifications?: string;
  tags?: string[];
  isRecommended: boolean;
  isHot: boolean;
  isNew: boolean;
  discount?: number;
  nutrition?: {
    calories: number;
    protein: number;
    fat: number;
    carbs: number;
  };
  allergens?: string[];
  rating?: number;
  reviewCount?: number;
  preparationTime?: number; // 分钟
  spicyLevel?: 0 | 1 | 2 | 3; // 辣度等级
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  icon?: string;
  image?: string;
  description?: string;
  order: number;
  isActive: boolean;
  parentId?: string;
  productCount: number;
  createdAt: string;
}

export interface ProductFilters {
  categoryId?: string;
  priceRange?: [number, number];
  tags?: string[];
  isRecommended?: boolean;
  isHot?: boolean;
  isNew?: boolean;
  inStock?: boolean;
  spicyLevel?: number[];
  rating?: number;
}

export interface ProductSearchParams {
  keyword?: string;
  filters?: ProductFilters;
  sortBy?: 'price' | 'rating' | 'createdAt' | 'name' | 'popularity';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  pageSize?: number;
}

export interface ProductState {
  // 商品数据
  products: Product[];
  categories: Category[];
  favoriteProducts: string[];
  
  // 当前状态
  currentCategory: string | null;
  searchKeyword: string;
  filters: ProductFilters;
  sortBy: ProductSearchParams['sortBy'];
  sortOrder: ProductSearchParams['sortOrder'];
  
  // 分页
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
  };
  
  // UI状态
  viewMode: 'grid' | 'list';
  gridSize: 'small' | 'medium' | 'large';
  showFilters: boolean;
  
  // 加载状态
  loading: {
    products: boolean;
    categories: boolean;
    search: boolean;
  };
  
  // 错误状态
  error: string | null;
}

const initialState: ProductState = {
  products: [],
  categories: [],
  favoriteProducts: JSON.parse(localStorage.getItem('favoriteProducts') || '[]'),
  
  currentCategory: null,
  searchKeyword: '',
  filters: {},
  sortBy: 'popularity',
  sortOrder: 'desc',
  
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0,
    hasMore: false,
  },
  
  viewMode: 'grid',
  gridSize: 'medium',
  showFilters: false,
  
  loading: {
    products: false,
    categories: false,
    search: false,
  },
  
  error: null,
};

// 异步操作
export const fetchProducts = createAsyncThunk(
  'product/fetchProducts',
  async (params: ProductSearchParams = {}) => {
    const { productService } = await import('../../services/productService');
    const result = await productService.getProducts(params);
    return result;
  }
);

export const fetchCategories = createAsyncThunk(
  'product/fetchCategories',
  async () => {
    const { productService } = await import('../../services/productService');
    const categories = await productService.getCategories();
    return categories;
  }
);

export const searchProducts = createAsyncThunk(
  'product/searchProducts',
  async (params: ProductSearchParams) => {
    const { productService } = await import('../../services/productService');
    const result = await productService.searchProducts(params);
    return result;
  }
);

const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    // 分类操作
    setCurrentCategory: (state, action: PayloadAction<string | null>) => {
      state.currentCategory = action.payload;
      state.pagination.page = 1;
    },
    
    // 搜索操作
    setSearchKeyword: (state, action: PayloadAction<string>) => {
      state.searchKeyword = action.payload;
      state.pagination.page = 1;
    },
    
    // 筛选操作
    setFilters: (state, action: PayloadAction<ProductFilters>) => {
      state.filters = { ...state.filters, ...action.payload };
      state.pagination.page = 1;
    },
    
    clearFilters: (state) => {
      state.filters = {};
      state.pagination.page = 1;
    },
    
    // 排序操作
    setSorting: (state, action: PayloadAction<{
      sortBy: ProductSearchParams['sortBy'];
      sortOrder: ProductSearchParams['sortOrder'];
    }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
      state.pagination.page = 1;
    },
    
    // 分页操作
    setPage: (state, action: PayloadAction<number>) => {
      state.pagination.page = action.payload;
    },
    
    setPageSize: (state, action: PayloadAction<number>) => {
      state.pagination.pageSize = action.payload;
      state.pagination.page = 1;
    },
    
    // 收藏操作
    toggleFavorite: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      const index = state.favoriteProducts.indexOf(productId);
      
      if (index > -1) {
        state.favoriteProducts.splice(index, 1);
      } else {
        state.favoriteProducts.push(productId);
      }
      
      // 持久化到本地存储
      localStorage.setItem('favoriteProducts', JSON.stringify(state.favoriteProducts));
    },
    
    // 视图模式
    setViewMode: (state, action: PayloadAction<'grid' | 'list'>) => {
      state.viewMode = action.payload;
    },
    
    setGridSize: (state, action: PayloadAction<'small' | 'medium' | 'large'>) => {
      state.gridSize = action.payload;
    },
    
    toggleFilters: (state) => {
      state.showFilters = !state.showFilters;
    },
    
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
    
    // 重置状态
    resetSearch: (state) => {
      state.searchKeyword = '';
      state.filters = {};
      state.currentCategory = null;
      state.pagination.page = 1;
    },
  },
  
  extraReducers: (builder) => {
    // 获取商品列表
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.loading.products = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading.products = false;
        
        if (action.meta.arg.page === 1) {
          // 新查询，替换商品列表
          state.products = action.payload.products;
        } else {
          // 加载更多，追加到商品列表
          state.products.push(...action.payload.products);
        }
        
        state.pagination = {
          page: action.payload.page,
          pageSize: action.payload.pageSize,
          total: action.payload.total,
          hasMore: action.payload.page * action.payload.pageSize < action.payload.total,
        };
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading.products = false;
        state.error = action.error.message || '获取商品列表失败';
      });
    
    // 获取分类列表
    builder
      .addCase(fetchCategories.pending, (state) => {
        state.loading.categories = true;
        state.error = null;
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.loading.categories = false;
        state.categories = action.payload;
      })
      .addCase(fetchCategories.rejected, (state, action) => {
        state.loading.categories = false;
        state.error = action.error.message || '获取分类列表失败';
      });
    
    // 搜索商品
    builder
      .addCase(searchProducts.pending, (state) => {
        state.loading.search = true;
        state.error = null;
      })
      .addCase(searchProducts.fulfilled, (state, action) => {
        state.loading.search = false;
        state.products = action.payload.products;
        state.pagination = {
          page: action.payload.page,
          pageSize: action.payload.pageSize,
          total: action.payload.total,
          hasMore: action.payload.page * action.payload.pageSize < action.payload.total,
        };
      })
      .addCase(searchProducts.rejected, (state, action) => {
        state.loading.search = false;
        state.error = action.error.message || '搜索商品失败';
      });
  },
});

export const {
  setCurrentCategory,
  setSearchKeyword,
  setFilters,
  clearFilters,
  setSorting,
  setPage,
  setPageSize,
  toggleFavorite,
  setViewMode,
  setGridSize,
  toggleFilters,
  clearError,
  resetSearch,
} = productSlice.actions;

export default productSlice.reducer; 