import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 会员接口
export interface Member {
  id: string;
  memberNo: string;
  name: string;
  phone: string;
  email?: string;
  gender?: 'male' | 'female';
  birthday?: string;
  level: 'bronze' | 'silver' | 'gold' | 'diamond';
  points: number;
  balance: number;
  totalSpent: number;
  discount: number;
  status: 'active' | 'inactive' | 'suspended';
  joinDate: string;
  lastVisit?: string;
  note?: string;
}

// 会员状态接口
export interface MemberState {
  currentMember: Member | null;
  memberList: Member[];
  searchResults: Member[];
  loading: {
    searching: boolean;
    updating: boolean;
    recharging: boolean;
  };
  error: string | null;
}

// 初始状态
const initialState: MemberState = {
  currentMember: null,
  memberList: [],
  searchResults: [],
  loading: {
    searching: false,
    updating: false,
    recharging: false,
  },
  error: null,
};

// 会员切片
const memberSlice = createSlice({
  name: 'member',
  initialState,
  reducers: {
    // 设置当前会员
    setCurrentMember: (state, action: PayloadAction<Member | null>) => {
      state.currentMember = action.payload;
    },
    
    // 设置会员列表
    setMemberList: (state, action: PayloadAction<Member[]>) => {
      state.memberList = action.payload;
    },
    
    // 设置搜索结果
    setSearchResults: (state, action: PayloadAction<Member[]>) => {
      state.searchResults = action.payload;
    },
    
    // 更新会员信息
    updateMember: (state, action: PayloadAction<Partial<Member> & { id: string }>) => {
      const { id, ...updates } = action.payload;
      
      // 更新当前会员
      if (state.currentMember?.id === id) {
        state.currentMember = { ...state.currentMember, ...updates };
      }
      
      // 更新会员列表中的会员
      const memberIndex = state.memberList.findIndex(member => member.id === id);
      if (memberIndex !== -1) {
        state.memberList[memberIndex] = { ...state.memberList[memberIndex], ...updates };
      }
      
      // 更新搜索结果中的会员
      const searchIndex = state.searchResults.findIndex(member => member.id === id);
      if (searchIndex !== -1) {
        state.searchResults[searchIndex] = { ...state.searchResults[searchIndex], ...updates };
      }
    },
    
    // 会员充值
    rechargeMember: (state, action: PayloadAction<{
      memberId: string;
      amount: number;
    }>) => {
      const { memberId, amount } = action.payload;
      
      // 更新当前会员余额
      if (state.currentMember?.id === memberId) {
        state.currentMember.balance += amount;
      }
      
      // 更新会员列表中的余额
      const member = state.memberList.find(m => m.id === memberId);
      if (member) {
        member.balance += amount;
      }
    },
    
    // 消费积分
    consumePoints: (state, action: PayloadAction<{
      memberId: string;
      points: number;
    }>) => {
      const { memberId, points } = action.payload;
      
      // 更新当前会员积分
      if (state.currentMember?.id === memberId) {
        state.currentMember.points = Math.max(0, state.currentMember.points - points);
      }
      
      // 更新会员列表中的积分
      const member = state.memberList.find(m => m.id === memberId);
      if (member) {
        member.points = Math.max(0, member.points - points);
      }
    },
    
    // 设置加载状态
    setLoading: (state, action: PayloadAction<{
      type: keyof MemberState['loading'];
      value: boolean;
    }>) => {
      state.loading[action.payload.type] = action.payload.value;
    },
    
    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    // 清除搜索结果
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
  },
});

export const {
  setCurrentMember,
  setMemberList,
  setSearchResults,
  updateMember,
  rechargeMember,
  consumePoints,
  setLoading,
  setError,
  clearSearchResults,
} = memberSlice.actions;

export default memberSlice.reducer; 