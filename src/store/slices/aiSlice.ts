import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// AI识别结果接口
export interface AiRecognitionResult {
  id: string;
  productId: string;
  productName: string;
  confidence: number;
  price: number;
  category: string;
  image: string;
  timestamp: string;
  added: boolean;
}

// AI识别状态接口
export interface AiState {
  // 摄像头状态
  camera: {
    isActive: boolean;
    isAvailable: boolean;
    deviceId: string | null;
    stream: MediaStream | null;
  };
  
  // 识别状态
  recognition: {
    isRecognizing: boolean;
    autoMode: boolean;
    confidenceThreshold: number;
    lastRecognitionTime: string | null;
  };
  
  // 识别结果
  results: {
    current: AiRecognitionResult | null;
    history: AiRecognitionResult[];
    dailyCount: number;
  };
  
  // 模型状态
  model: {
    isLoaded: boolean;
    isLoading: boolean;
    version: string | null;
    error: string | null;
  };
  
  // 设置
  settings: {
    enableAutoRecognition: boolean;
    recognitionInterval: number;
    maxHistoryRecords: number;
    enableSound: boolean;
    enableVibration: boolean;
  };
  
  // 错误信息
  error: string | null;
}

// 初始状态
const initialState: AiState = {
  camera: {
    isActive: false,
    isAvailable: false,
    deviceId: null,
    stream: null,
  },
  recognition: {
    isRecognizing: false,
    autoMode: true,
    confidenceThreshold: 0.85,
    lastRecognitionTime: null,
  },
  results: {
    current: null,
    history: [],
    dailyCount: 0,
  },
  model: {
    isLoaded: false,
    isLoading: false,
    version: null,
    error: null,
  },
  settings: {
    enableAutoRecognition: true,
    recognitionInterval: 3000,
    maxHistoryRecords: 100,
    enableSound: true,
    enableVibration: false,
  },
  error: null,
};

// AI切片
const aiSlice = createSlice({
  name: 'ai',
  initialState,
  reducers: {
    // 设置摄像头状态
    setCameraActive: (state, action: PayloadAction<boolean>) => {
      state.camera.isActive = action.payload;
    },
    
    setCameraAvailable: (state, action: PayloadAction<boolean>) => {
      state.camera.isAvailable = action.payload;
    },
    
    setCameraDevice: (state, action: PayloadAction<string | null>) => {
      state.camera.deviceId = action.payload;
    },
    
    setCameraStream: (state, action: PayloadAction<MediaStream | null>) => {
      state.camera.stream = action.payload;
    },
    
    // 设置识别状态
    setRecognizing: (state, action: PayloadAction<boolean>) => {
      state.recognition.isRecognizing = action.payload;
      if (action.payload) {
        state.recognition.lastRecognitionTime = new Date().toISOString();
      }
    },
    
    setAutoMode: (state, action: PayloadAction<boolean>) => {
      state.recognition.autoMode = action.payload;
    },
    
    setConfidenceThreshold: (state, action: PayloadAction<number>) => {
      state.recognition.confidenceThreshold = action.payload;
    },
    
    // 设置识别结果
    setCurrentResult: (state, action: PayloadAction<AiRecognitionResult | null>) => {
      state.results.current = action.payload;
    },
    
    addToHistory: (state, action: PayloadAction<AiRecognitionResult>) => {
      state.results.history.unshift(action.payload);
      state.results.dailyCount += 1;
      
      // 限制历史记录数量
      if (state.results.history.length > state.settings.maxHistoryRecords) {
        state.results.history.pop();
      }
    },
    
    markResultAsAdded: (state, action: PayloadAction<string>) => {
      // 标记当前结果为已添加
      if (state.results.current?.id === action.payload) {
        state.results.current.added = true;
      }
      
      // 标记历史记录中的结果为已添加
      const historyItem = state.results.history.find(item => item.id === action.payload);
      if (historyItem) {
        historyItem.added = true;
      }
    },
    
    clearHistory: (state) => {
      state.results.history = [];
    },
    
    resetDailyCount: (state) => {
      state.results.dailyCount = 0;
    },
    
    // 设置模型状态
    setModelLoading: (state, action: PayloadAction<boolean>) => {
      state.model.isLoading = action.payload;
    },
    
    setModelLoaded: (state, action: PayloadAction<{
      loaded: boolean;
      version?: string;
    }>) => {
      state.model.isLoaded = action.payload.loaded;
      if (action.payload.version) {
        state.model.version = action.payload.version;
      }
      state.model.isLoading = false;
      state.model.error = null;
    },
    
    setModelError: (state, action: PayloadAction<string>) => {
      state.model.error = action.payload;
      state.model.isLoading = false;
      state.model.isLoaded = false;
    },
    
    // 更新设置
    updateSettings: (state, action: PayloadAction<Partial<AiState['settings']>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    
    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setCameraActive,
  setCameraAvailable,
  setCameraDevice,
  setCameraStream,
  setRecognizing,
  setAutoMode,
  setConfidenceThreshold,
  setCurrentResult,
  addToHistory,
  markResultAsAdded,
  clearHistory,
  resetDailyCount,
  setModelLoading,
  setModelLoaded,
  setModelError,
  updateSettings,
  setError,
} = aiSlice.actions;

export default aiSlice.reducer; 