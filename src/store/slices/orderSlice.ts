import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 订单商品接口
export interface OrderItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  unit: string;
  categoryId: string;
  categoryName: string;
  image?: string;
  specifications?: string;
  discount?: number;
  totalPrice: number;
}

// 订单接口
export interface Order {
  id: string;
  orderNo: string;
  items: OrderItem[];
  subtotal: number;
  discount: number;
  totalAmount: number;
  paymentMethod: 'cash' | 'wechat' | 'alipay' | 'card';
  paymentStatus: 'pending' | 'paid' | 'refunded';
  memberId?: string;
  memberName?: string;
  cashierId: string;
  cashierName: string;
  createdAt: string;
  updatedAt: string;
  note?: string;
}

// 订单状态接口
export interface OrderState {
  // 当前购物车
  cart: {
    items: OrderItem[];
    subtotal: number;
    discount: number;
    totalAmount: number;
    memberId?: string;
    memberDiscount?: number;
  };
  
  // 当前订单
  currentOrder: Order | null;
  
  // 订单历史
  orderHistory: Order[];
  
  // 加载状态
  loading: {
    creating: boolean;
    updating: boolean;
    fetching: boolean;
  };
  
  // 错误信息
  error: string | null;
}

// 初始状态
const initialState: OrderState = {
  cart: {
    items: [],
    subtotal: 0,
    discount: 0,
    totalAmount: 0,
  },
  currentOrder: null,
  orderHistory: [],
  loading: {
    creating: false,
    updating: false,
    fetching: false,
  },
  error: null,
};

// 订单切片
const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    // 添加商品到购物车
    addToCart: (state, action: PayloadAction<Omit<OrderItem, 'totalPrice'>>) => {
      const existingItem = state.cart.items.find(
        item => item.productId === action.payload.productId
      );
      
      if (existingItem) {
        existingItem.quantity += action.payload.quantity;
        existingItem.totalPrice = existingItem.price * existingItem.quantity;
      } else {
        const newItem: OrderItem = {
          ...action.payload,
          totalPrice: action.payload.price * action.payload.quantity,
        };
        state.cart.items.push(newItem);
      }
      
      // 重新计算总价
      orderSlice.caseReducers.recalculateCart(state);
    },
    
    // 更新购物车商品数量
    updateCartItemQuantity: (state, action: PayloadAction<{
      productId: string;
      quantity: number;
    }>) => {
      const item = state.cart.items.find(
        item => item.productId === action.payload.productId
      );
      
      if (item) {
        if (action.payload.quantity <= 0) {
          state.cart.items = state.cart.items.filter(
            item => item.productId !== action.payload.productId
          );
        } else {
          item.quantity = action.payload.quantity;
          item.totalPrice = item.price * item.quantity;
        }
        
        // 重新计算总价
        orderSlice.caseReducers.recalculateCart(state);
      }
    },
    
    // 从购物车移除商品
    removeFromCart: (state, action: PayloadAction<string>) => {
      state.cart.items = state.cart.items.filter(
        item => item.productId !== action.payload
      );
      orderSlice.caseReducers.recalculateCart(state);
    },
    
    // 清空购物车
    clearCart: (state) => {
      state.cart.items = [];
      state.cart.subtotal = 0;
      state.cart.discount = 0;
      state.cart.totalAmount = 0;
      state.cart.memberId = undefined;
      state.cart.memberDiscount = undefined;
    },
    
    // 应用会员折扣
    applyMemberDiscount: (state, action: PayloadAction<{
      memberId: string;
      discount: number;
    }>) => {
      state.cart.memberId = action.payload.memberId;
      state.cart.memberDiscount = action.payload.discount;
      orderSlice.caseReducers.recalculateCart(state);
    },
    
    // 重新计算购物车总价
    recalculateCart: (state) => {
      state.cart.subtotal = state.cart.items.reduce(
        (sum, item) => sum + item.totalPrice, 0
      );
      
      const memberDiscount = state.cart.memberDiscount || 0;
      state.cart.discount = state.cart.subtotal * memberDiscount;
      state.cart.totalAmount = state.cart.subtotal - state.cart.discount;
    },
    
    // 设置当前订单
    setCurrentOrder: (state, action: PayloadAction<Order>) => {
      state.currentOrder = action.payload;
    },
    
    // 添加订单到历史
    addOrderToHistory: (state, action: PayloadAction<Order>) => {
      state.orderHistory.unshift(action.payload);
    },
    
    // 设置加载状态
    setLoading: (state, action: PayloadAction<{
      type: keyof OrderState['loading'];
      value: boolean;
    }>) => {
      state.loading[action.payload.type] = action.payload.value;
    },
    
    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  addToCart,
  updateCartItemQuantity,
  removeFromCart,
  clearCart,
  applyMemberDiscount,
  recalculateCart,
  setCurrentOrder,
  addOrderToHistory,
  setLoading,
  setError,
} = orderSlice.actions;

// 选择器
export const selectCartItems = (state: any) => state.order.cart.items;
export const selectCartTotal = (state: any) => ({
  subtotal: state.order.cart.subtotal,
  discount: state.order.cart.discount,
  total: state.order.cart.totalAmount,
});
export const selectCurrentOrder = (state: any) => state.order.currentOrder;
export const selectOrderHistory = (state: any) => state.order.orderHistory;
export const selectOrderLoading = (state: any) => state.order.loading;

export default orderSlice.reducer; 