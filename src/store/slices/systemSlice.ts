import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 系统状态接口
export interface SystemState {
  // 应用设置
  app: {
    theme: 'light' | 'dark' | 'high-contrast';
    fontSize: 'normal' | 'large' | 'extra-large';
    language: 'zh-CN' | 'en-US';
    soundEnabled: boolean;
    vibrationEnabled: boolean;
  };
  
  // 网络状态
  network: {
    isOnline: boolean;
    latency: number | null;
    lastCheck: string | null;
  };
  
  // 设备信息
  device: {
    platform: 'electron' | 'capacitor' | 'web';
    os: string;
    version: string;
    screenWidth: number;
    screenHeight: number;
  };
  
  // 收银员信息
  cashier: {
    id: string | null;
    name: string | null;
    workShift: 'morning' | 'afternoon' | 'evening' | 'night' | null;
    startTime: string | null;
  };
  
  // 系统状态
  status: {
    isInitialized: boolean;
    isLoading: boolean;
    maintenanceMode: boolean;
    lastUpdate: string | null;
  };
  
  // 通知
  notifications: Array<{
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    timestamp: string;
    read: boolean;
  }>;
  
  // 错误信息
  error: string | null;
}

// 初始状态
const initialState: SystemState = {
  app: {
    theme: 'light',
    fontSize: 'normal',
    language: 'zh-CN',
    soundEnabled: true,
    vibrationEnabled: false,
  },
  network: {
    isOnline: navigator.onLine || true,
    latency: null,
    lastCheck: null,
  },
  device: {
    platform: 'web',
    os: navigator.platform || 'Unknown',
    version: '1.0.0',
    screenWidth: window.innerWidth || 1024,
    screenHeight: window.innerHeight || 768,
  },
  cashier: {
    id: null,
    name: null,
    workShift: null,
    startTime: null,
  },
  status: {
    isInitialized: false,
    isLoading: false,
    maintenanceMode: false,
    lastUpdate: null,
  },
  notifications: [],
  error: null,
};

// 系统切片
const systemSlice = createSlice({
  name: 'system',
  initialState,
  reducers: {
    // 应用设置
    setTheme: (state, action: PayloadAction<SystemState['app']['theme']>) => {
      state.app.theme = action.payload;
    },
    
    setFontSize: (state, action: PayloadAction<SystemState['app']['fontSize']>) => {
      state.app.fontSize = action.payload;
    },
    
    setLanguage: (state, action: PayloadAction<SystemState['app']['language']>) => {
      state.app.language = action.payload;
    },
    
    setSoundEnabled: (state, action: PayloadAction<boolean>) => {
      state.app.soundEnabled = action.payload;
    },
    
    setVibrationEnabled: (state, action: PayloadAction<boolean>) => {
      state.app.vibrationEnabled = action.payload;
    },
    
    // 网络状态
    setNetworkStatus: (state, action: PayloadAction<{
      isOnline: boolean;
      latency?: number;
    }>) => {
      state.network.isOnline = action.payload.isOnline;
      if (action.payload.latency !== undefined) {
        state.network.latency = action.payload.latency;
      }
      state.network.lastCheck = new Date().toISOString();
    },
    
    // 设备信息
    updateDeviceInfo: (state, action: PayloadAction<Partial<SystemState['device']>>) => {
      state.device = { ...state.device, ...action.payload };
    },
    
    setScreenSize: (state, action: PayloadAction<{
      width: number;
      height: number;
    }>) => {
      state.device.screenWidth = action.payload.width;
      state.device.screenHeight = action.payload.height;
    },
    
    // 收银员信息
    setCashier: (state, action: PayloadAction<{
      id: string;
      name: string;
      workShift: SystemState['cashier']['workShift'];
    }>) => {
      state.cashier.id = action.payload.id;
      state.cashier.name = action.payload.name;
      state.cashier.workShift = action.payload.workShift;
      state.cashier.startTime = new Date().toISOString();
    },
    
    clearCashier: (state) => {
      state.cashier = {
        id: null,
        name: null,
        workShift: null,
        startTime: null,
      };
    },
    
    // 系统状态
    setInitialized: (state, action: PayloadAction<boolean>) => {
      state.status.isInitialized = action.payload;
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.status.isLoading = action.payload;
    },
    
    setMaintenanceMode: (state, action: PayloadAction<boolean>) => {
      state.status.maintenanceMode = action.payload;
    },
    
    updateLastUpdate: (state) => {
      state.status.lastUpdate = new Date().toISOString();
    },
    
    // 通知管理
    addNotification: (state, action: PayloadAction<Omit<SystemState['notifications'][0], 'id' | 'timestamp' | 'read'>>) => {
      const notification = {
        id: `notif_${Date.now()}`,
        ...action.payload,
        timestamp: new Date().toISOString(),
        read: false,
      };
      state.notifications.unshift(notification);
      
      // 限制通知数量
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50);
      }
    },
    
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },
    
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    
    clearNotifications: (state) => {
      state.notifications = [];
    },
    
    markAllNotificationsAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.read = true;
      });
    },
    
    // 错误管理
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setTheme,
  setFontSize,
  setLanguage,
  setSoundEnabled,
  setVibrationEnabled,
  setNetworkStatus,
  updateDeviceInfo,
  setScreenSize,
  setCashier,
  clearCashier,
  setInitialized,
  setLoading,
  setMaintenanceMode,
  updateLastUpdate,
  addNotification,
  markNotificationAsRead,
  removeNotification,
  clearNotifications,
  markAllNotificationsAsRead,
  setError,
} = systemSlice.actions;

export default systemSlice.reducer; 