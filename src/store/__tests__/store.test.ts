import { store } from '../index';

describe('Redux Store', () => {
  test('应该正确初始化store', () => {
    const state = store.getState();
    
    expect(state).toBeDefined();
    expect(state).toHaveProperty('auth');
    expect(state).toHaveProperty('order');
    expect(state).toHaveProperty('member');
    expect(state).toHaveProperty('ai');
    expect(state).toHaveProperty('system');
    expect(state).toHaveProperty('product');
  });

  test('auth slice应该有正确的初始状态', () => {
    const state = store.getState();
    
    expect(state.auth).toEqual({
      user: null,
      isAuthenticated: false,
      token: undefined,
      loading: false,
      error: null,
    });
  });

  test('order slice应该有正确的初始状态', () => {
    const state = store.getState();
    
    expect(state.order).toHaveProperty('currentOrder');
    expect(state.order).toHaveProperty('orderHistory');
    expect(state.order).toHaveProperty('loading');
    expect(state.order).toHaveProperty('error');
    expect(state.order).toHaveProperty('cart');
    
    expect(state.order.currentOrder).toBeNull();
    expect(Array.isArray(state.order.orderHistory)).toBe(true);
    expect(state.order.error).toBeNull();
  });

  test('ai slice应该有正确的初始状态', () => {
    const state = store.getState();
    
    expect(state.ai).toHaveProperty('camera');
    expect(state.ai).toHaveProperty('model');
    expect(state.ai).toHaveProperty('recognition');
    expect(state.ai).toHaveProperty('results');
    expect(state.ai).toHaveProperty('settings');
    expect(state.ai).toHaveProperty('error');
    
    expect(state.ai.error).toBeNull();
  });

  test('member slice应该有正确的初始状态', () => {
    const state = store.getState();
    
    expect(state.member).toHaveProperty('currentMember');
    expect(state.member).toHaveProperty('searchResults');
    expect(state.member).toHaveProperty('loading');
    expect(state.member).toHaveProperty('error');
    expect(state.member).toHaveProperty('memberList');
    
    expect(state.member.currentMember).toBeNull();
    expect(Array.isArray(state.member.searchResults)).toBe(true);
    expect(Array.isArray(state.member.memberList)).toBe(true);
    expect(state.member.error).toBeNull();
  });

  test('store应该支持dispatch action', () => {
    // 这是一个基础测试，确保store能够处理action
    expect(() => {
      store.dispatch({ type: '@@INIT' });
    }).not.toThrow();
  });

  test('store应该包含API reducer', () => {
    const state = store.getState();
    
    expect(state).toHaveProperty('api');
  });
}); 