import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout } from 'antd';

// 导入所有CSS文件
import './App.css';
import './index.css';
import './styles/elder.less';
import './pages/CashierPage.less';
import './components/sections/AISection.less';
import './components/sections/OrderSection.less';
import './components/sections/MenuSection.less';

import CashierPage from './pages/CashierPage';

const { Content } = Layout;

const App: React.FC = () => {
  return (
    <div className="App">
      <Router>
        <Layout style={{ minHeight: '100vh' }}>
          <Content>
            <Routes>
              <Route path="/" element={<CashierPage />} />
              <Route path="/cashier" element={<CashierPage />} />
            </Routes>
          </Content>
        </Layout>
      </Router>
    </div>
  );
};

export default App; 