import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import type { RootState, AppDispatch } from '../store';

// 使用类型安全的useDispatch hook
export const useAppDispatch = () => useDispatch<AppDispatch>();

// 使用类型安全的useSelector hook
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// 自定义hooks用于常用状态
export const useAuth = () => useAppSelector(state => state.auth);
export const useOrder = () => useAppSelector(state => state.order);
export const useMember = () => useAppSelector(state => state.member);
export const useAi = () => useAppSelector(state => state.ai);
export const useSystem = () => useAppSelector(state => state.system);

// 认证状态相关hooks
export const useAuthStatus = () => {
  const auth = useAuth();
  return {
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.loading,
    user: auth.user,
    error: auth.error,
  };
};

// 购物车相关hooks
export const useCart = () => {
  const order = useOrder();
  return {
    items: order.cart.items,
    subtotal: order.cart.subtotal,
    discount: order.cart.discount,
    totalAmount: order.cart.totalAmount,
    memberId: order.cart.memberId,
    itemCount: order.cart.items.length,
    totalQuantity: order.cart.items.reduce((sum, item) => sum + item.quantity, 0),
  };
};

// 当前会员相关hooks
export const useCurrentMember = () => {
  const member = useMember();
  return {
    currentMember: member.currentMember,
    isLoading: member.loading.searching,
    error: member.error,
  };
};

// AI识别状态hooks
export const useAiRecognition = () => {
  const ai = useAi();
  return {
    isRecognizing: ai.recognition.isRecognizing,
    autoMode: ai.recognition.autoMode,
    currentResult: ai.results.current,
    history: ai.results.history,
    dailyCount: ai.results.dailyCount,
    isModelLoaded: ai.model.isLoaded,
    isModelLoading: ai.model.isLoading,
    modelError: ai.model.error,
  };
};

// 摄像头状态hooks
export const useCamera = () => {
  const ai = useAi();
  return {
    isActive: ai.camera.isActive,
    isAvailable: ai.camera.isAvailable,
    deviceId: ai.camera.deviceId,
    stream: ai.camera.stream,
  };
};

// 系统状态hooks
export const useSystemStatus = () => {
  const system = useSystem();
  return {
    isInitialized: system.status.isInitialized,
    isLoading: system.status.isLoading,
    isOnline: system.network.isOnline,
    latency: system.network.latency,
    theme: system.app.theme,
    fontSize: system.app.fontSize,
    language: system.app.language,
  };
};

// 收银员信息hooks
export const useCashier = () => {
  const system = useSystem();
  return {
    id: system.cashier.id,
    name: system.cashier.name,
    workShift: system.cashier.workShift,
    startTime: system.cashier.startTime,
  };
};

// 通知hooks
export const useNotifications = () => {
  const system = useSystem();
  const unreadCount = system.notifications.filter(n => !n.read).length;
  
  return {
    notifications: system.notifications,
    unreadCount,
    hasUnread: unreadCount > 0,
  };
};

// 设备信息hooks
export const useDeviceInfo = () => {
  const system = useSystem();
  return {
    platform: system.device.platform,
    os: system.device.os,
    version: system.device.version,
    screenWidth: system.device.screenWidth,
    screenHeight: system.device.screenHeight,
    isMobile: system.device.screenWidth < 768,
    isTablet: system.device.screenWidth >= 768 && system.device.screenWidth < 1024,
    isDesktop: system.device.screenWidth >= 1024,
  };
};

// 加载状态组合hooks
export const useLoadingStates = () => {
  const auth = useAuth();
  const order = useOrder();
  const member = useMember();
  const ai = useAi();
  const system = useSystem();
  
  return {
    anyLoading: 
      auth.loading || 
      order.loading.creating || 
      order.loading.updating || 
      order.loading.fetching ||
      member.loading.searching ||
      member.loading.updating ||
      member.loading.recharging ||
      ai.recognition.isRecognizing ||
      ai.model.isLoading ||
      system.status.isLoading,
    
    authLoading: auth.loading,
    orderLoading: order.loading.creating || order.loading.updating || order.loading.fetching,
    memberLoading: member.loading.searching || member.loading.updating || member.loading.recharging,
    aiLoading: ai.recognition.isRecognizing || ai.model.isLoading,
    systemLoading: system.status.isLoading,
  };
};

// 错误状态组合hooks
export const useErrorStates = () => {
  const auth = useAuth();
  const order = useOrder();
  const member = useMember();
  const ai = useAi();
  const system = useSystem();
  
  const errors = [
    auth.error,
    order.error,
    member.error,
    ai.error,
    ai.model.error,
    system.error,
  ].filter(Boolean);
  
  return {
    hasErrors: errors.length > 0,
    errors,
    firstError: errors[0] || null,
  };
}; 