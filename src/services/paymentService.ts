export interface CashPaymentData {
  amount: number;
  received: number;
  change: number;
}

export interface QRCodePaymentData {
  method: 'wechat' | 'alipay';
  amount: number;
  orderId: string;
}

export interface CardPaymentData {
  memberId: string;
  amount: number;
}

export interface PaymentResult {
  transactionId: string;
  success: boolean;
  amount: number;
  method: string;
  timestamp: string;
}

export interface QRCodeResult {
  qrCodeUrl: string;
  transactionId: string;
  expiresAt: string;
}

export interface PaymentStatus {
  transactionId: string;
  paid: boolean;
  failed: boolean;
  amount?: number;
  timestamp?: string;
}

/**
 * 支付处理服务类
 * 
 * 功能特性：
 * - 现金支付处理
 * - 二维码支付（微信、支付宝）
 * - 储值卡支付
 * - 支付状态查询
 * - 支付记录管理
 * - 退款处理
 */
class PaymentService {
  private readonly API_BASE = '/api/payments';
  
  /**
   * 处理现金支付
   */
  async processCashPayment(data: CashPaymentData): Promise<PaymentResult> {
    try {
      // 验证金额
      if (data.received < data.amount) {
        throw new Error('收取金额不足');
      }

      const transactionId = this.generateTransactionId('CASH');
      
      // 模拟现金支付处理
      await this.delay(1000);

      const result: PaymentResult = {
        transactionId,
        success: true,
        amount: data.amount,
        method: 'cash',
        timestamp: new Date().toISOString(),
      };

      // 记录支付记录
      await this.recordPayment(result);

      return result;
    } catch (error) {
      console.error('现金支付处理失败:', error);
      throw new Error(error instanceof Error ? error.message : '现金支付失败');
    }
  }

  /**
   * 生成二维码支付
   */
  async generateQRCode(data: QRCodePaymentData): Promise<QRCodeResult> {
    try {
      const transactionId = this.generateTransactionId(data.method.toUpperCase());
      
      // 生成二维码URL（生产环境会调用第三方SDK）
      const qrCodeUrl = this.generateMockQRCode(data);
      const expiresAt = new Date(Date.now() + 5 * 60 * 1000).toISOString(); // 5分钟过期

      return {
        qrCodeUrl,
        transactionId,
        expiresAt,
      };
    } catch (error) {
      console.error('生成二维码失败:', error);
      throw new Error('生成支付二维码失败');
    }
  }

  /**
   * 查询支付状态
   */
  async checkPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    try {
      // 模拟支付状态查询
      await this.delay(500);

      // 模拟随机支付结果（生产环境会查询第三方支付状态）
      const random = Math.random();
      
      if (random > 0.8) {
        // 20%概率支付成功
        return {
          transactionId,
          paid: true,
          failed: false,
          timestamp: new Date().toISOString(),
        };
      } else if (random < 0.1) {
        // 10%概率支付失败
        return {
          transactionId,
          paid: false,
          failed: true,
        };
      } else {
        // 70%概率支付中
        return {
          transactionId,
          paid: false,
          failed: false,
        };
      }
    } catch (error) {
      console.error('查询支付状态失败:', error);
      throw new Error('查询支付状态失败');
    }
  }

  /**
   * 处理储值卡支付
   */
  async processCardPayment(data: CardPaymentData): Promise<PaymentResult> {
    try {
      const transactionId = this.generateTransactionId('CARD');
      
      // 模拟储值卡支付处理
      await this.delay(1000);

      const result: PaymentResult = {
        transactionId,
        success: true,
        amount: data.amount,
        method: 'card',
        timestamp: new Date().toISOString(),
      };

      // 记录支付记录
      await this.recordPayment(result);

      return result;
    } catch (error) {
      console.error('储值卡支付处理失败:', error);
      throw new Error('储值卡支付失败');
    }
  }

  /**
   * 处理退款
   */
  async processRefund(transactionId: string, amount: number, reason: string = '客户退款'): Promise<PaymentResult> {
    try {
      const refundTransactionId = this.generateTransactionId('REFUND');
      
      // 模拟退款处理
      await this.delay(1500);

      const result: PaymentResult = {
        transactionId: refundTransactionId,
        success: true,
        amount: -amount, // 负数表示退款
        method: 'refund',
        timestamp: new Date().toISOString(),
      };

      // 记录退款记录
      await this.recordPayment(result);

      return result;
    } catch (error) {
      console.error('退款处理失败:', error);
      throw new Error('退款处理失败');
    }
  }

  /**
   * 获取支付记录
   */
  async getPaymentHistory(params: {
    startDate?: string;
    endDate?: string;
    method?: string;
    page?: number;
    pageSize?: number;
  } = {}): Promise<{
    payments: PaymentResult[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const { page = 1, pageSize = 20 } = params;

      // 模拟支付记录查询
      const mockPayments: PaymentResult[] = [
        {
          transactionId: 'CASH20240110001',
          success: true,
          amount: 58.50,
          method: 'cash',
          timestamp: '2024-01-10T09:30:00.000Z',
        },
        {
          transactionId: 'WECHAT20240110002',
          success: true,
          amount: 32.00,
          method: 'wechat',
          timestamp: '2024-01-10T10:15:00.000Z',
        },
        {
          transactionId: 'ALIPAY20240110003',
          success: true,
          amount: 76.80,
          method: 'alipay',
          timestamp: '2024-01-10T11:22:00.000Z',
        },
        {
          transactionId: 'CARD20240110004',
          success: true,
          amount: 45.00,
          method: 'card',
          timestamp: '2024-01-10T14:08:00.000Z',
        },
      ];

      let filteredPayments = mockPayments;

      // 筛选
      if (params.method) {
        filteredPayments = filteredPayments.filter(p => p.method === params.method);
      }

      if (params.startDate) {
        filteredPayments = filteredPayments.filter(p => 
          new Date(p.timestamp) >= new Date(params.startDate!)
        );
      }

      if (params.endDate) {
        filteredPayments = filteredPayments.filter(p => 
          new Date(p.timestamp) <= new Date(params.endDate!)
        );
      }

      // 分页
      const total = filteredPayments.length;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      const pagedPayments = filteredPayments.slice(start, end);

      return {
        payments: pagedPayments,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      console.error('获取支付记录失败:', error);
      throw new Error('获取支付记录失败');
    }
  }

  /**
   * 获取支付统计
   */
  async getPaymentStatistics(params: {
    startDate?: string;
    endDate?: string;
  } = {}): Promise<{
    totalAmount: number;
    totalCount: number;
    methodBreakdown: Record<string, { amount: number; count: number }>;
    hourlyBreakdown: Record<string, number>;
  }> {
    try {
      // 模拟支付统计数据
      const stats = {
        totalAmount: 2156.80,
        totalCount: 45,
        methodBreakdown: {
          cash: { amount: 856.50, count: 18 },
          wechat: { amount: 634.20, count: 12 },
          alipay: { amount: 456.10, count: 10 },
          card: { amount: 210.00, count: 5 },
        },
        hourlyBreakdown: {
          '09': 125.60,
          '10': 234.80,
          '11': 345.20,
          '12': 456.30,
          '13': 234.50,
          '14': 345.80,
          '15': 234.60,
          '16': 180.00,
        },
      };

      return stats;
    } catch (error) {
      console.error('获取支付统计失败:', error);
      throw new Error('获取支付统计失败');
    }
  }

  /**
   * 验证支付金额
   */
  validatePaymentAmount(amount: number): boolean {
    return amount > 0 && amount <= 99999.99;
  }

  /**
   * 生成交易ID
   */
  private generateTransactionId(prefix: string): string {
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
    const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
    const random = Math.random().toString(36).substr(2, 6).toUpperCase();
    return `${prefix}${dateStr}${timeStr}${random}`;
  }

  /**
   * 生成模拟二维码
   */
  private generateMockQRCode(data: QRCodePaymentData): string {
    // 生产环境会调用微信/支付宝SDK生成真实二维码
    const baseUrl = data.method === 'wechat' 
      ? 'wxp://f2f0' 
      : 'https://qr.alipay.com/';
    
    const params = new URLSearchParams({
      amount: data.amount.toString(),
      order: data.orderId,
      timestamp: Date.now().toString(),
    });

    return `${baseUrl}${btoa(params.toString())}`;
  }

  /**
   * 记录支付记录
   */
  private async recordPayment(payment: PaymentResult): Promise<void> {
    try {
      // 生产环境会保存到数据库
      console.log('支付记录已保存:', payment);
    } catch (error) {
      console.error('保存支付记录失败:', error);
    }
  }

  /**
   * 延迟函数（模拟网络请求）
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 导出单例实例
export const paymentService = new PaymentService(); 