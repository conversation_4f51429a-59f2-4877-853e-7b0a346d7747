// 系统设置接口
export interface SystemSettings {
  // 基本设置
  storeName: string;
  storeAddress: string;
  storePhone: string;
  businessHours: {
    open: string;
    close: string;
  };
  
  // 收银设置
  cashierSettings: {
    autoLogout: boolean;
    autoLogoutTime: number; // 分钟
    soundEnabled: boolean;
    printEnabled: boolean;
    receiptTemplate: string;
  };

  // 适老化设置
  elderlyMode: {
    enabled: boolean;
    fontSize: 'normal' | 'large' | 'extra-large';
    highContrast: boolean;
    simpleLayout: boolean;
    voicePrompt: boolean;
  };

  // AI设置
  aiSettings: {
    enabled: boolean;
    confidence: number; // 0-1
    autoAdd: boolean;
    modelPath: string;
  };

  // 支付设置
  paymentSettings: {
    enabledMethods: string[];
    wechatConfig?: {
      appId: string;
      merchantId: string;
    };
    alipayConfig?: {
      appId: string;
      merchantId: string;
    };
  };

  // 会员设置
  memberSettings: {
    pointsEnabled: boolean;
    pointsRatio: number; // 消费1元获得多少积分
    levelUpRules: {
      level: string;
      requiredPoints: number;
      discount: number;
    }[];
  };

  // 其他设置
  otherSettings: {
    dataBackupEnabled: boolean;
    backupFrequency: 'daily' | 'weekly' | 'monthly';
    maxOrderHistory: number; // 天数
    debugMode: boolean;
  };

  updatedAt: string;
  updatedBy: string;
}

// 操作日志接口
export interface OperationLog {
  id: string;
  userId: string;
  username: string;
  action: string;
  module: string;
  description: string;
  ip?: string;
  userAgent?: string;
  requestData?: any;
  responseData?: any;
  success: boolean;
  errorMessage?: string;
  duration?: number; // 毫秒
  timestamp: string;
}

// 系统状态监控接口
export interface SystemStatus {
  // 系统信息
  system: {
    platform: string;
    version: string;
    uptime: number; // 秒
    memoryUsage: {
      used: number;
      total: number;
      percentage: number;
    };
    diskUsage: {
      used: number;
      total: number;
      percentage: number;
    };
  };

  // 应用状态
  application: {
    version: string;
    environment: 'development' | 'production';
    lastRestart: string;
    errorCount: number;
    activeUsers: number;
  };

  // 数据库状态
  database: {
    connected: boolean;
    responseTime: number; // 毫秒
    lastBackup?: string;
    recordCount: {
      orders: number;
      members: number;
      products: number;
    };
  };

  // 外部服务状态
  externalServices: {
    paymentGateway: boolean;
    aiService: boolean;
    printerService: boolean;
  };

  timestamp: string;
}

// 设备信息接口
export interface DeviceInfo {
  id: string;
  name: string;
  type: 'pos' | 'printer' | 'scanner' | 'camera' | 'display';
  status: 'online' | 'offline' | 'error';
  ip?: string;
  model?: string;
  version?: string;
  lastHeartbeat?: string;
  errorMessage?: string;
  settings?: Record<string, any>;
}

/**
 * 系统管理服务类
 * 
 * 功能特性：
 * - 系统参数配置管理
 * - 操作日志记录和查询
 * - 系统状态监控
 * - 设备管理
 * - 数据备份和恢复
 */
class SystemService {
  private readonly API_BASE = '/api/system';
  private readonly STORAGE_KEY = 'cashier_system';

  /**
   * 获取系统设置
   */
  async getSystemSettings(): Promise<SystemSettings> {
    try {
      // 开发环境：从本地存储获取或返回默认设置
      const stored = localStorage.getItem(this.STORAGE_KEY + '_settings');
      if (stored) {
        return JSON.parse(stored);
      }

      // 默认设置
      const defaultSettings: SystemSettings = {
        storeName: '助老餐厅',
        storeAddress: '北京市朝阳区示例街道123号',
        storePhone: '010-12345678',
        businessHours: {
          open: '08:00',
          close: '22:00',
        },
        cashierSettings: {
          autoLogout: true,
          autoLogoutTime: 30,
          soundEnabled: true,
          printEnabled: true,
          receiptTemplate: 'default',
        },
        elderlyMode: {
          enabled: true,
          fontSize: 'large',
          highContrast: false,
          simpleLayout: true,
          voicePrompt: true,
        },
        aiSettings: {
          enabled: true,
          confidence: 0.8,
          autoAdd: true,
          modelPath: '/models/food-recognition.json',
        },
        paymentSettings: {
          enabledMethods: ['cash', 'wechat', 'alipay', 'card'],
          wechatConfig: {
            appId: 'wx_demo_app_id',
            merchantId: 'wx_demo_merchant_id',
          },
          alipayConfig: {
            appId: 'alipay_demo_app_id',
            merchantId: 'alipay_demo_merchant_id',
          },
        },
        memberSettings: {
          pointsEnabled: true,
          pointsRatio: 1,
          levelUpRules: [
            { level: '铜牌会员', requiredPoints: 0, discount: 0.95 },
            { level: '银牌会员', requiredPoints: 500, discount: 0.9 },
            { level: '金牌会员', requiredPoints: 1000, discount: 0.85 },
            { level: 'VIP会员', requiredPoints: 2000, discount: 0.8 },
          ],
        },
        otherSettings: {
          dataBackupEnabled: true,
          backupFrequency: 'daily',
          maxOrderHistory: 90,
          debugMode: false,
        },
        updatedAt: new Date().toISOString(),
        updatedBy: 'system',
      };

      // 保存默认设置
      localStorage.setItem(this.STORAGE_KEY + '_settings', JSON.stringify(defaultSettings));
      return defaultSettings;
    } catch (error) {
      console.error('获取系统设置失败:', error);
      throw new Error('获取系统设置失败');
    }
  }

  /**
   * 更新系统设置
   */
  async updateSystemSettings(settings: Partial<SystemSettings>): Promise<SystemSettings> {
    try {
      const currentSettings = await this.getSystemSettings();
      
      const updatedSettings: SystemSettings = {
        ...currentSettings,
        ...settings,
        updatedAt: new Date().toISOString(),
        updatedBy: 'current_user', // TODO: 从当前用户获取
      };

      // 保存到本地存储
      localStorage.setItem(this.STORAGE_KEY + '_settings', JSON.stringify(updatedSettings));

      // 记录操作日志
      await this.recordOperation({
        action: 'UPDATE_SETTINGS',
        module: 'system',
        description: '更新系统设置',
        requestData: settings,
        success: true,
      });

      return updatedSettings;
    } catch (error) {
      console.error('更新系统设置失败:', error);
      throw new Error('更新系统设置失败');
    }
  }

  /**
   * 记录操作日志
   */
  async recordOperation(data: {
    action: string;
    module: string;
    description: string;
    requestData?: any;
    responseData?: any;
    success: boolean;
    errorMessage?: string;
    duration?: number;
  }): Promise<void> {
    try {
      const logs = this.getStoredLogs();
      
      const log: OperationLog = {
        id: `log_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
        userId: 'current_user', // TODO: 从当前用户获取
        username: '当前用户', // TODO: 从当前用户获取
        action: data.action,
        module: data.module,
        description: data.description,
        ip: '127.0.0.1', // 开发环境默认IP
        userAgent: navigator.userAgent,
        requestData: data.requestData,
        responseData: data.responseData,
        success: data.success,
        errorMessage: data.errorMessage,
        duration: data.duration,
        timestamp: new Date().toISOString(),
      };

      logs.unshift(log);
      
      // 只保留最近1000条日志
      if (logs.length > 1000) {
        logs.splice(1000);
      }

      localStorage.setItem(this.STORAGE_KEY + '_logs', JSON.stringify(logs));
    } catch (error) {
      console.error('记录操作日志失败:', error);
    }
  }

  /**
   * 获取操作日志
   */
  async getOperationLogs(params: {
    page?: number;
    pageSize?: number;
    userId?: string;
    module?: string;
    action?: string;
    startDate?: string;
    endDate?: string;
    success?: boolean;
  } = {}): Promise<{
    logs: OperationLog[];
    total: number;
  }> {
    try {
      const {
        page = 1,
        pageSize = 20,
        userId,
        module,
        action,
        startDate,
        endDate,
        success,
      } = params;

      let logs = this.getStoredLogs();

      // 应用筛选
      if (userId) {
        logs = logs.filter(log => log.userId === userId);
      }
      if (module) {
        logs = logs.filter(log => log.module === module);
      }
      if (action) {
        logs = logs.filter(log => log.action === action);
      }
      if (startDate) {
        logs = logs.filter(log => log.timestamp >= startDate);
      }
      if (endDate) {
        logs = logs.filter(log => log.timestamp <= endDate);
      }
      if (success !== undefined) {
        logs = logs.filter(log => log.success === success);
      }

      // 分页
      const total = logs.length;
      const start = (page - 1) * pageSize;
      const paginatedLogs = logs.slice(start, start + pageSize);

      return {
        logs: paginatedLogs,
        total,
      };
    } catch (error) {
      console.error('获取操作日志失败:', error);
      return { logs: [], total: 0 };
    }
  }

  /**
   * 获取系统状态
   */
  async getSystemStatus(): Promise<SystemStatus> {
    try {
      // 开发环境：生成模拟系统状态
      const status: SystemStatus = {
        system: {
          platform: navigator.platform,
          version: '1.0.0',
          uptime: Math.floor(Math.random() * 86400), // 随机运行时间
          memoryUsage: {
            used: 512 + Math.floor(Math.random() * 1024),
            total: 8192,
            percentage: 0,
          },
          diskUsage: {
            used: 25600 + Math.floor(Math.random() * 10240),
            total: 102400,
            percentage: 0,
          },
        },
        application: {
          version: '1.0.0',
          environment: 'development',
          lastRestart: new Date(Date.now() - Math.random() * 86400000).toISOString(),
          errorCount: Math.floor(Math.random() * 5),
          activeUsers: 1 + Math.floor(Math.random() * 3),
        },
        database: {
          connected: true,
          responseTime: 10 + Math.floor(Math.random() * 50),
          lastBackup: new Date(Date.now() - Math.random() * 86400000).toISOString(),
          recordCount: {
            orders: 1000 + Math.floor(Math.random() * 5000),
            members: 500 + Math.floor(Math.random() * 2000),
            products: 100 + Math.floor(Math.random() * 200),
          },
        },
        externalServices: {
          paymentGateway: true,
          aiService: true,
          printerService: Math.random() > 0.1, // 10%概率离线
        },
        timestamp: new Date().toISOString(),
      };

      // 计算百分比
      status.system.memoryUsage.percentage = 
        Math.round((status.system.memoryUsage.used / status.system.memoryUsage.total) * 100);
      status.system.diskUsage.percentage = 
        Math.round((status.system.diskUsage.used / status.system.diskUsage.total) * 100);

      return status;
    } catch (error) {
      console.error('获取系统状态失败:', error);
      throw new Error('获取系统状态失败');
    }
  }

  /**
   * 获取设备列表
   */
  async getDevices(): Promise<DeviceInfo[]> {
    try {
      // 开发环境：返回模拟设备列表
      const devices: DeviceInfo[] = [
        {
          id: 'pos_001',
          name: '收银终端1',
          type: 'pos',
          status: 'online',
          ip: '*************',
          model: 'CASH-POS-2024',
          version: '1.0.0',
          lastHeartbeat: new Date().toISOString(),
        },
        {
          id: 'printer_001',
          name: '小票打印机',
          type: 'printer',
          status: Math.random() > 0.2 ? 'online' : 'offline',
          ip: '*************',
          model: 'HP-58MM',
          version: '2.1.0',
          lastHeartbeat: new Date(Date.now() - Math.random() * 60000).toISOString(),
        },
        {
          id: 'camera_001',
          name: 'AI识别摄像头',
          type: 'camera',
          status: 'online',
          ip: '*************',
          model: 'CAM-HD-1080',
          version: '1.5.2',
          lastHeartbeat: new Date().toISOString(),
        },
        {
          id: 'display_001',
          name: '客显屏',
          type: 'display',
          status: Math.random() > 0.1 ? 'online' : 'error',
          ip: '*************',
          model: 'LED-15INCH',
          version: '1.0.0',
          lastHeartbeat: new Date(Date.now() - Math.random() * 30000).toISOString(),
        },
      ];

      return devices;
    } catch (error) {
      console.error('获取设备列表失败:', error);
      return [];
    }
  }

  /**
   * 执行数据备份
   */
  async performBackup(): Promise<{
    success: boolean;
    backupFile?: string;
    message: string;
  }> {
    try {
      // 开发环境：模拟备份过程
      const backupFile = `backup_${new Date().toISOString().split('T')[0]}_${Date.now()}.db`;
      
      // 模拟备份延迟
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 记录操作日志
      await this.recordOperation({
        action: 'DATA_BACKUP',
        module: 'system',
        description: '执行数据备份',
        responseData: { backupFile },
        success: true,
      });

      return {
        success: true,
        backupFile,
        message: '数据备份完成',
      };
    } catch (error) {
      console.error('数据备份失败:', error);
      return {
        success: false,
        message: '数据备份失败',
      };
    }
  }

  /**
   * 执行数据恢复
   */
  async performRestore(backupFile: string): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      // 开发环境：模拟恢复过程
      console.log('恢复数据文件:', backupFile);
      
      // 模拟恢复延迟
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 记录操作日志
      await this.recordOperation({
        action: 'DATA_RESTORE',
        module: 'system',
        description: '执行数据恢复',
        requestData: { backupFile },
        success: true,
      });

      return {
        success: true,
        message: '数据恢复完成',
      };
    } catch (error) {
      console.error('数据恢复失败:', error);
      return {
        success: false,
        message: '数据恢复失败',
      };
    }
  }

  /**
   * 重启系统
   */
  async restartSystem(): Promise<void> {
    try {
      // 记录操作日志
      await this.recordOperation({
        action: 'SYSTEM_RESTART',
        module: 'system',
        description: '重启系统',
        success: true,
      });

      // 开发环境：模拟重启（实际上只是刷新页面）
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('重启系统失败:', error);
      throw new Error('重启系统失败');
    }
  }

  /**
   * 私有方法 - 获取存储的日志
   */
  private getStoredLogs(): OperationLog[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY + '_logs');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }
}

// 导出单例实例
export const systemService = new SystemService(); 