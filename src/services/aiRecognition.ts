// AI菜品识别服务（注意：需要安装@tensorflow/tfjs依赖）
// import * as tf from '@tensorflow/tfjs';

// 菜品识别类型定义
export interface DishPrediction {
  id: string;
  name: string;
  confidence: number;
  price: number;
  category: string;
  description: string;
  image?: string;
}

export interface RecognitionResult {
  id: string;
  predictions: DishPrediction[];
  topPrediction: DishPrediction | null;
  confidence: number;
  timestamp: Date;
  processingTime: number;
}

// 模型配置
interface ModelConfig {
  modelUrl: string;
  labelsUrl: string;
  inputSize: number;
  confidenceThreshold: number;
  maxPredictions: number;
}

// 默认配置
const DEFAULT_CONFIG: ModelConfig = {
  modelUrl: '/models/dish-recognition/model.json',
  labelsUrl: '/models/dish-recognition/labels.json',
  inputSize: 224,
  confidenceThreshold: 0.1,
  maxPredictions: 5,
};

// 菜品数据库（模拟）
const DISH_DATABASE: { [key: string]: Omit<DishPrediction, 'confidence' | 'id'> } = {
  '宫保鸡丁': {
    name: '宫保鸡丁',
    price: 28.0,
    category: '热菜',
    description: '经典川菜，香辣可口',
    image: '/images/dishes/gongbao-chicken.jpg'
  },
  '红烧肉': {
    name: '红烧肉',
    price: 35.0,
    category: '热菜',
    description: '肥而不腻，香甜可口',
    image: '/images/dishes/hongshao-pork.jpg'
  },
  '麻婆豆腐': {
    name: '麻婆豆腐',
    price: 18.0,
    category: '热菜',
    description: '嫩滑豆腐，麻辣鲜香',
    image: '/images/dishes/mapo-tofu.jpg'
  },
  '鱼香肉丝': {
    name: '鱼香肉丝',
    price: 25.0,
    category: '热菜',
    description: '甜酸开胃，下饭神器',
    image: '/images/dishes/yuxiang-pork.jpg'
  },
  '凉拌黄瓜': {
    name: '凉拌黄瓜',
    price: 12.0,
    category: '凉菜',
    description: '清爽解腻，开胃小菜',
    image: '/images/dishes/cucumber-salad.jpg'
  },
  '口水鸡': {
    name: '口水鸡',
    price: 32.0,
    category: '凉菜',
    description: '麻辣鲜香，口感丰富',
    image: '/images/dishes/saliva-chicken.jpg'
  },
  '番茄鸡蛋汤': {
    name: '番茄鸡蛋汤',
    price: 16.0,
    category: '汤品',
    description: '酸甜开胃，营养丰富',
    image: '/images/dishes/tomato-egg-soup.jpg'
  },
  '白米饭': {
    name: '白米饭',
    price: 3.0,
    category: '主食',
    description: '优质大米，香软可口',
    image: '/images/dishes/white-rice.jpg'
  },
  '蛋炒饭': {
    name: '蛋炒饭',
    price: 18.0,
    category: '主食',
    description: '粒粒分明，营养丰富',
    image: '/images/dishes/fried-rice.jpg'
  },
};

/**
 * AI菜品识别服务类
 */
export class AIRecognitionService {
  // private model: tf.LayersModel | null = null;
  private model: any = null; // 临时类型，待安装TensorFlow.js后更正
  private labels: string[] = [];
  private config: ModelConfig;
  private isModelLoading = false;
  private isModelLoaded = false;

  constructor(config?: Partial<ModelConfig>) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * 初始化AI模型
   */
  async initializeModel(): Promise<void> {
    if (this.isModelLoaded || this.isModelLoading) {
      return;
    }

    this.isModelLoading = true;

    try {
      console.log('开始加载AI模型...');
      
      // TODO: 实际的模型加载逻辑（需要TensorFlow.js）
      // const [model, labelsResponse] = await Promise.all([
      //   tf.loadLayersModel(this.config.modelUrl),
      //   fetch(this.config.labelsUrl)
      // ]);
      // this.model = model;
      // this.labels = await labelsResponse.json();

      // 临时使用模拟模式
      await this.initializeMockModel();
      console.log('AI模型加载成功，支持类别数:', this.labels.length);
    } catch (error) {
      console.error('AI模型加载失败:', error);
      // 使用模拟模式
      await this.initializeMockModel();
    } finally {
      this.isModelLoading = false;
    }
  }

  /**
   * 初始化模拟模型（用于开发和测试）
   */
  private async initializeMockModel(): Promise<void> {
    console.log('使用模拟AI模型');
    this.labels = Object.keys(DISH_DATABASE);
    this.isModelLoaded = true;
  }

  /**
   * 菜品识别
   */
  async recognizeDish(imageSource: HTMLVideoElement | HTMLImageElement | HTMLCanvasElement): Promise<RecognitionResult> {
    const startTime = Date.now();

    if (!this.isModelLoaded) {
      await this.initializeModel();
    }

    try {
      let predictions: DishPrediction[];

      if (this.model) {
        // 真实模型推理（暂时不可用）
        predictions = await this.runMockInference();
      } else {
        // 模拟推理
        predictions = await this.runMockInference();
      }

      // 排序和过滤预测结果
      const filteredPredictions = predictions
        .filter(p => p.confidence >= this.config.confidenceThreshold)
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, this.config.maxPredictions);

      const topPrediction = filteredPredictions.length > 0 ? filteredPredictions[0] : null;
      const processingTime = Date.now() - startTime;

      const result: RecognitionResult = {
        id: `recognition_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        predictions: filteredPredictions,
        topPrediction,
        confidence: topPrediction?.confidence || 0,
        timestamp: new Date(),
        processingTime,
      };

      console.log(`识别完成，耗时 ${processingTime}ms`, result);
      return result;

    } catch (error) {
      console.error('菜品识别失败:', error);
      throw new Error(`识别失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 模拟推理（用于开发测试）
   */
  private async runMockInference(): Promise<DishPrediction[]> {
    // 模拟推理延迟
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    // 随机选择1-3个菜品
    const dishNames = Object.keys(DISH_DATABASE);
    const numResults = Math.floor(Math.random() * 3) + 1;
    const selectedDishes = dishNames
      .sort(() => Math.random() - 0.5)
      .slice(0, numResults);

    const predictions: DishPrediction[] = selectedDishes
      .map((name, index) => {
        const dishInfo = DISH_DATABASE[name];
        if (!dishInfo) {
          return null;
        }
        
        // 第一个结果有更高的置信度
        const baseConfidence = index === 0 ? 0.7 : 0.4;
        const confidence = baseConfidence + Math.random() * 0.3;

        return {
          id: `mock_dish_${index}_${Date.now()}`,
          name,
          confidence,
          price: dishInfo.price,
          category: dishInfo.category,
          description: dishInfo.description,
          image: dishInfo.image,
        };
      })
      .filter(prediction => prediction !== null && prediction !== undefined) as DishPrediction[];

    return predictions;
  }

  /**
   * 获取模型状态
   */
  getModelStatus(): { isLoaded: boolean; isLoading: boolean; supportedLabels: string[] } {
    return {
      isLoaded: this.isModelLoaded,
      isLoading: this.isModelLoading,
      supportedLabels: this.labels,
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ModelConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 释放资源
   */
  dispose(): void {
    if (this.model) {
      // TODO: 实际的模型释放逻辑
      // this.model.dispose();
      this.model = null;
    }
    this.isModelLoaded = false;
    this.isModelLoading = false;
  }
}

// 全局实例
let aiRecognitionService: AIRecognitionService | null = null;

/**
 * 获取AI识别服务实例
 */
export function getAIRecognitionService(): AIRecognitionService {
  if (!aiRecognitionService) {
    aiRecognitionService = new AIRecognitionService();
  }
  return aiRecognitionService;
}

/**
 * 清理AI识别服务
 */
export function disposeAIRecognitionService(): void {
  if (aiRecognitionService) {
    aiRecognitionService.dispose();
    aiRecognitionService = null;
  }
} 