import type { Product, Category, ProductSearchParams, ProductFilters } from '../store/slices/productSlice';

export interface ProductListResult {
  products: Product[];
  total: number;
  page: number;
  pageSize: number;
}

export interface CategoryTreeNode extends Category {
  children?: CategoryTreeNode[];
}

/**
 * 商品管理服务类
 * 
 * 功能特性：
 * - 商品信息CRUD操作
 * - 分类管理
 * - 搜索和筛选
 * - 图片管理
 * - 库存管理
 * - 推荐算法
 */
class ProductService {
  private readonly API_BASE = '/api/products';
  private readonly STORAGE_KEY = 'cashier_products';

  /**
   * 获取商品列表
   */
  async getProducts(params: ProductSearchParams = {}): Promise<ProductListResult> {
    try {
      const {
        keyword = '',
        filters = {},
        sortBy = 'popularity',
        sortOrder = 'desc',
        page = 1,
        pageSize = 20,
      } = params;

      if (process.env.NODE_ENV === 'production') {
        const queryParams = new URLSearchParams({
          keyword,
          sortBy,
          sortOrder,
          page: page.toString(),
          pageSize: pageSize.toString(),
        });

        // 添加筛选参数
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              queryParams.append(key, value.join(','));
            } else {
              queryParams.append(key, value.toString());
            }
          }
        });

        const response = await fetch(`${this.API_BASE}?${queryParams}`);
        if (response.ok) {
          return await response.json();
        }
        throw new Error('获取商品列表失败');
      }

      // 开发环境使用模拟数据
      let products = this.getMockProducts();

      // 应用筛选
      products = this.applyFilters(products, filters);

      // 应用搜索
      if (keyword) {
        products = this.searchInProducts(products, keyword);
      }

      // 应用排序
      products = this.sortProducts(products, sortBy, sortOrder);

      // 应用分页
      const total = products.length;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      const pagedProducts = products.slice(start, end);

      return {
        products: pagedProducts,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      console.error('获取商品列表失败:', error);
      throw new Error('获取商品列表失败');
    }
  }

  /**
   * 搜索商品
   */
  async searchProducts(params: ProductSearchParams): Promise<ProductListResult> {
    return this.getProducts(params);
  }

  /**
   * 根据ID获取商品详情
   */
  async getProductById(id: string): Promise<Product | null> {
    try {
      if (process.env.NODE_ENV === 'production') {
        const response = await fetch(`${this.API_BASE}/${id}`);
        if (response.ok) {
          return await response.json();
        }
        return null;
      }

      const products = this.getMockProducts();
      return products.find(product => product.id === id) || null;
    } catch (error) {
      console.error('获取商品详情失败:', error);
      throw new Error('获取商品详情失败');
    }
  }

  /**
   * 获取分类列表
   */
  async getCategories(): Promise<Category[]> {
    try {
      if (process.env.NODE_ENV === 'production') {
        const response = await fetch('/api/categories');
        if (response.ok) {
          return await response.json();
        }
        throw new Error('获取分类列表失败');
      }

      return this.getMockCategories();
    } catch (error) {
      console.error('获取分类列表失败:', error);
      throw new Error('获取分类列表失败');
    }
  }

  /**
   * 获取分类树结构
   */
  async getCategoryTree(): Promise<CategoryTreeNode[]> {
    const categories = await this.getCategories();
    return this.buildCategoryTree(categories);
  }

  /**
   * 获取推荐商品
   */
  async getRecommendedProducts(limit: number = 10): Promise<Product[]> {
    try {
      const { products } = await this.getProducts({
        filters: { isRecommended: true },
        pageSize: limit,
      });
      return products;
    } catch (error) {
      console.error('获取推荐商品失败:', error);
      throw new Error('获取推荐商品失败');
    }
  }

  /**
   * 获取热门商品
   */
  async getHotProducts(limit: number = 10): Promise<Product[]> {
    try {
      const { products } = await this.getProducts({
        filters: { isHot: true },
        pageSize: limit,
      });
      return products;
    } catch (error) {
      console.error('获取热门商品失败:', error);
      throw new Error('获取热门商品失败');
    }
  }

  /**
   * 获取新品
   */
  async getNewProducts(limit: number = 10): Promise<Product[]> {
    try {
      const { products } = await this.getProducts({
        filters: { isNew: true },
        pageSize: limit,
      });
      return products;
    } catch (error) {
      console.error('获取新品失败:', error);
      throw new Error('获取新品失败');
    }
  }

  /**
   * 更新商品库存
   */
  async updateStock(productId: string, quantity: number): Promise<Product> {
    try {
      if (process.env.NODE_ENV === 'production') {
        const response = await fetch(`${this.API_BASE}/${productId}/stock`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ quantity }),
        });
        
        if (response.ok) {
          return await response.json();
        }
        throw new Error('更新库存失败');
      }

      // 开发环境模拟
      const product = await this.getProductById(productId);
      if (!product) {
        throw new Error('商品不存在');
      }

      return {
        ...product,
        stock: quantity,
        updatedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('更新商品库存失败:', error);
      throw new Error('更新商品库存失败');
    }
  }

  /**
   * 私有方法 - 应用筛选
   */
  private applyFilters(products: Product[], filters: ProductFilters): Product[] {
    return products.filter(product => {
      // 分类筛选
      if (filters.categoryId && product.categoryId !== filters.categoryId) {
        return false;
      }

      // 价格区间筛选
      if (filters.priceRange) {
        const [minPrice, maxPrice] = filters.priceRange;
        if (product.price < minPrice || product.price > maxPrice) {
          return false;
        }
      }

      // 标签筛选
      if (filters.tags && filters.tags.length > 0) {
        const productTags = product.tags || [];
        if (!filters.tags.some(tag => productTags.includes(tag))) {
          return false;
        }
      }

      // 推荐商品筛选
      if (filters.isRecommended !== undefined && product.isRecommended !== filters.isRecommended) {
        return false;
      }

      // 热门商品筛选
      if (filters.isHot !== undefined && product.isHot !== filters.isHot) {
        return false;
      }

      // 新品筛选
      if (filters.isNew !== undefined && product.isNew !== filters.isNew) {
        return false;
      }

      // 库存筛选
      if (filters.inStock !== undefined) {
        const inStock = product.stock > 0;
        if (inStock !== filters.inStock) {
          return false;
        }
      }

      // 辣度筛选
      if (filters.spicyLevel && filters.spicyLevel.length > 0) {
        if (!product.spicyLevel || !filters.spicyLevel.includes(product.spicyLevel)) {
          return false;
        }
      }

      // 评分筛选
      if (filters.rating && product.rating && product.rating < filters.rating) {
        return false;
      }

      return true;
    });
  }

  /**
   * 私有方法 - 搜索商品
   */
  private searchInProducts(products: Product[], keyword: string): Product[] {
    const searchTerm = keyword.toLowerCase();
    
    return products.filter(product => {
      // 商品名称匹配
      if (product.name.toLowerCase().includes(searchTerm)) {
        return true;
      }

      // 商品描述匹配
      if (product.description.toLowerCase().includes(searchTerm)) {
        return true;
      }

      // 分类名称匹配
      if (product.categoryName.toLowerCase().includes(searchTerm)) {
        return true;
      }

      // 标签匹配
      if (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchTerm))) {
        return true;
      }

      return false;
    });
  }

  /**
   * 私有方法 - 排序商品
   */
  private sortProducts(
    products: Product[], 
    sortBy: ProductSearchParams['sortBy'], 
    sortOrder: ProductSearchParams['sortOrder']
  ): Product[] {
    const sortedProducts = [...products];
    
    sortedProducts.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'price':
          comparison = a.price - b.price;
          break;
        case 'rating':
          comparison = (a.rating || 0) - (b.rating || 0);
          break;
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'createdAt':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'popularity':
          // 按推荐、热门、评分综合排序
          {
            const aScore = (a.isRecommended ? 10 : 0) + (a.isHot ? 5 : 0) + (a.rating || 0);
            const bScore = (b.isRecommended ? 10 : 0) + (b.isHot ? 5 : 0) + (b.rating || 0);
            comparison = aScore - bScore;
          }
          break;
        default:
          comparison = 0;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return sortedProducts;
  }

  /**
   * 私有方法 - 构建分类树
   */
  private buildCategoryTree(categories: Category[]): CategoryTreeNode[] {
    const categoryMap = new Map<string, CategoryTreeNode>();
    const rootCategories: CategoryTreeNode[] = [];

    // 创建分类映射
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // 构建树结构
    categories.forEach(category => {
      const node = categoryMap.get(category.id)!;
      
      if (category.parentId && categoryMap.has(category.parentId)) {
        const parent = categoryMap.get(category.parentId)!;
        parent.children!.push(node);
      } else {
        rootCategories.push(node);
      }
    });

    // 按order字段排序
    const sortByOrder = (nodes: CategoryTreeNode[]) => {
      nodes.sort((a, b) => a.order - b.order);
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          sortByOrder(node.children);
        }
      });
    };

    sortByOrder(rootCategories);
    return rootCategories;
  }

  /**
   * 私有方法 - 获取模拟商品数据
   */
  private getMockProducts(): Product[] {
    return [
      {
        id: 'product_001',
        name: '宫保鸡丁',
        description: '经典川菜，嫩滑鸡肉配花生米，酸甜香辣',
        price: 32.00,
        originalPrice: 38.00,
        categoryId: 'category_hot',
        categoryName: '热菜',
        image: '/images/dishes/gongbao-chicken.jpg',
        images: [
          '/images/dishes/gongbao-chicken-1.jpg',
          '/images/dishes/gongbao-chicken-2.jpg',
        ],
        unit: '份',
        stock: 50,
        specifications: '约300g，微辣',
        tags: ['川菜', '下饭菜', '经典'],
        isRecommended: true,
        isHot: true,
        isNew: false,
        discount: 0.16,
        nutrition: {
          calories: 280,
          protein: 22,
          fat: 18,
          carbs: 12,
        },
        allergens: ['花生', '大豆'],
        rating: 4.8,
        reviewCount: 156,
        preparationTime: 15,
        spicyLevel: 2,
        createdAt: '2023-06-15T10:30:00.000Z',
        updatedAt: '2024-01-10T14:20:00.000Z',
      },
      {
        id: 'product_002',
        name: '红烧肉',
        description: '肥瘦相间，软糯香甜，传统家常菜',
        price: 45.00,
        categoryId: 'category_hot',
        categoryName: '热菜',
        image: '/images/dishes/braised-pork.jpg',
        unit: '份',
        stock: 30,
        specifications: '约400g',
        tags: ['家常菜', '下饭菜'],
        isRecommended: true,
        isHot: false,
        isNew: false,
        nutrition: {
          calories: 420,
          protein: 25,
          fat: 32,
          carbs: 8,
        },
        rating: 4.6,
        reviewCount: 89,
        preparationTime: 25,
        spicyLevel: 0,
        createdAt: '2023-08-20T09:15:00.000Z',
        updatedAt: '2024-01-08T16:45:00.000Z',
      },
      {
        id: 'product_003',
        name: '麻婆豆腐',
        description: '嫩滑豆腐配香辣肉末，经典川菜',
        price: 28.00,
        categoryId: 'category_hot',
        categoryName: '热菜',
        image: '/images/dishes/mapo-tofu.jpg',
        unit: '份',
        stock: 45,
        specifications: '约350g，中辣',
        tags: ['川菜', '豆制品', '下饭菜'],
        isRecommended: false,
        isHot: true,
        isNew: false,
        nutrition: {
          calories: 180,
          protein: 12,
          fat: 14,
          carbs: 6,
        },
        rating: 4.5,
        reviewCount: 78,
        preparationTime: 12,
        spicyLevel: 3,
        createdAt: '2023-09-10T11:20:00.000Z',
        updatedAt: '2024-01-05T13:30:00.000Z',
      },
      {
        id: 'product_004',
        name: '酸辣土豆丝',
        description: '爽脆土豆丝，酸辣开胃',
        price: 18.00,
        categoryId: 'category_vegetable',
        categoryName: '素菜',
        image: '/images/dishes/potato-strips.jpg',
        unit: '份',
        stock: 60,
        specifications: '约250g，微辣',
        tags: ['素菜', '开胃菜', '爽脆'],
        isRecommended: false,
        isHot: false,
        isNew: true,
        nutrition: {
          calories: 120,
          protein: 3,
          fat: 8,
          carbs: 22,
        },
        rating: 4.3,
        reviewCount: 45,
        preparationTime: 8,
        spicyLevel: 1,
        createdAt: '2024-01-01T08:00:00.000Z',
        updatedAt: '2024-01-10T10:15:00.000Z',
      },
      {
        id: 'product_005',
        name: '蒸蛋羹',
        description: '嫩滑如丝的蒸蛋，老少皆宜',
        price: 15.00,
        categoryId: 'category_soup',
        categoryName: '汤羹',
        image: '/images/dishes/steamed-egg.jpg',
        unit: '份',
        stock: 40,
        specifications: '约200g',
        tags: ['蒸菜', '养生', '嫩滑'],
        isRecommended: true,
        isHot: false,
        isNew: false,
        nutrition: {
          calories: 90,
          protein: 8,
          fat: 6,
          carbs: 2,
        },
        rating: 4.7,
        reviewCount: 92,
        preparationTime: 10,
        spicyLevel: 0,
        createdAt: '2023-07-25T14:30:00.000Z',
        updatedAt: '2024-01-09T09:20:00.000Z',
      },
      {
        id: 'product_006',
        name: '小笼包',
        description: '皮薄馅大，汤汁丰富的传统小笼包',
        price: 25.00,
        categoryId: 'category_staple',
        categoryName: '主食',
        image: '/images/dishes/xiaolongbao.jpg',
        unit: '笼(8个)',
        stock: 35,
        specifications: '8个装，约150g',
        tags: ['包子', '传统小吃', '汤包'],
        isRecommended: true,
        isHot: false,
        isNew: true,
        nutrition: {
          calories: 220,
          protein: 12,
          fat: 8,
          carbs: 28,
        },
        rating: 4.9,
        reviewCount: 203,
        preparationTime: 5,
        spicyLevel: 0,
        createdAt: '2024-01-05T07:45:00.000Z',
        updatedAt: '2024-01-10T12:00:00.000Z',
      },
    ];
  }

  /**
   * 私有方法 - 获取模拟分类数据
   */
  private getMockCategories(): Category[] {
    return [
      {
        id: 'category_hot',
        name: '热菜',
        icon: '🔥',
        image: '/images/categories/hot-dishes.jpg',
        description: '各种热菜炒菜',
        order: 1,
        isActive: true,
        productCount: 25,
        createdAt: '2023-06-01T00:00:00.000Z',
      },
      {
        id: 'category_vegetable',
        name: '素菜',
        icon: '🥬',
        image: '/images/categories/vegetables.jpg',
        description: '清爽素食菜品',
        order: 2,
        isActive: true,
        productCount: 12,
        createdAt: '2023-06-01T00:00:00.000Z',
      },
      {
        id: 'category_soup',
        name: '汤羹',
        icon: '🍲',
        image: '/images/categories/soups.jpg',
        description: '各式汤品羹类',
        order: 3,
        isActive: true,
        productCount: 8,
        createdAt: '2023-06-01T00:00:00.000Z',
      },
      {
        id: 'category_staple',
        name: '主食',
        icon: '🍚',
        image: '/images/categories/staples.jpg',
        description: '米饭面条包子等主食',
        order: 4,
        isActive: true,
        productCount: 15,
        createdAt: '2023-06-01T00:00:00.000Z',
      },
      {
        id: 'category_cold',
        name: '凉菜',
        icon: '🥗',
        image: '/images/categories/cold-dishes.jpg',
        description: '清爽凉拌菜品',
        order: 5,
        isActive: true,
        productCount: 10,
        createdAt: '2023-06-01T00:00:00.000Z',
      },
      {
        id: 'category_dessert',
        name: '甜品',
        icon: '🍮',
        image: '/images/categories/desserts.jpg',
        description: '各种甜品点心',
        order: 6,
        isActive: true,
        productCount: 8,
        createdAt: '2023-06-01T00:00:00.000Z',
      },
    ];
  }
}

// 导出单例实例
export const productService = new ProductService(); 