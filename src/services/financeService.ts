// 财务数据接口
export interface FinancialData {
  date: string;
  totalRevenue: number;
  totalOrders: number;
  avgOrderValue: number;
  refundAmount: number;
  netRevenue: number;
  paymentMethodBreakdown: PaymentMethodBreakdown;
  hourlyBreakdown: HourlyBreakdown[];
  categoryBreakdown: CategoryBreakdown[];
}

// 支付方式分解
export interface PaymentMethodBreakdown {
  cash: { amount: number; count: number };
  wechat: { amount: number; count: number };
  alipay: { amount: number; count: number };
  card: { amount: number; count: number };
}

// 小时销售分解
export interface HourlyBreakdown {
  hour: number;
  revenue: number;
  orderCount: number;
}

// 分类销售分解
export interface CategoryBreakdown {
  categoryId: string;
  categoryName: string;
  revenue: number;
  orderCount: number;
  itemCount: number;
}

// 财务报表参数
export interface FinancialReportParams {
  startDate: string;
  endDate: string;
  cashierId?: string;
  reportType?: 'daily' | 'weekly' | 'monthly' | 'custom';
}

// 日结数据
export interface DailyClosing {
  id: string;
  date: string;
  cashierId: string;
  cashierName: string;
  openingCash: number;
  closingCash: number;
  expectedCash: number;
  actualCash: number;
  cashDifference: number;
  totalRevenue: number;
  totalOrders: number;
  refundAmount: number;
  netRevenue: number;
  paymentBreakdown: PaymentMethodBreakdown;
  notes?: string;
  status: 'pending' | 'completed' | 'verified';
  createdAt: string;
  verifiedAt?: string;
  verifiedBy?: string;
}

// 月结数据
export interface MonthlyClosing {
  id: string;
  year: number;
  month: number;
  totalRevenue: number;
  totalOrders: number;
  totalRefunds: number;
  netRevenue: number;
  dailyClosings: number;
  averageDailyRevenue: number;
  topCategories: CategoryBreakdown[];
  paymentTrends: PaymentMethodBreakdown;
  status: 'pending' | 'completed';
  createdAt: string;
  createdBy: string;
}

// 销售统计数据
export interface SalesStatistics {
  period: string;
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  avgOrderValue: number;
  topProducts: ProductSalesData[];
  topCategories: CategoryBreakdown[];
  customerSegments: CustomerSegmentData[];
  salesTrends: SalesTrendData[];
  conversionRate: number;
  returnRate: number;
}

// 商品销售数据
export interface ProductSalesData {
  productId: string;
  productName: string;
  categoryName: string;
  totalSold: number;
  totalRevenue: number;
  avgPrice: number;
  profitMargin?: number;
}

// 客户分段数据
export interface CustomerSegmentData {
  segment: string;
  customerCount: number;
  totalRevenue: number;
  avgOrderValue: number;
  orderFrequency: number;
}

// 销售趋势数据
export interface SalesTrendData {
  date: string;
  revenue: number;
  orderCount: number;
  customerCount: number;
}

/**
 * 财务管理服务类
 * 
 * 功能特性：
 * - 财务报表生成
 * - 销售数据统计
 * - 日结和月结管理
 * - 收支明细查询
 * - 对账和核销
 * - 数据导出功能
 */
class FinanceService {
  private readonly API_BASE = '/api/finance';
  private readonly STORAGE_KEY = 'cashier_finance';

  /**
   * 生成财务报表
   */
  async generateFinancialReport(params: FinancialReportParams): Promise<FinancialData> {
    try {
      // 开发环境：生成模拟财务数据
      const startDate = new Date(params.startDate);
      const endDate = new Date(params.endDate);
      const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      // 模拟数据生成
      const totalOrders = Math.floor(Math.random() * 100 * days) + 50 * days;
      const avgOrderValue = 45 + Math.random() * 30;
      const totalRevenue = totalOrders * avgOrderValue;
      const refundAmount = totalRevenue * (0.02 + Math.random() * 0.03); // 2-5%退款率
      const netRevenue = totalRevenue - refundAmount;

      const financialData: FinancialData = {
        date: params.startDate === params.endDate ? params.startDate : `${params.startDate} 至 ${params.endDate}`,
        totalRevenue: Math.round(totalRevenue * 100) / 100,
        totalOrders,
        avgOrderValue: Math.round(avgOrderValue * 100) / 100,
        refundAmount: Math.round(refundAmount * 100) / 100,
        netRevenue: Math.round(netRevenue * 100) / 100,
        paymentMethodBreakdown: this.generatePaymentBreakdown(totalRevenue, totalOrders),
        hourlyBreakdown: this.generateHourlyBreakdown(totalRevenue, totalOrders),
        categoryBreakdown: this.generateCategoryBreakdown(totalRevenue, totalOrders),
      };

      return financialData;
    } catch (error) {
      console.error('生成财务报表失败:', error);
      throw new Error('生成财务报表失败');
    }
  }

  /**
   * 获取销售统计数据
   */
  async getSalesStatistics(params: FinancialReportParams): Promise<SalesStatistics> {
    try {
      const financialData = await this.generateFinancialReport(params);
      
      const salesStats: SalesStatistics = {
        period: financialData.date,
        totalRevenue: financialData.totalRevenue,
        totalOrders: financialData.totalOrders,
        totalCustomers: Math.floor(financialData.totalOrders * 0.8), // 假设80%是不同客户
        avgOrderValue: financialData.avgOrderValue,
        topProducts: this.generateTopProducts(),
        topCategories: financialData.categoryBreakdown,
        customerSegments: this.generateCustomerSegments(),
        salesTrends: this.generateSalesTrends(params),
        conversionRate: 0.85 + Math.random() * 0.1, // 85-95%
        returnRate: 0.02 + Math.random() * 0.03, // 2-5%
      };

      return salesStats;
    } catch (error) {
      console.error('获取销售统计失败:', error);
      throw new Error('获取销售统计失败');
    }
  }

  /**
   * 执行日结操作
   */
  async performDailyClosing(data: {
    date: string;
    actualCash: number;
    notes?: string;
  }): Promise<DailyClosing> {
    try {
      const financialData = await this.generateFinancialReport({
        startDate: data.date,
        endDate: data.date,
      });

      const expectedCash = financialData.paymentMethodBreakdown.cash.amount;
      const cashDifference = data.actualCash - expectedCash;

      const dailyClosing: DailyClosing = {
        id: this.generateClosingId('DC'),
        date: data.date,
        cashierId: 'current_cashier', // TODO: 从当前用户获取
        cashierName: '当前收银员', // TODO: 从当前用户获取
        openingCash: 500, // 假设开始现金
        closingCash: data.actualCash,
        expectedCash,
        actualCash: data.actualCash,
        cashDifference,
        totalRevenue: financialData.totalRevenue,
        totalOrders: financialData.totalOrders,
        refundAmount: financialData.refundAmount,
        netRevenue: financialData.netRevenue,
        paymentBreakdown: financialData.paymentMethodBreakdown,
        notes: data.notes,
        status: Math.abs(cashDifference) < 5 ? 'completed' : 'pending', // 差额小于5元自动完成
        createdAt: new Date().toISOString(),
      };

      // 存储日结数据
      const closings = this.getStoredClosings();
      closings.push(dailyClosing);
      localStorage.setItem(this.STORAGE_KEY + '_closings', JSON.stringify(closings));

      return dailyClosing;
    } catch (error) {
      console.error('执行日结失败:', error);
      throw new Error('执行日结失败');
    }
  }

  /**
   * 执行月结操作
   */
  async performMonthlyClosing(year: number, month: number): Promise<MonthlyClosing> {
    try {
      // 获取该月的所有日结数据
      const dailyClosings = this.getStoredClosings().filter(closing => {
        const closingDate = new Date(closing.date);
        return closingDate.getFullYear() === year && closingDate.getMonth() + 1 === month;
      });

      const totalRevenue = dailyClosings.reduce((sum, closing) => sum + closing.totalRevenue, 0);
      const totalOrders = dailyClosings.reduce((sum, closing) => sum + closing.totalOrders, 0);
      const totalRefunds = dailyClosings.reduce((sum, closing) => sum + closing.refundAmount, 0);
      const netRevenue = dailyClosings.reduce((sum, closing) => sum + closing.netRevenue, 0);

      const monthlyClosing: MonthlyClosing = {
        id: this.generateClosingId('MC'),
        year,
        month,
        totalRevenue,
        totalOrders,
        totalRefunds,
        netRevenue,
        dailyClosings: dailyClosings.length,
        averageDailyRevenue: dailyClosings.length > 0 ? totalRevenue / dailyClosings.length : 0,
        topCategories: this.generateCategoryBreakdown(totalRevenue, totalOrders),
        paymentTrends: this.aggregatePaymentBreakdown(dailyClosings),
        status: 'completed',
        createdAt: new Date().toISOString(),
        createdBy: 'current_user', // TODO: 从当前用户获取
      };

      // 存储月结数据
      const monthlyClosings = this.getStoredMonthlyClosings();
      monthlyClosings.push(monthlyClosing);
      localStorage.setItem(this.STORAGE_KEY + '_monthly', JSON.stringify(monthlyClosings));

      return monthlyClosing;
    } catch (error) {
      console.error('执行月结失败:', error);
      throw new Error('执行月结失败');
    }
  }

  /**
   * 获取日结列表
   */
  async getDailyClosings(params: {
    startDate?: string;
    endDate?: string;
    cashierId?: string;
    status?: DailyClosing['status'];
  } = {}): Promise<DailyClosing[]> {
    try {
      let closings = this.getStoredClosings();

      // 应用筛选
      if (params.startDate) {
        closings = closings.filter(closing => closing.date >= params.startDate!);
      }
      if (params.endDate) {
        closings = closings.filter(closing => closing.date <= params.endDate!);
      }
      if (params.cashierId) {
        closings = closings.filter(closing => closing.cashierId === params.cashierId);
      }
      if (params.status) {
        closings = closings.filter(closing => closing.status === params.status);
      }

      return closings.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    } catch (error) {
      console.error('获取日结列表失败:', error);
      throw new Error('获取日结列表失败');
    }
  }

  /**
   * 获取月结列表
   */
  async getMonthlyClosings(year?: number): Promise<MonthlyClosing[]> {
    try {
      let closings = this.getStoredMonthlyClosings();

      if (year) {
        closings = closings.filter(closing => closing.year === year);
      }

      return closings.sort((a, b) => {
        if (a.year !== b.year) return b.year - a.year;
        return b.month - a.month;
      });
    } catch (error) {
      console.error('获取月结列表失败:', error);
      throw new Error('获取月结列表失败');
    }
  }

  /**
   * 导出财务数据
   */
  async exportFinancialData(params: FinancialReportParams & {
    format: 'csv' | 'excel' | 'pdf';
  }): Promise<Blob> {
    try {
      const financialData = await this.generateFinancialReport(params);
      
      if (params.format === 'csv') {
        return this.exportToCSV(financialData);
      } else if (params.format === 'excel') {
        return this.exportToExcel(financialData);
      } else {
        return this.exportToPDF(financialData);
      }
    } catch (error) {
      console.error('导出财务数据失败:', error);
      throw new Error('导出财务数据失败');
    }
  }

  /**
   * 私有方法 - 生成支付方式分解数据
   */
  private generatePaymentBreakdown(totalRevenue: number, totalOrders: number): PaymentMethodBreakdown {
    const cashRatio = 0.3 + Math.random() * 0.2; // 30-50%
    const wechatRatio = 0.25 + Math.random() * 0.15; // 25-40%
    const alipayRatio = 0.15 + Math.random() * 0.15; // 15-30%
    const cardRatio = 1 - cashRatio - wechatRatio - alipayRatio;

    return {
      cash: {
        amount: Math.round(totalRevenue * cashRatio * 100) / 100,
        count: Math.floor(totalOrders * cashRatio),
      },
      wechat: {
        amount: Math.round(totalRevenue * wechatRatio * 100) / 100,
        count: Math.floor(totalOrders * wechatRatio),
      },
      alipay: {
        amount: Math.round(totalRevenue * alipayRatio * 100) / 100,
        count: Math.floor(totalOrders * alipayRatio),
      },
      card: {
        amount: Math.round(totalRevenue * cardRatio * 100) / 100,
        count: Math.floor(totalOrders * cardRatio),
      },
    };
  }

  /**
   * 私有方法 - 生成小时销售分解数据
   */
  private generateHourlyBreakdown(totalRevenue: number, totalOrders: number): HourlyBreakdown[] {
    const breakdown: HourlyBreakdown[] = [];
    const peakHours = [11, 12, 13, 17, 18, 19]; // 餐点高峰时间

    for (let hour = 9; hour <= 21; hour++) {
      const isPeak = peakHours.includes(hour);
      const hourRatio = isPeak ? 0.12 + Math.random() * 0.08 : 0.02 + Math.random() * 0.04;
      
      breakdown.push({
        hour,
        revenue: Math.round(totalRevenue * hourRatio * 100) / 100,
        orderCount: Math.floor(totalOrders * hourRatio),
      });
    }

    return breakdown;
  }

  /**
   * 私有方法 - 生成分类销售分解数据
   */
  private generateCategoryBreakdown(totalRevenue: number, totalOrders: number): CategoryBreakdown[] {
    const categories = [
      { id: 'category_hot', name: '热菜', ratio: 0.4 },
      { id: 'category_staple', name: '主食', ratio: 0.25 },
      { id: 'category_soup', name: '汤羹', ratio: 0.15 },
      { id: 'category_vegetable', name: '素菜', ratio: 0.1 },
      { id: 'category_cold', name: '凉菜', ratio: 0.06 },
      { id: 'category_dessert', name: '甜品', ratio: 0.04 },
    ];

    return categories.map(category => ({
      categoryId: category.id,
      categoryName: category.name,
      revenue: Math.round(totalRevenue * category.ratio * 100) / 100,
      orderCount: Math.floor(totalOrders * category.ratio),
      itemCount: Math.floor(totalOrders * category.ratio * (1.2 + Math.random() * 0.5)),
    }));
  }

  /**
   * 私有方法 - 生成热销商品数据
   */
  private generateTopProducts(): ProductSalesData[] {
    const products = [
      { id: 'product_001', name: '宫保鸡丁', category: '热菜' },
      { id: 'product_002', name: '红烧肉', category: '热菜' },
      { id: 'product_006', name: '小笼包', category: '主食' },
      { id: 'product_003', name: '麻婆豆腐', category: '热菜' },
      { id: 'product_005', name: '蒸蛋羹', category: '汤羹' },
    ];

    return products.map((product, index) => ({
      productId: product.id,
      productName: product.name,
      categoryName: product.category,
      totalSold: Math.floor(50 - index * 8 + Math.random() * 20),
      totalRevenue: Math.round((800 - index * 120 + Math.random() * 200) * 100) / 100,
      avgPrice: 25 + Math.random() * 20,
      profitMargin: 0.35 + Math.random() * 0.15,
    }));
  }

  /**
   * 私有方法 - 生成客户分段数据
   */
  private generateCustomerSegments(): CustomerSegmentData[] {
    return [
      {
        segment: '新客户',
        customerCount: Math.floor(30 + Math.random() * 20),
        totalRevenue: Math.round((1200 + Math.random() * 500) * 100) / 100,
        avgOrderValue: 35 + Math.random() * 10,
        orderFrequency: 1,
      },
      {
        segment: '常客',
        customerCount: Math.floor(40 + Math.random() * 20),
        totalRevenue: Math.round((2500 + Math.random() * 800) * 100) / 100,
        avgOrderValue: 45 + Math.random() * 15,
        orderFrequency: 3.2 + Math.random() * 1.5,
      },
      {
        segment: 'VIP客户',
        customerCount: Math.floor(15 + Math.random() * 10),
        totalRevenue: Math.round((1800 + Math.random() * 600) * 100) / 100,
        avgOrderValue: 65 + Math.random() * 20,
        orderFrequency: 5.5 + Math.random() * 2,
      },
    ];
  }

  /**
   * 私有方法 - 生成销售趋势数据
   */
  private generateSalesTrends(params: FinancialReportParams): SalesTrendData[] {
    const startDate = new Date(params.startDate);
    const endDate = new Date(params.endDate);
    const trends: SalesTrendData[] = [];

    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      const dateStr = date.toISOString().split('T')[0];
      trends.push({
        date: dateStr,
        revenue: Math.round((800 + Math.random() * 600) * 100) / 100,
        orderCount: Math.floor(30 + Math.random() * 40),
        customerCount: Math.floor(25 + Math.random() * 30),
      });
    }

    return trends;
  }

  /**
   * 私有方法 - 聚合支付方式分解数据
   */
  private aggregatePaymentBreakdown(closings: DailyClosing[]): PaymentMethodBreakdown {
    const total = {
      cash: { amount: 0, count: 0 },
      wechat: { amount: 0, count: 0 },
      alipay: { amount: 0, count: 0 },
      card: { amount: 0, count: 0 },
    };

    closings.forEach(closing => {
      total.cash.amount += closing.paymentBreakdown.cash.amount;
      total.cash.count += closing.paymentBreakdown.cash.count;
      total.wechat.amount += closing.paymentBreakdown.wechat.amount;
      total.wechat.count += closing.paymentBreakdown.wechat.count;
      total.alipay.amount += closing.paymentBreakdown.alipay.amount;
      total.alipay.count += closing.paymentBreakdown.alipay.count;
      total.card.amount += closing.paymentBreakdown.card.amount;
      total.card.count += closing.paymentBreakdown.card.count;
    });

    return total;
  }

  /**
   * 私有方法 - 导出为CSV格式
   */
  private exportToCSV(data: FinancialData): Blob {
    const csvContent = [
      '财务报表',
      `时间期间,${data.date}`,
      `总收入,${data.totalRevenue}`,
      `订单数量,${data.totalOrders}`,
      `平均订单金额,${data.avgOrderValue}`,
      `退款金额,${data.refundAmount}`,
      `净收入,${data.netRevenue}`,
      '',
      '支付方式分解',
      '方式,金额,订单数',
      `现金,${data.paymentMethodBreakdown.cash.amount},${data.paymentMethodBreakdown.cash.count}`,
      `微信,${data.paymentMethodBreakdown.wechat.amount},${data.paymentMethodBreakdown.wechat.count}`,
      `支付宝,${data.paymentMethodBreakdown.alipay.amount},${data.paymentMethodBreakdown.alipay.count}`,
      `储值卡,${data.paymentMethodBreakdown.card.amount},${data.paymentMethodBreakdown.card.count}`,
    ].join('\n');

    return new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
  }

  /**
   * 私有方法 - 导出为Excel格式（简化版）
   */
  private exportToExcel(data: FinancialData): Blob {
    // 简化的Excel导出，实际项目中应使用专门的库如 xlsx
    return this.exportToCSV(data);
  }

  /**
   * 私有方法 - 导出为PDF格式（简化版）
   */
  private exportToPDF(data: FinancialData): Blob {
    // 简化的PDF导出，实际项目中应使用专门的库如 jsPDF
    const content = `财务报表\n时间: ${data.date}\n总收入: ¥${data.totalRevenue}\n净收入: ¥${data.netRevenue}`;
    return new Blob([content], { type: 'application/pdf' });
  }

  /**
   * 私有方法 - 生成结账ID
   */
  private generateClosingId(prefix: string): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    const randomStr = random.toString();
    const paddedRandom = randomStr.length === 1 ? `00${randomStr}` : 
                        randomStr.length === 2 ? `0${randomStr}` : randomStr;
    return `${prefix}${timestamp}${paddedRandom}`;
  }

  /**
   * 私有方法 - 获取存储的日结数据
   */
  private getStoredClosings(): DailyClosing[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY + '_closings');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  /**
   * 私有方法 - 获取存储的月结数据
   */
  private getStoredMonthlyClosings(): MonthlyClosing[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY + '_monthly');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }
}

// 导出单例实例
export const financeService = new FinanceService(); 