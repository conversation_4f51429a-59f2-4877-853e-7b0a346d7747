/**
 * Electron服务
 * 
 * 提供在React应用中安全调用Electron API的接口
 * 自动检测运行环境，在Web环境下提供模拟实现
 */

// 类型定义
interface ElectronAPI {
  getAppVersion: () => Promise<string>;
  getSystemInfo: () => Promise<{
    platform: string;
    arch: string;
    version: string;
    electronVersion: string;
    chromeVersion: string;
  }>;
  showMessageBox: (options: any) => Promise<any>;
  showOpenDialog: (options: any) => Promise<any>;
  showSaveDialog: (options: any) => Promise<any>;
  openExternal: (url: string) => Promise<void>;
  showItemInFolder: (path: string) => Promise<void>;
  setAlwaysOnTop: (flag: boolean) => Promise<void>;
  minimizeToTray: () => Promise<void>;
  checkForUpdates: () => Promise<void>;
  printReceipt: (options?: any) => Promise<{ success: boolean; error?: string }>;
  readFile: (filePath: string) => Promise<{ success: boolean; data?: string; error?: string }>;
  writeFile: (filePath: string, data: string) => Promise<{ success: boolean; error?: string }>;
  onMenuAction: (callback: (action: string, ...args: any[]) => void) => () => void;
  removeAllListeners: (channel: string) => void;
}

interface PlatformInfo {
  isWindows: boolean;
  isMacOS: boolean;
  isLinux: boolean;
}

interface EnvInfo {
  NODE_ENV: string;
  isDev: boolean;
}

declare global {
  interface Window {
    electronAPI?: ElectronAPI;
    platform?: PlatformInfo;
    env?: EnvInfo;
  }
}

/**
 * Electron服务类
 */
class ElectronService {
  private isElectron: boolean;
  private menuActionCallback: ((action: string, ...args: any[]) => void) | null = null;
  private cleanupMenuListener: (() => void) | null = null;

  constructor() {
    this.isElectron = this.checkElectronEnvironment();
  }

  /**
   * 检查是否在Electron环境中运行
   */
  private checkElectronEnvironment(): boolean {
    if (typeof window === 'undefined') {
      return false;
    }

    return (
      typeof window.electronAPI !== 'undefined' ||
      navigator.userAgent.toLowerCase().indexOf('electron') > -1
    );
  }

  /**
   * 获取应用版本
   */
  async getAppVersion(): Promise<string> {
    if (this.isElectron && window.electronAPI) {
      try {
        return await window.electronAPI.getAppVersion();
      } catch (error) {
        console.error('获取应用版本失败:', error);
      }
    }
    return '1.0.0'; // Web环境下的默认版本
  }

  /**
   * 获取系统信息
   */
  async getSystemInfo(): Promise<{
    platform: string;
    arch: string;
    version: string;
    electronVersion: string;
    chromeVersion: string;
  }> {
    if (this.isElectron && window.electronAPI) {
      try {
        return await window.electronAPI.getSystemInfo();
      } catch (error) {
        console.error('获取系统信息失败:', error);
      }
    }

    // Web环境下的模拟数据
    return {
      platform: navigator.platform ?? 'unknown',
      arch: 'unknown',
      version: 'unknown',
      electronVersion: 'N/A',
      chromeVersion: this.getChromeVersion()
    };
  }

  /**
   * 获取Chrome版本
   */
  private getChromeVersion(): string {
    const raw = navigator.userAgent.match(/Chrom(e|ium)\/([0-9]+)\./);
    return raw ? raw[2] : 'unknown';
  }

  /**
   * 显示消息框
   */
  async showMessageBox(options: {
    type?: 'info' | 'warning' | 'error' | 'question';
    title?: string;
    message: string;
    detail?: string;
    buttons?: string[];
  }): Promise<{ response: number; checkboxChecked?: boolean }> {
    if (this.isElectron && window.electronAPI) {
      try {
        return await window.electronAPI.showMessageBox(options);
      } catch (error) {
        console.error('显示消息框失败:', error);
      }
    }

    // Web环境下使用浏览器对话框
    const result = window.confirm(options.message);
    return { response: result ? 0 : 1 };
  }

  /**
   * 显示文件打开对话框
   */
  async showOpenDialog(options: {
    title?: string;
    defaultPath?: string;
    buttonLabel?: string;
    filters?: Array<{ name: string; extensions: string[] }>;
    properties?: string[];
  }): Promise<{ canceled: boolean; filePaths: string[] }> {
    if (this.isElectron && window.electronAPI) {
      try {
        return await window.electronAPI.showOpenDialog(options);
      } catch (error) {
        console.error('显示文件打开对话框失败:', error);
      }
    }

    // Web环境下使用input[type=file]
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.multiple = options.properties ? options.properties.indexOf('multiSelections') >= 0 : false;
      
      if (options.filters && options.filters.length > 0) {
        const extensions: string[] = [];
        options.filters.forEach((filter) => {
          filter.extensions.forEach((ext) => {
            extensions.push(`.${ext}`);
          });
        });
        input.accept = extensions.join(',');
      }

      input.onchange = () => {
        const files = Array.from(input.files || []);
        resolve({
          canceled: files.length === 0,
          filePaths: files.map(file => file.name)
        });
      };

      input.oncancel = () => {
        resolve({ canceled: true, filePaths: [] });
      };

      input.click();
    });
  }

  /**
   * 显示文件保存对话框
   */
  async showSaveDialog(options: {
    title?: string;
    defaultPath?: string;
    buttonLabel?: string;
    filters?: Array<{ name: string; extensions: string[] }>;
  }): Promise<{ canceled: boolean; filePath?: string }> {
    if (this.isElectron && window.electronAPI) {
      try {
        return await window.electronAPI.showSaveDialog(options);
      } catch (error) {
        console.error('显示文件保存对话框失败:', error);
      }
    }

    // Web环境下使用download
    const fileName = options.defaultPath || 'untitled';
    return { canceled: false, filePath: fileName };
  }

  /**
   * 打开外部链接
   */
  async openExternal(url: string): Promise<void> {
    if (this.isElectron && window.electronAPI) {
      try {
        await window.electronAPI.openExternal(url);
        return;
      } catch (error) {
        console.error('打开外部链接失败:', error);
      }
    }

    // Web环境下使用window.open
    window.open(url, '_blank', 'noopener,noreferrer');
  }

  /**
   * 在文件管理器中显示文件
   */
  async showItemInFolder(path: string): Promise<void> {
    if (this.isElectron && window.electronAPI) {
      try {
        await window.electronAPI.showItemInFolder(path);
        return;
      } catch (error) {
        console.error('在文件管理器中显示文件失败:', error);
      }
    }

    // Web环境下无法实现此功能
    console.warn('Web环境下无法在文件管理器中显示文件');
  }

  /**
   * 设置窗口置顶
   */
  async setAlwaysOnTop(flag: boolean): Promise<void> {
    if (this.isElectron && window.electronAPI) {
      try {
        await window.electronAPI.setAlwaysOnTop(flag);
      } catch (error) {
        console.error('设置窗口置顶失败:', error);
      }
    }
  }

  /**
   * 最小化到系统托盘
   */
  async minimizeToTray(): Promise<void> {
    if (this.isElectron && window.electronAPI) {
      try {
        await window.electronAPI.minimizeToTray();
      } catch (error) {
        console.error('最小化到托盘失败:', error);
      }
    }
  }

  /**
   * 检查更新
   */
  async checkForUpdates(): Promise<void> {
    if (this.isElectron && window.electronAPI) {
      try {
        await window.electronAPI.checkForUpdates();
      } catch (error) {
        console.error('检查更新失败:', error);
      }
    }
  }

  /**
   * 打印小票
   */
  async printReceipt(options?: any): Promise<{ success: boolean; error?: string }> {
    if (this.isElectron && window.electronAPI) {
      try {
        return await window.electronAPI.printReceipt(options);
      } catch (error) {
        console.error('打印失败:', error);
        return { success: false, error: '打印失败' };
      }
    }

    // Web环境下使用浏览器打印
    try {
      window.print();
      return { success: true };
    } catch (error) {
      return { success: false, error: '浏览器打印失败' };
    }
  }

  /**
   * 读取文件
   */
  async readFile(filePath: string): Promise<{ success: boolean; data?: string; error?: string }> {
    if (this.isElectron && window.electronAPI) {
      try {
        return await window.electronAPI.readFile(filePath);
      } catch (error) {
        console.error('读取文件失败:', error);
        return { success: false, error: '读取文件失败' };
      }
    }

    return { success: false, error: 'Web环境下无法直接读取文件' };
  }

  /**
   * 写入文件
   */
  async writeFile(filePath: string, data: string): Promise<{ success: boolean; error?: string }> {
    if (this.isElectron && window.electronAPI) {
      try {
        return await window.electronAPI.writeFile(filePath, data);
      } catch (error) {
        console.error('写入文件失败:', error);
        return { success: false, error: '写入文件失败' };
      }
    }

    // Web环境下使用下载
    try {
      const blob = new Blob([data], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filePath.split('/').pop() || 'download.txt';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      return { success: true };
    } catch (error) {
      return { success: false, error: '下载文件失败' };
    }
  }

  /**
   * 监听菜单操作
   */
  onMenuAction(callback: (action: string, ...args: any[]) => void): () => void {
    this.menuActionCallback = callback;

    if (this.isElectron && window.electronAPI) {
      try {
        this.cleanupMenuListener = window.electronAPI.onMenuAction(callback);
        return this.cleanupMenuListener;
      } catch (error) {
        console.error('监听菜单操作失败:', error);
      }
    }

    // 返回空的清理函数
    return () => {};
  }

  /**
   * 移除菜单监听
   */
  removeMenuListener(): void {
    if (this.cleanupMenuListener) {
      this.cleanupMenuListener();
      this.cleanupMenuListener = null;
    }
    this.menuActionCallback = null;
  }

  /**
   * 获取平台信息
   */
  getPlatformInfo(): PlatformInfo {
    if (this.isElectron && window.platform) {
      return window.platform;
    }

    // Web环境下根据navigator判断
    const userAgent = navigator.userAgent.toLowerCase();
    return {
      isWindows: userAgent.includes('win'),
      isMacOS: userAgent.includes('mac'),
      isLinux: userAgent.includes('linux')
    };
  }

  /**
   * 获取环境信息
   */
  getEnvInfo(): EnvInfo {
    if (this.isElectron && window.env) {
      return window.env;
    }

    // Web环境下的默认值，避免直接访问process
    let nodeEnv = 'production';
    let isDev = false;

    try {
      // 尝试安全地访问process.env
      if (typeof window !== 'undefined' && (window as any).process?.env) {
        nodeEnv = (window as any).process.env.NODE_ENV || 'production';
      }
    } catch (error) {
      // 忽略错误，使用默认值
    }

    isDev = nodeEnv === 'development';

    return {
      NODE_ENV: nodeEnv,
      isDev: isDev
    };
  }

  /**
   * 检查是否在Electron环境中
   */
  isElectronEnvironment(): boolean {
    return this.isElectron;
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.removeMenuListener();
  }
}

// 导出单例实例
export const electronService = new ElectronService(); 