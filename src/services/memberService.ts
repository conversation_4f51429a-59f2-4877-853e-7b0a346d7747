import type { Member } from '../store/slices/memberSlice';

// 查询参数接口
export interface MemberQueryParams {
  phone?: string;
  memberNo?: string;
  name?: string;
  level?: 'bronze' | 'silver' | 'gold' | 'diamond';
  page?: number;
  pageSize?: number;
}

export interface MemberCreateData {
  name: string;
  phone: string;
  idCard?: string;
  address?: string;
  level?: 'regular' | 'silver' | 'gold';
  initialPoints?: number;
  initialBalance?: number;
}

export interface MemberUpdateData {
  name?: string;
  phone?: string;
  idCard?: string;
  address?: string;
  level?: 'regular' | 'silver' | 'gold';
}

export interface PointsTransaction {
  id: string;
  memberId: string;
  type: 'earn' | 'redeem' | 'expire' | 'adjust';
  amount: number;
  reason: string;
  orderId?: string;
  createdAt: string;
  expiresAt?: string;
}

export interface BalanceTransaction {
  id: string;
  memberId: string;
  type: 'recharge' | 'consume' | 'refund' | 'adjust';
  amount: number;
  reason: string;
  orderId?: string;
  paymentMethod?: string;
  createdAt: string;
}

export interface MemberStatistics {
  totalMembers: number;
  newMembersToday: number;
  newMembersThisMonth: number;
  levelDistribution: {
    regular: number;
    silver: number;
    gold: number;
  };
  averageSpending: number;
  totalSpending: number;
}

/**
 * 会员服务类 - 简化版本
 */
class MemberService {
  private readonly API_BASE = '/api/members';

  /**
   * 根据手机号查询会员
   */
  async getMemberByPhone(phone: string): Promise<Member | null> {
    // 开发环境返回null，表示功能开发中
    console.log('getMemberByPhone called with:', phone);
    return null;
  }

  /**
   * 根据会员卡号查询会员
   */
  async getMemberByCardNo(cardNo: string): Promise<Member | null> {
    // 开发环境返回null，表示功能开发中
    console.log('getMemberByCardNo called with:', cardNo);
    return null;
  }

  /**
   * 根据ID获取会员信息
   */
  async getMemberById(id: string): Promise<Member | null> {
    // 开发环境返回null，表示功能开发中
    console.log('getMemberById called with:', id);
    return null;
  }

  /**
   * 查询会员列表
   */
  async getMembers(params: MemberQueryParams = {}): Promise<{
    members: Member[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const { page = 1, pageSize = 20 } = params;
      
      // 开发环境返回空列表
      return {
        members: [],
        total: 0,
        page,
        pageSize,
      };
    } catch (error) {
      console.error('查询会员列表失败:', error);
      throw new Error('查询会员列表失败');
    }
  }
}

// 导出单例
export const memberService = new MemberService(); 