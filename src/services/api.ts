import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { RootState } from '../store';

// API基础URL配置
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api';

// 创建带认证的baseQuery
const baseQueryWithAuth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const result = await fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // 从store中获取token
      const token = (getState() as RootState).auth?.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('Content-Type', 'application/json');
      return headers;
    },
  })(args, api, extraOptions);

  // 处理401未授权错误
  if (result.error && result.error.status === 401) {
    // 触发登出操作
    api.dispatch({ type: 'auth/logout' });
  }

  return result;
};

// 创建API服务
export const api = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithAuth,
  tagTypes: [
    'Order',
    'Member', 
    'Product',
    'Category',
    'Payment',
    'User',
    'Report',
    'SystemConfig'
  ],
  endpoints: (builder) => ({
    // 健康检查接口
    healthCheck: builder.query<{ status: string; timestamp: number }, void>({
      query: () => '/health',
    }),
  }),
});

export const { useHealthCheckQuery } = api; 