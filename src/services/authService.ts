import type { LoginCredentials, LoginResponse, User, LoginHistory } from '../types/auth';

/**
 * 认证服务类
 * 
 * 功能特性：
 * - 用户登录和登出
 * - 权限验证
 * - 用户信息管理
 * - 登录历史记录
 * - 密码重置
 */
class AuthService {
  private readonly API_BASE = '/api/auth';
  private readonly STORAGE_KEY = 'cashier_auth';

  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // 开发环境：模拟登录验证
      const { username, password, remember } = credentials;
      
      // 模拟预设用户账号
      const mockUsers = [
        {
          id: 'user_001',
          username: 'admin',
          password: 'admin123',
          nickname: '系统管理员',
          role: 'admin' as const,
          permissions: ['*'], // 所有权限
          avatar: '',
          email: '<EMAIL>',
          phone: '13800138000',
        },
        {
          id: 'user_002',
          username: 'cashier',
          password: 'cashier123',
          nickname: '收银员',
          role: 'cashier' as const,
          permissions: [
            'order.create',
            'order.query',
            'payment.process',
            'member.query',
            'product.query',
            'refund.create',
          ],
          avatar: '',
          email: '<EMAIL>',
          phone: '13800138001',
        },
        {
          id: 'user_003',
          username: 'manager',
          password: 'manager123',
          nickname: '店长',
          role: 'manager' as const,
          permissions: [
            'order.*',
            'payment.*',
            'member.*',
            'product.*',
            'refund.*',
            'finance.query',
            'system.settings',
          ],
          avatar: '',
          email: '<EMAIL>',
          phone: '13800138002',
        },
      ];

      // 验证用户凭据
      const user = mockUsers.find(u => u.username === username && u.password === password);
      
      if (!user) {
        throw new Error('用户名或密码错误');
      }

      // 生成token
      const token = this.generateToken(user.id);
      
      // 构建用户信息
      const userInfo: User = {
        id: user.id,
        username: user.username,
        nickname: user.nickname,
        role: user.role,
        permissions: user.permissions,
        avatar: user.avatar,
        email: user.email,
        phone: user.phone,
        createdAt: '2024-01-01T00:00:00.000Z',
        lastLoginAt: new Date().toISOString(),
      };

      const loginResponse: LoginResponse = {
        token,
        user: userInfo,
        expiresIn: 24 * 60 * 60 * 1000, // 24小时
      };

      // 记住密码
      if (remember) {
        localStorage.setItem('remembered_username', username);
      } else {
        localStorage.removeItem('remembered_username');
      }

      // 记录登录历史
      await this.recordLoginHistory({
        userId: user.id,
        username: user.username,
        success: true,
      });

      return loginResponse;
    } catch (error) {
      // 记录登录失败
      await this.recordLoginHistory({
        userId: '',
        username: credentials.username,
        success: false,
        reason: error instanceof Error ? error.message : '登录失败',
      });
      
      console.error('登录失败:', error);
      throw new Error(error instanceof Error ? error.message : '登录失败');
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      // 清除本地存储
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      
      // 这里可以调用后端API注销token
      // await fetch(`${this.API_BASE}/logout`, { method: 'POST' });
    } catch (error) {
      console.error('登出失败:', error);
    }
  }

  /**
   * 验证token有效性
   */
  async validateToken(token: string): Promise<User | null> {
    try {
      // 开发环境：简单验证token格式
      if (!token || token.indexOf('TOKEN_') !== 0) {
        return null;
      }

      // 从token中解析用户ID（实际项目中应该调用后端验证）
      const userId = token.replace('TOKEN_', '').split('_')[0];
      
      // 模拟从存储中获取用户信息
      const storedUser = localStorage.getItem('auth_user');
      if (storedUser) {
        const user: User = JSON.parse(storedUser);
        if (user.id === userId) {
          return user;
        }
      }

      return null;
    } catch (error) {
      console.error('验证token失败:', error);
      return null;
    }
  }

  /**
   * 检查用户权限
   */
  hasPermission(userPermissions: string[], requiredPermission: string): boolean {
    // 超级管理员权限
    if (userPermissions.indexOf('*') >= 0) {
      return true;
    }

    // 精确匹配
    if (userPermissions.indexOf(requiredPermission) >= 0) {
      return true;
    }

    // 通配符匹配
    const [module, action] = requiredPermission.split('.');
    const wildcardPermission = `${module}.*`;
    
    return userPermissions.indexOf(wildcardPermission) >= 0;
  }

  /**
   * 获取用户权限列表
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    try {
      // 开发环境：从本地存储获取
      const storedUser = localStorage.getItem('auth_user');
      if (storedUser) {
        const user: User = JSON.parse(storedUser);
        if (user.id === userId) {
          return user.permissions;
        }
      }
      
      return [];
    } catch (error) {
      console.error('获取用户权限失败:', error);
      return [];
    }
  }

  /**
   * 修改密码
   */
  async changePassword(data: {
    oldPassword: string;
    newPassword: string;
  }): Promise<void> {
    try {
      // 开发环境：模拟密码修改
      console.log('修改密码:', data);
      
      // 实际项目中调用后端API
      // await fetch(`${this.API_BASE}/change-password`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(data),
      // });
    } catch (error) {
      console.error('修改密码失败:', error);
      throw new Error('修改密码失败');
    }
  }

  /**
   * 重置密码
   */
  async resetPassword(username: string): Promise<void> {
    try {
      // 开发环境：模拟密码重置
      console.log('重置密码:', username);
      
      // 实际项目中调用后端API
      // await fetch(`${this.API_BASE}/reset-password`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ username }),
      // });
    } catch (error) {
      console.error('重置密码失败:', error);
      throw new Error('重置密码失败');
    }
  }

  /**
   * 获取登录历史
   */
  async getLoginHistory(params: {
    userId?: string;
    page?: number;
    pageSize?: number;
  } = {}): Promise<{
    history: LoginHistory[];
    total: number;
  }> {
    try {
      const {
        userId,
        page = 1,
        pageSize = 20,
      } = params;

      // 开发环境：从本地存储获取
      let history = this.getStoredLoginHistory();

      // 筛选用户
      if (userId) {
        history = history.filter(h => h.userId === userId);
      }

      // 分页
      const total = history.length;
      const start = (page - 1) * pageSize;
      const paginatedHistory = history.slice(start, start + pageSize);

      return {
        history: paginatedHistory,
        total,
      };
    } catch (error) {
      console.error('获取登录历史失败:', error);
      return { history: [], total: 0 };
    }
  }

  /**
   * 私有方法 - 生成token
   */
  private generateToken(userId: string): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `TOKEN_${userId}_${timestamp}_${random}`;
  }

  /**
   * 私有方法 - 记录登录历史
   */
  private async recordLoginHistory(data: {
    userId: string;
    username: string;
    success: boolean;
    reason?: string;
  }): Promise<void> {
    try {
      const history = this.getStoredLoginHistory();
      
      const record: LoginHistory = {
        id: `login_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
        userId: data.userId,
        username: data.username,
        loginTime: new Date().toISOString(),
        ip: '127.0.0.1', // 开发环境默认IP
        userAgent: navigator.userAgent,
        success: data.success,
      };

      // 只有在有reason时才添加
      if (data.reason) {
        record.reason = data.reason;
      }

      history.unshift(record);
      
      // 只保留最近100条记录
      if (history.length > 100) {
        history.splice(100);
      }

      localStorage.setItem(this.STORAGE_KEY + '_login_history', JSON.stringify(history));
    } catch (error) {
      console.error('记录登录历史失败:', error);
    }
  }

  /**
   * 私有方法 - 获取存储的登录历史
   */
  private getStoredLoginHistory(): LoginHistory[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY + '_login_history');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }
}

// 导出单例实例
export const authService = new AuthService(); 