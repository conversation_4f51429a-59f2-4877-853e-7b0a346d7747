import type { Order } from '../store/slices/orderSlice';

// 退款申请接口
export interface RefundRequest {
  id?: string;
  orderId: string;
  orderNo: string;
  refundAmount: number;
  refundReason: string;
  refundItems: RefundItem[];
  applicantId: string;
  applicantName: string;
  status: RefundStatus;
  approvalInfo?: RefundApproval;
  createdAt: string;
  updatedAt: string;
  processedAt?: string;
  note?: string;
}

// 退款商品项目
export interface RefundItem {
  productId: string;
  productName: string;
  originalPrice: number;
  quantity: number;
  refundQuantity: number;
  refundAmount: number;
  reason: string;
}

// 退款状态
export type RefundStatus = 'pending' | 'approved' | 'rejected' | 'processed' | 'cancelled';

// 退款审批信息
export interface RefundApproval {
  approverId: string;
  approverName: string;
  approvalTime: string;
  approvalNote?: string;
  decision: 'approved' | 'rejected';
}

// 退款统计数据
export interface RefundStatistics {
  totalRefunds: number;
  totalRefundAmount: number;
  todayRefunds: number;
  todayRefundAmount: number;
  pendingRefunds: number;
  approvedRefunds: number;
  processedRefunds: number;
  rejectedRefunds: number;
  refundRate: number; // 退款率
  avgRefundAmount: number; // 平均退款金额
}

// 退款查询参数
export interface RefundQueryParams {
  page?: number;
  pageSize?: number;
  status?: RefundStatus;
  orderId?: string;
  applicantId?: string;
  approverId?: string;
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
  keyword?: string;
}

// 退款申请输入接口
export interface RefundRequestInput {
  productId: string;
  refundQuantity: number;
  reason: string;
}

/**
 * 退款管理服务类
 * 
 * 功能特性：
 * - 退款申请创建和提交
 * - 退款审批流程管理
 * - 退款处理和执行
 * - 退款查询和统计
 * - 退款记录管理
 */
class RefundService {
  private readonly API_BASE = '/api/refunds';
  private readonly STORAGE_KEY = 'cashier_refunds';

  /**
   * 创建退款申请
   */
  async createRefundRequest(data: {
    orderId: string;
    refundItems: RefundRequestInput[];
    refundReason: string;
    note?: string;
  }): Promise<RefundRequest> {
    try {
      // 获取订单信息验证
      const order = await this.getOrderById(data.orderId);
      if (!order) {
        throw new Error('订单不存在');
      }

      if (order.paymentStatus !== 'paid') {
        throw new Error('只有已支付的订单才能申请退款');
      }

      // 计算退款金额和处理退款商品
      const refundItems: RefundItem[] = [];
      let totalRefundAmount = 0;

      for (const item of data.refundItems) {
        const orderItem = order.items.find(oi => oi.productId === item.productId);
        if (!orderItem) {
          throw new Error(`商品 ${item.productId} 不在订单中`);
        }

        if (item.refundQuantity > orderItem.quantity) {
          throw new Error(`退款数量不能超过购买数量`);
        }

        const refundAmount = (orderItem.price * item.refundQuantity);
        totalRefundAmount += refundAmount;

        refundItems.push({
          productId: orderItem.productId,
          productName: orderItem.name,
          originalPrice: orderItem.price,
          quantity: orderItem.quantity,
          refundQuantity: item.refundQuantity,
          refundAmount,
          reason: item.reason,
        });
      }

      // 创建退款申请
      const refundRequest: RefundRequest = {
        id: this.generateRefundId(),
        orderId: data.orderId,
        orderNo: order.orderNo,
        refundAmount: totalRefundAmount,
        refundReason: data.refundReason,
        refundItems,
        applicantId: 'current_cashier', // TODO: 从当前用户获取
        applicantName: '当前收银员', // TODO: 从当前用户获取
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...(data.note && { note: data.note }),
      };

      // 开发环境：存储到本地
      const refunds = this.getStoredRefunds();
      refunds.push(refundRequest);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(refunds));

      return refundRequest;
    } catch (error) {
      console.error('创建退款申请失败:', error);
      throw new Error(error instanceof Error ? error.message : '创建退款申请失败');
    }
  }

  /**
   * 审批退款申请
   */
  async approveRefund(refundId: string, decision: 'approved' | 'rejected', note?: string): Promise<RefundRequest> {
    try {
      const approval: RefundApproval = {
        approverId: 'current_manager', // TODO: 从当前用户获取
        approverName: '当前管理员', // TODO: 从当前用户获取
        approvalTime: new Date().toISOString(),
        decision,
      };

      if (note) {
        approval.approvalNote = note;
      }

      // 开发环境：更新本地数据
      const refunds = this.getStoredRefunds();
      const refund = refunds.find(r => r.id === refundId);
      
      if (!refund) {
        throw new Error('退款申请不存在');
      }

      refund.status = decision;
      refund.approvalInfo = approval;
      refund.updatedAt = new Date().toISOString();

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(refunds));
      return refund;
    } catch (error) {
      console.error('审批退款申请失败:', error);
      throw new Error(error instanceof Error ? error.message : '审批退款申请失败');
    }
  }

  /**
   * 处理退款（执行退款）
   */
  async processRefund(refundId: string): Promise<RefundRequest> {
    try {
      const refund = await this.getRefundById(refundId);
      
      if (!refund) {
        throw new Error('退款申请不存在');
      }

      if (refund.status !== 'approved') {
        throw new Error('只有已审批通过的退款才能处理');
      }

      // 调用支付服务处理退款
      const { paymentService } = await import('./paymentService');
      const paymentResult = await paymentService.processRefund(
        refund.orderId,
        refund.refundAmount,
        refund.refundReason
      );

      if (!paymentResult.success) {
        throw new Error('退款处理失败');
      }

      // 开发环境：更新本地数据
      const refunds = this.getStoredRefunds();
      const refundIndex = refunds.findIndex(r => r.id === refundId);
      
      if (refundIndex === -1) {
        throw new Error('退款申请不存在');
      }

      refunds[refundIndex].status = 'processed';
      refunds[refundIndex].processedAt = new Date().toISOString();
      refunds[refundIndex].updatedAt = new Date().toISOString();
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(refunds));
      return refunds[refundIndex];
    } catch (error) {
      console.error('处理退款失败:', error);
      throw new Error(error instanceof Error ? error.message : '处理退款失败');
    }
  }

  /**
   * 获取退款申请列表
   */
  async getRefunds(params: RefundQueryParams = {}): Promise<{
    refunds: RefundRequest[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const {
        page = 1,
        pageSize = 20,
        status,
        orderId,
        applicantId,
        approverId,
        startDate,
        endDate,
        minAmount,
        maxAmount,
        keyword
      } = params;

      // 开发环境：使用模拟数据
      let refunds = this.getStoredRefunds();

      // 应用筛选
      if (status) {
        refunds = refunds.filter(r => r.status === status);
      }
      
      if (orderId) {
        refunds = refunds.filter(r => r.orderId === orderId);
      }
      
      if (applicantId) {
        refunds = refunds.filter(r => r.applicantId === applicantId);
      }
      
      if (approverId) {
        refunds = refunds.filter(r => r.approvalInfo?.approverId === approverId);
      }
      
      if (startDate) {
        refunds = refunds.filter(r => r.createdAt >= startDate);
      }
      
      if (endDate) {
        refunds = refunds.filter(r => r.createdAt <= endDate);
      }
      
      if (minAmount !== undefined) {
        refunds = refunds.filter(r => r.refundAmount >= minAmount);
      }
      
      if (maxAmount !== undefined) {
        refunds = refunds.filter(r => r.refundAmount <= maxAmount);
      }
      
      if (keyword) {
        const searchTerm = keyword.toLowerCase();
        refunds = refunds.filter(r => 
          r.orderNo.toLowerCase().includes(searchTerm) ||
          r.refundReason.toLowerCase().includes(searchTerm) ||
          r.applicantName.toLowerCase().includes(searchTerm)
        );
      }

      // 分页
      const total = refunds.length;
      const start = (page - 1) * pageSize;
      const paginatedRefunds = refunds.slice(start, start + pageSize);

      return {
        refunds: paginatedRefunds,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      console.error('获取退款列表失败:', error);
      throw new Error('获取退款列表失败');
    }
  }

  /**
   * 获取退款详情
   */
  async getRefundById(refundId: string): Promise<RefundRequest | null> {
    try {
      const refunds = this.getStoredRefunds();
      return refunds.find(r => r.id === refundId) || null;
    } catch (error) {
      console.error('获取退款详情失败:', error);
      return null;
    }
  }

  /**
   * 获取退款统计数据
   */
  async getRefundStatistics(params: {
    startDate?: string;
    endDate?: string;
  } = {}): Promise<RefundStatistics> {
    try {
      // 开发环境：计算模拟统计数据
      const refunds = this.getStoredRefunds();
      const today = new Date().toISOString().split('T')[0];

      let filteredRefunds = refunds;
      if (params.startDate) {
        filteredRefunds = filteredRefunds.filter(r => r.createdAt >= params.startDate!);
      }
      if (params.endDate) {
        filteredRefunds = filteredRefunds.filter(r => r.createdAt <= params.endDate!);
      }

      const todayRefunds = filteredRefunds.filter(r => r.createdAt.startsWith(today));

      const totalRefunds = filteredRefunds.length;
      const totalRefundAmount = filteredRefunds.reduce((sum, r) => sum + r.refundAmount, 0);
      const todayRefundsCount = todayRefunds.length;
      const todayRefundAmount = todayRefunds.reduce((sum, r) => sum + r.refundAmount, 0);

      const pendingRefunds = filteredRefunds.filter(r => r.status === 'pending').length;
      const approvedRefunds = filteredRefunds.filter(r => r.status === 'approved').length;
      const processedRefunds = filteredRefunds.filter(r => r.status === 'processed').length;
      const rejectedRefunds = filteredRefunds.filter(r => r.status === 'rejected').length;

      return {
        totalRefunds,
        totalRefundAmount,
        todayRefunds: todayRefundsCount,
        todayRefundAmount,
        pendingRefunds,
        approvedRefunds,
        processedRefunds,
        rejectedRefunds,
        refundRate: totalRefunds > 0 ? (totalRefunds / 100) * 100 : 0, // 模拟退款率
        avgRefundAmount: totalRefunds > 0 ? totalRefundAmount / totalRefunds : 0,
      };
    } catch (error) {
      console.error('获取退款统计失败:', error);
      throw new Error('获取退款统计失败');
    }
  }

  /**
   * 取消退款申请
   */
  async cancelRefund(refundId: string, reason?: string): Promise<RefundRequest> {
    try {
      // 开发环境：更新本地数据
      const refunds = this.getStoredRefunds();
      const refund = refunds.find(r => r.id === refundId);
      
      if (!refund) {
        throw new Error('退款申请不存在');
      }

      if (refund.status !== 'pending') {
        throw new Error('只有待审批的退款申请才能取消');
      }

      refund.status = 'cancelled';
      refund.updatedAt = new Date().toISOString();
      if (reason) {
        refund.note = reason;
      }

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(refunds));
      return refund;
    } catch (error) {
      console.error('取消退款申请失败:', error);
      throw new Error(error instanceof Error ? error.message : '取消退款申请失败');
    }
  }

  /**
   * 私有方法 - 生成退款ID
   */
  private generateRefundId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    const randomStr = random.toString();
    // 使用简单的字符串拼接来确保3位数
    const paddedRandom = randomStr.length === 1 ? `00${randomStr}` : 
                        randomStr.length === 2 ? `0${randomStr}` : randomStr;
    return `RF${timestamp}${paddedRandom}`;
  }

  /**
   * 私有方法 - 获取本地存储的退款数据
   */
  private getStoredRefunds(): RefundRequest[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  /**
   * 私有方法 - 获取订单信息（模拟）
   */
  private async getOrderById(orderId: string): Promise<Order | null> {
    // 这里应该调用订单服务获取订单信息
    // 为了简化，返回一个模拟订单
    return {
      id: orderId,
      orderNo: `ORD${Date.now()}`,
      items: [],
      subtotal: 100,
      discount: 0,
      totalAmount: 100,
      paymentMethod: 'cash',
      paymentStatus: 'paid',
      cashierId: 'cashier1',
      cashierName: '收银员1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }
}

// 导出单例实例
export const refundService = new RefundService(); 