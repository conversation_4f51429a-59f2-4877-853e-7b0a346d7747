{"version": 3, "file": "static/js/165.68925318.chunk.js", "mappings": "qKAqqBO,MAAMA,EAAiB,IA7oB9B,MAAqBC,WAAAA,GAAA,KACFC,SAAW,gBAAgB,KAC3BC,YAAc,kBAAmB,CAKlD,iBAAMC,GAA2E,IAA/DC,EAA2BC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC/C,IACE,MAAM,QACJG,EAAU,GAAE,QACZC,EAAU,CAAC,EAAC,OACZC,EAAS,aAAY,UACrBC,EAAY,OAAM,KAClBC,EAAO,EAAC,SACRC,EAAW,IACTT,EAEuC,CACzC,MAAMU,EAAc,IAAIC,gBAAgB,CACtCP,UACAE,SACAC,YACAC,KAAMA,EAAKI,WACXH,SAAUA,EAASG,aAIrBC,OAAOC,QAAQT,GAASU,SAAQC,IAAmB,IAAjBC,EAAKC,GAAMF,OAC7Bb,IAAVe,GAAiC,OAAVA,IACrBC,MAAMC,QAAQF,GAChBR,EAAYW,OAAOJ,EAAKC,EAAMI,KAAK,MAEnCZ,EAAYW,OAAOJ,EAAKC,EAAMN,gBAKpC,MAAMW,QAAiBC,MAAM,GAADC,OAAIC,KAAK7B,SAAQ,KAAA4B,OAAIf,IACjD,GAAIa,EAASI,GACX,aAAaJ,EAASK,OAExB,MAAM,IAAIC,MAAM,mDAClB,CA4BF,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,oDAAaA,GACrB,IAAID,MAAM,mDAClB,CACF,CAKA,oBAAMG,CAAehC,GACnB,OAAO0B,KAAK3B,YAAYC,EAC1B,CAKA,oBAAMiC,CAAeC,GACnB,IAC6C,CACzC,MAAMX,QAAiBC,MAAM,GAADC,OAAIC,KAAK7B,SAAQ,KAAA4B,OAAIS,IACjD,OAAIX,EAASI,SACEJ,EAASK,OAEjB,IACT,CAIF,CAAE,MAAOE,GAEP,MADAC,QAAQD,MAAM,oDAAaA,GACrB,IAAID,MAAM,mDAClB,CACF,CAKA,mBAAMM,GACJ,IAC6C,CACzC,MAAMZ,QAAiBC,MAAM,mBAC7B,GAAID,EAASI,GACX,aAAaJ,EAASK,OAExB,MAAM,IAAIC,MAAM,mDAClB,CAGF,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,oDAAaA,GACrB,IAAID,MAAM,mDAClB,CACF,CAKA,qBAAMO,GACJ,MAAMC,QAAmBX,KAAKS,gBAC9B,OAAOT,KAAKY,kBAAkBD,EAChC,CAKA,4BAAME,GAAgE,IAAzCC,EAAavC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAC3C,IACE,MAAM,SAAEwC,SAAmBf,KAAK3B,YAAY,CAC1CM,QAAS,CAAEqC,eAAe,GAC1BjC,SAAU+B,IAEZ,OAAOC,CACT,CAAE,MAAOX,GAEP,MADAC,QAAQD,MAAM,oDAAaA,GACrB,IAAID,MAAM,mDAClB,CACF,CAKA,oBAAMc,GAAwD,IAAzCH,EAAavC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACnC,IACE,MAAM,SAAEwC,SAAmBf,KAAK3B,YAAY,CAC1CM,QAAS,CAAEuC,OAAO,GAClBnC,SAAU+B,IAEZ,OAAOC,CACT,CAAE,MAAOX,GAEP,MADAC,QAAQD,MAAM,oDAAaA,GACrB,IAAID,MAAM,mDAClB,CACF,CAKA,oBAAMgB,GAAwD,IAAzCL,EAAavC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACnC,IACE,MAAM,SAAEwC,SAAmBf,KAAK3B,YAAY,CAC1CM,QAAS,CAAEyC,OAAO,GAClBrC,SAAU+B,IAEZ,OAAOC,CACT,CAAE,MAAOX,GAEP,MADAC,QAAQD,MAAM,wCAAWA,GACnB,IAAID,MAAM,uCAClB,CACF,CAKA,iBAAMkB,CAAYC,EAAmBC,GACnC,IAC6C,CACzC,MAAM1B,QAAiBC,MAAM,GAADC,OAAIC,KAAK7B,SAAQ,KAAA4B,OAAIuB,EAAS,UAAU,CAClEE,OAAQ,MACRC,QAAS,CAAE,eAAgB,oBAC3BC,KAAMC,KAAKC,UAAU,CAAEL,eAGzB,GAAI1B,EAASI,GACX,aAAaJ,EAASK,OAExB,MAAM,IAAIC,MAAM,uCAClB,CAaF,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,oDAAaA,GACrB,IAAID,MAAM,mDAClB,CACF,CAKQ0B,YAAAA,CAAad,EAAqBpC,GACxC,OAAOoC,EAASe,QAAOC,IAErB,GAAIpD,EAAQqD,YAAcD,EAAQC,aAAerD,EAAQqD,WACvD,OAAO,EAIT,GAAIrD,EAAQsD,WAAY,CACtB,MAAOC,EAAUC,GAAYxD,EAAQsD,WACrC,GAAIF,EAAQK,MAAQF,GAAYH,EAAQK,MAAQD,EAC9C,OAAO,CAEX,CAGA,GAAIxD,EAAQ0D,MAAQ1D,EAAQ0D,KAAK7D,OAAS,EAAG,CAC3C,MAAM8D,EAAcP,EAAQM,MAAQ,GACpC,IAAK1D,EAAQ0D,KAAKE,MAAKC,GAAOF,EAAYG,SAASD,KACjD,OAAO,CAEX,CAGA,QAA8B/D,IAA1BE,EAAQqC,eAA+Be,EAAQf,gBAAkBrC,EAAQqC,cAC3E,OAAO,EAIT,QAAsBvC,IAAlBE,EAAQuC,OAAuBa,EAAQb,QAAUvC,EAAQuC,MAC3D,OAAO,EAIT,QAAsBzC,IAAlBE,EAAQyC,OAAuBW,EAAQX,QAAUzC,EAAQyC,MAC3D,OAAO,EAIT,QAAwB3C,IAApBE,EAAQ+D,QAAuB,CAEjC,GADgBX,EAAQY,MAAQ,IAChBhE,EAAQ+D,QACtB,OAAO,CAEX,CAGA,QAAI/D,EAAQiE,YAAcjE,EAAQiE,WAAWpE,OAAS,KAC/CuD,EAAQa,aAAejE,EAAQiE,WAAWH,SAASV,EAAQa,iBAM9DjE,EAAQkE,QAAUd,EAAQc,QAAUd,EAAQc,OAASlE,EAAQkE,UAMrE,CAKQC,gBAAAA,CAAiB/B,EAAqBrC,GAC5C,MAAMqE,EAAarE,EAAQsE,cAE3B,OAAOjC,EAASe,QAAOC,KAEjBA,EAAQkB,KAAKD,cAAcP,SAASM,OAKpChB,EAAQmB,YAAYF,cAAcP,SAASM,OAK3ChB,EAAQoB,aAAaH,cAAcP,SAASM,OAK5ChB,EAAQM,OAAQN,EAAQM,KAAKE,MAAKC,GAAOA,EAAIQ,cAAcP,SAASM,SAM5E,CAKQK,YAAAA,CACNrC,EACAnC,EACAC,GAEA,MAAMwE,EAAiB,IAAItC,GAiC3B,OA/BAsC,EAAeC,MAAK,CAACC,EAAGC,KACtB,IAAIC,EAAa,EAEjB,OAAQ7E,GACN,IAAK,QACH6E,EAAaF,EAAEnB,MAAQoB,EAAEpB,MACzB,MACF,IAAK,SACHqB,GAAcF,EAAEV,QAAU,IAAMW,EAAEX,QAAU,GAC5C,MACF,IAAK,OACHY,EAAaF,EAAEN,KAAKS,cAAcF,EAAEP,MACpC,MACF,IAAK,YACHQ,EAAa,IAAIE,KAAKJ,EAAEK,WAAWC,UAAY,IAAIF,KAAKH,EAAEI,WAAWC,UACrE,MACF,IAAK,aAKDJ,GAFgBF,EAAEvC,cAAgB,GAAK,IAAMuC,EAAErC,MAAQ,EAAI,IAAMqC,EAAEV,QAAU,KAC7DW,EAAExC,cAAgB,GAAK,IAAMwC,EAAEtC,MAAQ,EAAI,IAAMsC,EAAEX,QAAU,IAG/E,MACF,QACEY,EAAa,EAGjB,MAAqB,SAAd5E,GAAwB4E,EAAaA,KAGvCJ,CACT,CAKQzC,iBAAAA,CAAkBD,GACxB,MAAMmD,EAAc,IAAIC,IAClBC,EAAqC,GAG3CrD,EAAWtB,SAAQ4E,IACjBH,EAAYI,IAAID,EAASzD,IAAE2D,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOF,GAAQ,IAAEG,SAAU,SAIxDzD,EAAWtB,SAAQ4E,IACjB,MAAMI,EAAOP,EAAYQ,IAAIL,EAASzD,IAEtC,GAAIyD,EAASM,UAAYT,EAAYU,IAAIP,EAASM,UAAW,CAC5CT,EAAYQ,IAAIL,EAASM,UACjCH,SAAUK,KAAKJ,EACxB,MACEL,EAAeS,KAAKJ,MAKxB,MAAMK,EAAeC,IACnBA,EAAMrB,MAAK,CAACC,EAAGC,IAAMD,EAAEqB,MAAQpB,EAAEoB,QACjCD,EAAMtF,SAAQgF,IACRA,EAAKD,UAAYC,EAAKD,SAAS5F,OAAS,GAC1CkG,EAAYL,EAAKD,cAMvB,OADAM,EAAYV,GACLA,CACT,CAKQa,eAAAA,GACN,MAAO,CACL,CACErE,GAAI,cACJyC,KAAM,2BACNC,YAAa,+GACbd,MAAO,GACP0C,cAAe,GACf9C,WAAY,eACZmB,aAAc,eACd4B,MAAO,qCACPC,OAAQ,CACN,uCACA,wCAEFC,KAAM,SACNtC,MAAO,GACPuC,eAAgB,+BAChB7C,KAAM,CAAC,eAAM,qBAAO,gBACpBrB,eAAe,EACfE,OAAO,EACPE,OAAO,EACP+D,SAAU,IACVC,UAAW,CACTC,SAAU,IACVC,QAAS,GACTC,IAAK,GACLC,MAAO,IAETC,UAAW,CAAC,eAAM,gBAClB5C,OAAQ,IACR6C,YAAa,IACbC,gBAAiB,GACjB/C,WAAY,EACZgB,UAAW,2BACXgC,UAAW,4BAEb,CACEpF,GAAI,cACJyC,KAAM,qBACNC,YAAa,6FACbd,MAAO,GACPJ,WAAY,eACZmB,aAAc,eACd4B,MAAO,kCACPE,KAAM,SACNtC,MAAO,GACPuC,eAAgB,aAChB7C,KAAM,CAAC,qBAAO,sBACdrB,eAAe,EACfE,OAAO,EACPE,OAAO,EACPgE,UAAW,CACTC,SAAU,IACVC,QAAS,GACTC,IAAK,GACLC,MAAO,GAET3C,OAAQ,IACR6C,YAAa,GACbC,gBAAiB,GACjB/C,WAAY,EACZgB,UAAW,2BACXgC,UAAW,4BAEb,CACEpF,GAAI,cACJyC,KAAM,2BACNC,YAAa,uFACbd,MAAO,GACPJ,WAAY,eACZmB,aAAc,eACd4B,MAAO,+BACPE,KAAM,SACNtC,MAAO,GACPuC,eAAgB,+BAChB7C,KAAM,CAAC,eAAM,qBAAO,sBACpBrB,eAAe,EACfE,OAAO,EACPE,OAAO,EACPgE,UAAW,CACTC,SAAU,IACVC,QAAS,GACTC,IAAK,GACLC,MAAO,GAET3C,OAAQ,IACR6C,YAAa,GACbC,gBAAiB,GACjB/C,WAAY,EACZgB,UAAW,2BACXgC,UAAW,4BAEb,CACEpF,GAAI,cACJyC,KAAM,iCACNC,YAAa,+DACbd,MAAO,GACPJ,WAAY,qBACZmB,aAAc,eACd4B,MAAO,mCACPE,KAAM,SACNtC,MAAO,GACPuC,eAAgB,+BAChB7C,KAAM,CAAC,eAAM,qBAAO,gBACpBrB,eAAe,EACfE,OAAO,EACPE,OAAO,EACPgE,UAAW,CACTC,SAAU,IACVC,QAAS,EACTC,IAAK,EACLC,MAAO,IAET3C,OAAQ,IACR6C,YAAa,GACbC,gBAAiB,EACjB/C,WAAY,EACZgB,UAAW,2BACXgC,UAAW,4BAEb,CACEpF,GAAI,cACJyC,KAAM,qBACNC,YAAa,2EACbd,MAAO,GACPJ,WAAY,gBACZmB,aAAc,eACd4B,MAAO,iCACPE,KAAM,SACNtC,MAAO,GACPuC,eAAgB,aAChB7C,KAAM,CAAC,eAAM,eAAM,gBACnBrB,eAAe,EACfE,OAAO,EACPE,OAAO,EACPgE,UAAW,CACTC,SAAU,GACVC,QAAS,EACTC,IAAK,EACLC,MAAO,GAET3C,OAAQ,IACR6C,YAAa,GACbC,gBAAiB,GACjB/C,WAAY,EACZgB,UAAW,2BACXgC,UAAW,4BAEb,CACEpF,GAAI,cACJyC,KAAM,qBACNC,YAAa,6FACbd,MAAO,GACPJ,WAAY,kBACZmB,aAAc,eACd4B,MAAO,iCACPE,KAAM,kBACNtC,MAAO,GACPuC,eAAgB,gCAChB7C,KAAM,CAAC,eAAM,2BAAQ,gBACrBrB,eAAe,EACfE,OAAO,EACPE,OAAO,EACPgE,UAAW,CACTC,SAAU,IACVC,QAAS,GACTC,IAAK,EACLC,MAAO,IAET3C,OAAQ,IACR6C,YAAa,IACbC,gBAAiB,EACjB/C,WAAY,EACZgB,UAAW,2BACXgC,UAAW,4BAGjB,CAKQC,iBAAAA,GACN,MAAO,CACL,CACErF,GAAI,eACJyC,KAAM,eACN6C,KAAM,eACNf,MAAO,oCACP7B,YAAa,uCACb0B,MAAO,EACPmB,UAAU,EACVC,aAAc,GACdpC,UAAW,4BAEb,CACEpD,GAAI,qBACJyC,KAAM,eACN6C,KAAM,eACNf,MAAO,oCACP7B,YAAa,uCACb0B,MAAO,EACPmB,UAAU,EACVC,aAAc,GACdpC,UAAW,4BAEb,CACEpD,GAAI,gBACJyC,KAAM,eACN6C,KAAM,eACNf,MAAO,+BACP7B,YAAa,uCACb0B,MAAO,EACPmB,UAAU,EACVC,aAAc,EACdpC,UAAW,4BAEb,CACEpD,GAAI,kBACJyC,KAAM,eACN6C,KAAM,eACNf,MAAO,iCACP7B,YAAa,yDACb0B,MAAO,EACPmB,UAAU,EACVC,aAAc,GACdpC,UAAW,4BAEb,CACEpD,GAAI,gBACJyC,KAAM,eACN6C,KAAM,eACNf,MAAO,qCACP7B,YAAa,uCACb0B,MAAO,EACPmB,UAAU,EACVC,aAAc,GACdpC,UAAW,4BAEb,CACEpD,GAAI,mBACJyC,KAAM,eACN6C,KAAM,eACNf,MAAO,kCACP7B,YAAa,uCACb0B,MAAO,EACPmB,UAAU,EACVC,aAAc,EACdpC,UAAW,4BAGjB,E", "sources": ["services/productService.ts"], "sourcesContent": ["import type { Product, Category, ProductSearchParams, ProductFilters } from '../store/slices/productSlice';\n\nexport interface ProductListResult {\n  products: Product[];\n  total: number;\n  page: number;\n  pageSize: number;\n}\n\nexport interface CategoryTreeNode extends Category {\n  children?: CategoryTreeNode[];\n}\n\n/**\n * 商品管理服务类\n * \n * 功能特性：\n * - 商品信息CRUD操作\n * - 分类管理\n * - 搜索和筛选\n * - 图片管理\n * - 库存管理\n * - 推荐算法\n */\nclass ProductService {\n  private readonly API_BASE = '/api/products';\n  private readonly STORAGE_KEY = 'cashier_products';\n\n  /**\n   * 获取商品列表\n   */\n  async getProducts(params: ProductSearchParams = {}): Promise<ProductListResult> {\n    try {\n      const {\n        keyword = '',\n        filters = {},\n        sortBy = 'popularity',\n        sortOrder = 'desc',\n        page = 1,\n        pageSize = 20,\n      } = params;\n\n      if (process.env.NODE_ENV === 'production') {\n        const queryParams = new URLSearchParams({\n          keyword,\n          sortBy,\n          sortOrder,\n          page: page.toString(),\n          pageSize: pageSize.toString(),\n        });\n\n        // 添加筛选参数\n        Object.entries(filters).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            if (Array.isArray(value)) {\n              queryParams.append(key, value.join(','));\n            } else {\n              queryParams.append(key, value.toString());\n            }\n          }\n        });\n\n        const response = await fetch(`${this.API_BASE}?${queryParams}`);\n        if (response.ok) {\n          return await response.json();\n        }\n        throw new Error('获取商品列表失败');\n      }\n\n      // 开发环境使用模拟数据\n      let products = this.getMockProducts();\n\n      // 应用筛选\n      products = this.applyFilters(products, filters);\n\n      // 应用搜索\n      if (keyword) {\n        products = this.searchInProducts(products, keyword);\n      }\n\n      // 应用排序\n      products = this.sortProducts(products, sortBy, sortOrder);\n\n      // 应用分页\n      const total = products.length;\n      const start = (page - 1) * pageSize;\n      const end = start + pageSize;\n      const pagedProducts = products.slice(start, end);\n\n      return {\n        products: pagedProducts,\n        total,\n        page,\n        pageSize,\n      };\n    } catch (error) {\n      console.error('获取商品列表失败:', error);\n      throw new Error('获取商品列表失败');\n    }\n  }\n\n  /**\n   * 搜索商品\n   */\n  async searchProducts(params: ProductSearchParams): Promise<ProductListResult> {\n    return this.getProducts(params);\n  }\n\n  /**\n   * 根据ID获取商品详情\n   */\n  async getProductById(id: string): Promise<Product | null> {\n    try {\n      if (process.env.NODE_ENV === 'production') {\n        const response = await fetch(`${this.API_BASE}/${id}`);\n        if (response.ok) {\n          return await response.json();\n        }\n        return null;\n      }\n\n      const products = this.getMockProducts();\n      return products.find(product => product.id === id) || null;\n    } catch (error) {\n      console.error('获取商品详情失败:', error);\n      throw new Error('获取商品详情失败');\n    }\n  }\n\n  /**\n   * 获取分类列表\n   */\n  async getCategories(): Promise<Category[]> {\n    try {\n      if (process.env.NODE_ENV === 'production') {\n        const response = await fetch('/api/categories');\n        if (response.ok) {\n          return await response.json();\n        }\n        throw new Error('获取分类列表失败');\n      }\n\n      return this.getMockCategories();\n    } catch (error) {\n      console.error('获取分类列表失败:', error);\n      throw new Error('获取分类列表失败');\n    }\n  }\n\n  /**\n   * 获取分类树结构\n   */\n  async getCategoryTree(): Promise<CategoryTreeNode[]> {\n    const categories = await this.getCategories();\n    return this.buildCategoryTree(categories);\n  }\n\n  /**\n   * 获取推荐商品\n   */\n  async getRecommendedProducts(limit: number = 10): Promise<Product[]> {\n    try {\n      const { products } = await this.getProducts({\n        filters: { isRecommended: true },\n        pageSize: limit,\n      });\n      return products;\n    } catch (error) {\n      console.error('获取推荐商品失败:', error);\n      throw new Error('获取推荐商品失败');\n    }\n  }\n\n  /**\n   * 获取热门商品\n   */\n  async getHotProducts(limit: number = 10): Promise<Product[]> {\n    try {\n      const { products } = await this.getProducts({\n        filters: { isHot: true },\n        pageSize: limit,\n      });\n      return products;\n    } catch (error) {\n      console.error('获取热门商品失败:', error);\n      throw new Error('获取热门商品失败');\n    }\n  }\n\n  /**\n   * 获取新品\n   */\n  async getNewProducts(limit: number = 10): Promise<Product[]> {\n    try {\n      const { products } = await this.getProducts({\n        filters: { isNew: true },\n        pageSize: limit,\n      });\n      return products;\n    } catch (error) {\n      console.error('获取新品失败:', error);\n      throw new Error('获取新品失败');\n    }\n  }\n\n  /**\n   * 更新商品库存\n   */\n  async updateStock(productId: string, quantity: number): Promise<Product> {\n    try {\n      if (process.env.NODE_ENV === 'production') {\n        const response = await fetch(`${this.API_BASE}/${productId}/stock`, {\n          method: 'PUT',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({ quantity }),\n        });\n        \n        if (response.ok) {\n          return await response.json();\n        }\n        throw new Error('更新库存失败');\n      }\n\n      // 开发环境模拟\n      const product = await this.getProductById(productId);\n      if (!product) {\n        throw new Error('商品不存在');\n      }\n\n      return {\n        ...product,\n        stock: quantity,\n        updatedAt: new Date().toISOString(),\n      };\n    } catch (error) {\n      console.error('更新商品库存失败:', error);\n      throw new Error('更新商品库存失败');\n    }\n  }\n\n  /**\n   * 私有方法 - 应用筛选\n   */\n  private applyFilters(products: Product[], filters: ProductFilters): Product[] {\n    return products.filter(product => {\n      // 分类筛选\n      if (filters.categoryId && product.categoryId !== filters.categoryId) {\n        return false;\n      }\n\n      // 价格区间筛选\n      if (filters.priceRange) {\n        const [minPrice, maxPrice] = filters.priceRange;\n        if (product.price < minPrice || product.price > maxPrice) {\n          return false;\n        }\n      }\n\n      // 标签筛选\n      if (filters.tags && filters.tags.length > 0) {\n        const productTags = product.tags || [];\n        if (!filters.tags.some(tag => productTags.includes(tag))) {\n          return false;\n        }\n      }\n\n      // 推荐商品筛选\n      if (filters.isRecommended !== undefined && product.isRecommended !== filters.isRecommended) {\n        return false;\n      }\n\n      // 热门商品筛选\n      if (filters.isHot !== undefined && product.isHot !== filters.isHot) {\n        return false;\n      }\n\n      // 新品筛选\n      if (filters.isNew !== undefined && product.isNew !== filters.isNew) {\n        return false;\n      }\n\n      // 库存筛选\n      if (filters.inStock !== undefined) {\n        const inStock = product.stock > 0;\n        if (inStock !== filters.inStock) {\n          return false;\n        }\n      }\n\n      // 辣度筛选\n      if (filters.spicyLevel && filters.spicyLevel.length > 0) {\n        if (!product.spicyLevel || !filters.spicyLevel.includes(product.spicyLevel)) {\n          return false;\n        }\n      }\n\n      // 评分筛选\n      if (filters.rating && product.rating && product.rating < filters.rating) {\n        return false;\n      }\n\n      return true;\n    });\n  }\n\n  /**\n   * 私有方法 - 搜索商品\n   */\n  private searchInProducts(products: Product[], keyword: string): Product[] {\n    const searchTerm = keyword.toLowerCase();\n    \n    return products.filter(product => {\n      // 商品名称匹配\n      if (product.name.toLowerCase().includes(searchTerm)) {\n        return true;\n      }\n\n      // 商品描述匹配\n      if (product.description.toLowerCase().includes(searchTerm)) {\n        return true;\n      }\n\n      // 分类名称匹配\n      if (product.categoryName.toLowerCase().includes(searchTerm)) {\n        return true;\n      }\n\n      // 标签匹配\n      if (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchTerm))) {\n        return true;\n      }\n\n      return false;\n    });\n  }\n\n  /**\n   * 私有方法 - 排序商品\n   */\n  private sortProducts(\n    products: Product[], \n    sortBy: ProductSearchParams['sortBy'], \n    sortOrder: ProductSearchParams['sortOrder']\n  ): Product[] {\n    const sortedProducts = [...products];\n    \n    sortedProducts.sort((a, b) => {\n      let comparison = 0;\n      \n      switch (sortBy) {\n        case 'price':\n          comparison = a.price - b.price;\n          break;\n        case 'rating':\n          comparison = (a.rating || 0) - (b.rating || 0);\n          break;\n        case 'name':\n          comparison = a.name.localeCompare(b.name);\n          break;\n        case 'createdAt':\n          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n          break;\n        case 'popularity':\n          // 按推荐、热门、评分综合排序\n          {\n            const aScore = (a.isRecommended ? 10 : 0) + (a.isHot ? 5 : 0) + (a.rating || 0);\n            const bScore = (b.isRecommended ? 10 : 0) + (b.isHot ? 5 : 0) + (b.rating || 0);\n            comparison = aScore - bScore;\n          }\n          break;\n        default:\n          comparison = 0;\n      }\n\n      return sortOrder === 'desc' ? -comparison : comparison;\n    });\n\n    return sortedProducts;\n  }\n\n  /**\n   * 私有方法 - 构建分类树\n   */\n  private buildCategoryTree(categories: Category[]): CategoryTreeNode[] {\n    const categoryMap = new Map<string, CategoryTreeNode>();\n    const rootCategories: CategoryTreeNode[] = [];\n\n    // 创建分类映射\n    categories.forEach(category => {\n      categoryMap.set(category.id, { ...category, children: [] });\n    });\n\n    // 构建树结构\n    categories.forEach(category => {\n      const node = categoryMap.get(category.id)!;\n      \n      if (category.parentId && categoryMap.has(category.parentId)) {\n        const parent = categoryMap.get(category.parentId)!;\n        parent.children!.push(node);\n      } else {\n        rootCategories.push(node);\n      }\n    });\n\n    // 按order字段排序\n    const sortByOrder = (nodes: CategoryTreeNode[]) => {\n      nodes.sort((a, b) => a.order - b.order);\n      nodes.forEach(node => {\n        if (node.children && node.children.length > 0) {\n          sortByOrder(node.children);\n        }\n      });\n    };\n\n    sortByOrder(rootCategories);\n    return rootCategories;\n  }\n\n  /**\n   * 私有方法 - 获取模拟商品数据\n   */\n  private getMockProducts(): Product[] {\n    return [\n      {\n        id: 'product_001',\n        name: '宫保鸡丁',\n        description: '经典川菜，嫩滑鸡肉配花生米，酸甜香辣',\n        price: 32.00,\n        originalPrice: 38.00,\n        categoryId: 'category_hot',\n        categoryName: '热菜',\n        image: '/images/dishes/gongbao-chicken.jpg',\n        images: [\n          '/images/dishes/gongbao-chicken-1.jpg',\n          '/images/dishes/gongbao-chicken-2.jpg',\n        ],\n        unit: '份',\n        stock: 50,\n        specifications: '约300g，微辣',\n        tags: ['川菜', '下饭菜', '经典'],\n        isRecommended: true,\n        isHot: true,\n        isNew: false,\n        discount: 0.16,\n        nutrition: {\n          calories: 280,\n          protein: 22,\n          fat: 18,\n          carbs: 12,\n        },\n        allergens: ['花生', '大豆'],\n        rating: 4.8,\n        reviewCount: 156,\n        preparationTime: 15,\n        spicyLevel: 2,\n        createdAt: '2023-06-15T10:30:00.000Z',\n        updatedAt: '2024-01-10T14:20:00.000Z',\n      },\n      {\n        id: 'product_002',\n        name: '红烧肉',\n        description: '肥瘦相间，软糯香甜，传统家常菜',\n        price: 45.00,\n        categoryId: 'category_hot',\n        categoryName: '热菜',\n        image: '/images/dishes/braised-pork.jpg',\n        unit: '份',\n        stock: 30,\n        specifications: '约400g',\n        tags: ['家常菜', '下饭菜'],\n        isRecommended: true,\n        isHot: false,\n        isNew: false,\n        nutrition: {\n          calories: 420,\n          protein: 25,\n          fat: 32,\n          carbs: 8,\n        },\n        rating: 4.6,\n        reviewCount: 89,\n        preparationTime: 25,\n        spicyLevel: 0,\n        createdAt: '2023-08-20T09:15:00.000Z',\n        updatedAt: '2024-01-08T16:45:00.000Z',\n      },\n      {\n        id: 'product_003',\n        name: '麻婆豆腐',\n        description: '嫩滑豆腐配香辣肉末，经典川菜',\n        price: 28.00,\n        categoryId: 'category_hot',\n        categoryName: '热菜',\n        image: '/images/dishes/mapo-tofu.jpg',\n        unit: '份',\n        stock: 45,\n        specifications: '约350g，中辣',\n        tags: ['川菜', '豆制品', '下饭菜'],\n        isRecommended: false,\n        isHot: true,\n        isNew: false,\n        nutrition: {\n          calories: 180,\n          protein: 12,\n          fat: 14,\n          carbs: 6,\n        },\n        rating: 4.5,\n        reviewCount: 78,\n        preparationTime: 12,\n        spicyLevel: 3,\n        createdAt: '2023-09-10T11:20:00.000Z',\n        updatedAt: '2024-01-05T13:30:00.000Z',\n      },\n      {\n        id: 'product_004',\n        name: '酸辣土豆丝',\n        description: '爽脆土豆丝，酸辣开胃',\n        price: 18.00,\n        categoryId: 'category_vegetable',\n        categoryName: '素菜',\n        image: '/images/dishes/potato-strips.jpg',\n        unit: '份',\n        stock: 60,\n        specifications: '约250g，微辣',\n        tags: ['素菜', '开胃菜', '爽脆'],\n        isRecommended: false,\n        isHot: false,\n        isNew: true,\n        nutrition: {\n          calories: 120,\n          protein: 3,\n          fat: 8,\n          carbs: 22,\n        },\n        rating: 4.3,\n        reviewCount: 45,\n        preparationTime: 8,\n        spicyLevel: 1,\n        createdAt: '2024-01-01T08:00:00.000Z',\n        updatedAt: '2024-01-10T10:15:00.000Z',\n      },\n      {\n        id: 'product_005',\n        name: '蒸蛋羹',\n        description: '嫩滑如丝的蒸蛋，老少皆宜',\n        price: 15.00,\n        categoryId: 'category_soup',\n        categoryName: '汤羹',\n        image: '/images/dishes/steamed-egg.jpg',\n        unit: '份',\n        stock: 40,\n        specifications: '约200g',\n        tags: ['蒸菜', '养生', '嫩滑'],\n        isRecommended: true,\n        isHot: false,\n        isNew: false,\n        nutrition: {\n          calories: 90,\n          protein: 8,\n          fat: 6,\n          carbs: 2,\n        },\n        rating: 4.7,\n        reviewCount: 92,\n        preparationTime: 10,\n        spicyLevel: 0,\n        createdAt: '2023-07-25T14:30:00.000Z',\n        updatedAt: '2024-01-09T09:20:00.000Z',\n      },\n      {\n        id: 'product_006',\n        name: '小笼包',\n        description: '皮薄馅大，汤汁丰富的传统小笼包',\n        price: 25.00,\n        categoryId: 'category_staple',\n        categoryName: '主食',\n        image: '/images/dishes/xiaolongbao.jpg',\n        unit: '笼(8个)',\n        stock: 35,\n        specifications: '8个装，约150g',\n        tags: ['包子', '传统小吃', '汤包'],\n        isRecommended: true,\n        isHot: false,\n        isNew: true,\n        nutrition: {\n          calories: 220,\n          protein: 12,\n          fat: 8,\n          carbs: 28,\n        },\n        rating: 4.9,\n        reviewCount: 203,\n        preparationTime: 5,\n        spicyLevel: 0,\n        createdAt: '2024-01-05T07:45:00.000Z',\n        updatedAt: '2024-01-10T12:00:00.000Z',\n      },\n    ];\n  }\n\n  /**\n   * 私有方法 - 获取模拟分类数据\n   */\n  private getMockCategories(): Category[] {\n    return [\n      {\n        id: 'category_hot',\n        name: '热菜',\n        icon: '🔥',\n        image: '/images/categories/hot-dishes.jpg',\n        description: '各种热菜炒菜',\n        order: 1,\n        isActive: true,\n        productCount: 25,\n        createdAt: '2023-06-01T00:00:00.000Z',\n      },\n      {\n        id: 'category_vegetable',\n        name: '素菜',\n        icon: '🥬',\n        image: '/images/categories/vegetables.jpg',\n        description: '清爽素食菜品',\n        order: 2,\n        isActive: true,\n        productCount: 12,\n        createdAt: '2023-06-01T00:00:00.000Z',\n      },\n      {\n        id: 'category_soup',\n        name: '汤羹',\n        icon: '🍲',\n        image: '/images/categories/soups.jpg',\n        description: '各式汤品羹类',\n        order: 3,\n        isActive: true,\n        productCount: 8,\n        createdAt: '2023-06-01T00:00:00.000Z',\n      },\n      {\n        id: 'category_staple',\n        name: '主食',\n        icon: '🍚',\n        image: '/images/categories/staples.jpg',\n        description: '米饭面条包子等主食',\n        order: 4,\n        isActive: true,\n        productCount: 15,\n        createdAt: '2023-06-01T00:00:00.000Z',\n      },\n      {\n        id: 'category_cold',\n        name: '凉菜',\n        icon: '🥗',\n        image: '/images/categories/cold-dishes.jpg',\n        description: '清爽凉拌菜品',\n        order: 5,\n        isActive: true,\n        productCount: 10,\n        createdAt: '2023-06-01T00:00:00.000Z',\n      },\n      {\n        id: 'category_dessert',\n        name: '甜品',\n        icon: '🍮',\n        image: '/images/categories/desserts.jpg',\n        description: '各种甜品点心',\n        order: 6,\n        isActive: true,\n        productCount: 8,\n        createdAt: '2023-06-01T00:00:00.000Z',\n      },\n    ];\n  }\n}\n\n// 导出单例实例\nexport const productService = new ProductService(); "], "names": ["productService", "constructor", "API_BASE", "STORAGE_KEY", "getProducts", "params", "arguments", "length", "undefined", "keyword", "filters", "sortBy", "sortOrder", "page", "pageSize", "queryParams", "URLSearchParams", "toString", "Object", "entries", "for<PERSON>ach", "_ref", "key", "value", "Array", "isArray", "append", "join", "response", "fetch", "concat", "this", "ok", "json", "Error", "error", "console", "searchProducts", "getProductById", "id", "getCategories", "getCategoryTree", "categories", "buildCategoryTree", "getRecommendedProducts", "limit", "products", "isRecommended", "getHotProducts", "isHot", "getNewProducts", "isNew", "updateStock", "productId", "quantity", "method", "headers", "body", "JSON", "stringify", "applyFilters", "filter", "product", "categoryId", "priceRange", "minPrice", "maxPrice", "price", "tags", "productTags", "some", "tag", "includes", "inStock", "stock", "spicyLevel", "rating", "searchInProducts", "searchTerm", "toLowerCase", "name", "description", "categoryName", "sortProducts", "sortedProducts", "sort", "a", "b", "comparison", "localeCompare", "Date", "createdAt", "getTime", "categoryMap", "Map", "rootCategories", "category", "set", "_objectSpread", "children", "node", "get", "parentId", "has", "push", "sortByOrder", "nodes", "order", "getMockProducts", "originalPrice", "image", "images", "unit", "specifications", "discount", "nutrition", "calories", "protein", "fat", "carbs", "allergens", "reviewCount", "preparationTime", "updatedAt", "getMockCategories", "icon", "isActive", "productCount"], "sourceRoot": ""}