// 系统状态栏样式
.status-bar {
  height: 60px;
  background: linear-gradient(135deg, #1677ff 0%, #0958d9 100%);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 100;
  
  // 左侧信息区域
  &-left {
    display: flex;
    align-items: center;
    gap: 32px;
  }
  
  // 右侧操作区域
  &-right {
    display: flex;
    align-items: center;
    gap: 24px;
  }
}

// 状态项通用样式
.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 44px; // 适老化触摸目标尺寸
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
  }
  
  &:focus {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
  }
}

// 状态图标
.status-icon {
  font-size: 18px;
  color: #ffffff;
  
  &.syncing {
    animation: rotate 2s linear infinite;
  }
}

// 旋转动画（数据同步）
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 时间显示
.time-display {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 12px 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  
  .elder-text {
    color: #ffffff;
    font-size: 18px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

// 同步状态
.sync-status {
  .elder-text {
    color: #ffffff;
    font-size: 14px;
  }
}

// 网络状态
.network-status {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  
  .elder-text {
    font-size: 14px;
    font-weight: 500;
  }
}

// 用户信息
.user-info {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  min-width: 160px;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  .user-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
    
    .elder-text {
      color: #ffffff;
      line-height: 1.2;
      
      &.elder-text-base {
        font-size: 16px;
        font-weight: 600;
      }
      
      &.elder-text-xs {
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }
}

// 状态按钮
.status-button {
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  min-width: 44px;
  min-height: 44px;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-1px);
  }
  
  &:focus {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
  }
  
  .anticon {
    font-size: 18px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .status-bar {
    padding: 0 16px;
    height: 55px;
    
    &-left {
      gap: 24px;
    }
    
    &-right {
      gap: 16px;
    }
  }
  
  .status-item {
    padding: 6px 10px;
    gap: 6px;
  }
  
  .time-display {
    padding: 8px 12px;
    
    .elder-text {
      font-size: 16px;
    }
  }
  
  .user-info {
    min-width: 140px;
    padding: 6px 12px;
  }
}

@media (max-width: 768px) {
  .status-bar {
    padding: 0 12px;
    height: 50px;
    
    &-left {
      gap: 16px;
    }
    
    &-right {
      gap: 12px;
    }
  }
  
  .status-item {
    padding: 4px 8px;
    gap: 4px;
    min-height: 40px;
  }
  
  .status-icon {
    font-size: 16px;
  }
  
  .time-display {
    padding: 6px 10px;
    
    .elder-text {
      font-size: 14px;
    }
  }
  
  .sync-status {
    .elder-text {
      font-size: 12px;
    }
  }
  
  .network-status {
    .elder-text {
      font-size: 12px;
    }
  }
  
  .user-info {
    min-width: 120px;
    padding: 4px 10px;
    
    .user-details {
      .elder-text {
        &.elder-text-base {
          font-size: 14px;
        }
        
        &.elder-text-xs {
          font-size: 10px;
        }
      }
    }
  }
  
  .status-button {
    min-width: 40px;
    min-height: 40px;
    
    .anticon {
      font-size: 16px;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .status-bar {
    background: #000000;
    border-bottom: 3px solid #ffffff;
  }
  
  .status-item {
    border: 1px solid #ffffff;
    background-color: transparent;
    
    &:hover {
      background-color: #333333;
    }
  }
  
  .time-display, .user-info, .network-status {
    background-color: #333333;
    border: 2px solid #ffffff;
  }
  
  .status-button {
    background-color: #333333 !important;
    border: 2px solid #ffffff !important;
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  .status-item {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
  
  .status-button {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
  
  .status-icon.syncing {
    animation: none;
  }
}

// 打印样式
@media print {
  .status-bar {
    background: #ffffff !important;
    color: #000000 !important;
    border-bottom: 2px solid #000000;
    box-shadow: none;
    
    * {
      color: #000000 !important;
    }
  }
} 