// 菜品展示区域样式
.menu-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

// 搜索栏
.menu-search {
  .ant-input-affix-wrapper {
    border-radius: 8px;
    border: 2px solid #d9d9d9;
    
    &:hover {
      border-color: #40a9ff;
    }
    
    &:focus-within {
      border-color: #1677ff;
      box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.2);
    }
  }
}

// 分类标签页
.menu-categories {
  .elder-menu-tabs {
    .ant-tabs-nav {
      margin-bottom: 16px;
      
      .ant-tabs-tab {
        padding: 12px 24px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 8px 8px 0 0;
        margin-right: 8px;
        min-height: 48px;
        display: flex;
        align-items: center;
        
        &:hover {
          background-color: rgba(22, 119, 255, 0.1);
        }
        
        &.ant-tabs-tab-active {
          background-color: #1677ff;
          color: #ffffff;
          
          .elder-text {
            color: #ffffff;
          }
          
          .ant-badge {
            .ant-badge-count {
              background-color: #ffffff;
              color: #1677ff;
            }
          }
        }
        
        .elder-text {
          color: inherit;
        }
        
        .ant-badge {
          .ant-badge-count {
            background-color: #1677ff;
            font-size: 12px;
            min-width: 20px;
            height: 20px;
            line-height: 20px;
          }
        }
      }
      
      .ant-tabs-ink-bar {
        display: none;
      }
    }
  }
}

// 菜品网格
.menu-grid {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  
  .menu-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    padding-bottom: 16px;
  }
}

// 菜品卡片
.menu-item {
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
  
  &:hover {
    border-color: #40a9ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  &:focus {
    outline: 3px solid #1677ff;
    outline-offset: 2px;
  }
  
  &.menu-item-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      border-color: transparent;
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
  
  .menu-item-content {
    .item-image {
      position: relative;
      width: 100%;
      height: 120px;
      border-radius: 6px;
      overflow: hidden;
      background-color: #f5f5f5;
      margin-bottom: 12px;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
      
      .item-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #bfbfbf;
        font-size: 32px;
      }
      
      .item-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        
        .elder-text {
          color: #ffffff;
          background-color: rgba(255, 255, 255, 0.2);
          padding: 4px 12px;
          border-radius: 4px;
        }
      }
    }
    
    .item-info {
      h4 {
        margin: 0 0 4px 0;
        color: #262626;
        font-size: 16px;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      p {
        margin: 0 0 8px 0;
        color: #8c8c8c;
        font-size: 12px;
        line-height: 1.3;
        height: 32px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .item-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .item-price {
          font-size: 18px;
          font-weight: 700;
          color: #f5222d;
        }
        
        .ant-btn {
          padding: 4px 12px;
          height: auto;
          font-size: 14px;
          border-radius: 4px;
        }
      }
    }
  }
}

// 操作提示
.menu-tips {
  padding: 8px 16px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  text-align: center;
  
  .elder-text {
    color: #52c41a;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .menu-section {
    gap: 12px;
  }
  
  .menu-categories {
    .elder-menu-tabs {
      .ant-tabs-nav {
        margin-bottom: 12px;
        
        .ant-tabs-tab {
          padding: 10px 20px;
          font-size: 15px;
          min-height: 44px;
          margin-right: 6px;
        }
      }
    }
  }
  
  .menu-grid {
    .menu-items-grid {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
      gap: 12px;
    }
  }
  
  .menu-item {
    .menu-item-content {
      .item-image {
        height: 100px;
        margin-bottom: 10px;
      }
      
      .item-info {
        h4 {
          font-size: 15px;
        }
        
        p {
          font-size: 11px;
          height: 28px;
        }
        
        .item-footer {
          .item-price {
            font-size: 16px;
          }
          
          .ant-btn {
            font-size: 13px;
            padding: 3px 10px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .menu-section {
    gap: 10px;
  }
  
  .menu-categories {
    .elder-menu-tabs {
      .ant-tabs-nav {
        margin-bottom: 10px;
        
        .ant-tabs-tab {
          padding: 8px 16px;
          font-size: 14px;
          min-height: 40px;
          margin-right: 4px;
          
          .ant-badge {
            .ant-badge-count {
              font-size: 10px;
              min-width: 16px;
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
    }
  }
  
  .menu-grid {
    .menu-items-grid {
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
      gap: 10px;
    }
  }
  
  .menu-item {
    .menu-item-content {
      .item-image {
        height: 80px;
        margin-bottom: 8px;
      }
      
      .item-info {
        h4 {
          font-size: 14px;
        }
        
        p {
          font-size: 10px;
          height: 24px;
        }
        
        .item-footer {
          flex-direction: column;
          align-items: flex-start;
          gap: 6px;
          
          .item-price {
            font-size: 15px;
          }
          
          .ant-btn {
            width: 100%;
            font-size: 12px;
            padding: 4px 8px;
          }
        }
      }
    }
  }
  
  .menu-tips {
    padding: 6px 12px;
    
    .elder-text {
      font-size: 10px;
    }
  }
}

// 超小屏幕适配
@media (max-width: 480px) {
  .menu-grid {
    .menu-items-grid {
      grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
      gap: 8px;
    }
  }
  
  .menu-categories {
    .elder-menu-tabs {
      .ant-tabs-nav {
        .ant-tabs-tab {
          padding: 6px 12px;
          font-size: 13px;
          min-height: 36px;
          margin-right: 2px;
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .menu-item {
    border: 2px solid #000000;
    
    &:hover {
      border-color: #0050b3;
      background-color: #f0f8ff;
    }
    
    &.menu-item-disabled {
      border-color: #434343;
      background-color: #f5f5f5;
    }
    
    .menu-item-content {
      .item-image {
        border: 1px solid #000000;
        
        .item-overlay {
          background-color: rgba(0, 0, 0, 0.8);
          
          .elder-text {
            background-color: #ffffff;
            color: #000000;
          }
        }
      }
      
      .item-info {
        h4 {
          color: #000000;
        }
        
        p {
          color: #434343;
        }
        
        .item-footer {
          .item-price {
            color: #a8071a;
          }
        }
      }
    }
  }
  
  .menu-categories {
    .elder-menu-tabs {
      .ant-tabs-nav {
        .ant-tabs-tab {
          border: 2px solid #000000;
          
          &.ant-tabs-tab-active {
            background-color: #000000;
            color: #ffffff;
          }
        }
      }
    }
  }
  
  .menu-tips {
    background-color: #ffffff;
    border: 2px solid #000000;
    
    .elder-text {
      color: #000000;
    }
  }
}

// 打印样式
@media print {
  .menu-section {
    height: auto;
  }
  
  .menu-search, .menu-tips {
    display: none;
  }
  
  .menu-grid {
    overflow: visible;
    
    .menu-items-grid {
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
    }
  }
  
  .menu-item {
    border: 1px solid #000000;
    break-inside: avoid;
    
    &:hover {
      transform: none;
      box-shadow: none;
    }
    
    .menu-item-content {
      .item-info {
        .item-footer {
          .ant-btn {
            display: none;
          }
        }
      }
    }
  }
  
  .menu-categories {
    .elder-menu-tabs {
      .ant-tabs-nav {
        border-bottom: 2px solid #000000;
      }
    }
  }
} 