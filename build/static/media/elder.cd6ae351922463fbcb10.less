// 适老化专用样式
@import '~antd/dist/reset.css';

// 适老化变量定义
@elder-font-size-xs: 18px;
@elder-font-size-sm: 20px;
@elder-font-size-base: 22px;
@elder-font-size-lg: 24px;
@elder-font-size-xl: 28px;
@elder-font-size-2xl: 32px;
@elder-font-size-3xl: 36px;
@elder-font-size-4xl: 42px;

@elder-line-height-tight: 1.25;
@elder-line-height-normal: 1.5;
@elder-line-height-relaxed: 1.75;
@elder-line-height-loose: 2;

@elder-spacing-xs: 4px;
@elder-spacing-sm: 8px;
@elder-spacing-base: 16px;
@elder-spacing-lg: 24px;
@elder-spacing-xl: 32px;
@elder-spacing-2xl: 48px;
@elder-spacing-3xl: 64px;
@elder-spacing-4xl: 80px;

@elder-radius-sm: 6px;
@elder-radius-base: 8px;
@elder-radius-lg: 12px;

// 适老化通用样式类
.elder {
  // 基础文本样式
  &-text {
    font-size: @elder-font-size-base;
    line-height: @elder-line-height-normal;
    color: #000000;
    font-weight: 400;
    
    &-xs { font-size: @elder-font-size-xs; }
    &-sm { font-size: @elder-font-size-sm; }
    &-lg { font-size: @elder-font-size-lg; }
    &-xl { font-size: @elder-font-size-xl; }
    &-2xl { font-size: @elder-font-size-2xl; }
    &-3xl { font-size: @elder-font-size-3xl; }
    &-4xl { font-size: @elder-font-size-4xl; }
    
    &-bold { font-weight: 600; }
    &-medium { font-weight: 500; }
    
    &-primary { color: #1677ff; }
    &-success { color: #52c41a; }
    &-warning { color: #fa8c16; }
    &-error { color: #f5222d; }
    &-secondary { color: #595959; }
  }
  
  // 间距辅助类
  &-m {
    &-xs { margin: @elder-spacing-xs; }
    &-sm { margin: @elder-spacing-sm; }
    &-base { margin: @elder-spacing-base; }
    &-lg { margin: @elder-spacing-lg; }
    &-xl { margin: @elder-spacing-xl; }
    &-2xl { margin: @elder-spacing-2xl; }
    
    &t-xs { margin-top: @elder-spacing-xs; }
    &t-sm { margin-top: @elder-spacing-sm; }
    &t-base { margin-top: @elder-spacing-base; }
    &t-lg { margin-top: @elder-spacing-lg; }
    &t-xl { margin-top: @elder-spacing-xl; }
    
    &b-xs { margin-bottom: @elder-spacing-xs; }
    &b-sm { margin-bottom: @elder-spacing-sm; }
    &b-base { margin-bottom: @elder-spacing-base; }
    &b-lg { margin-bottom: @elder-spacing-lg; }
    &b-xl { margin-bottom: @elder-spacing-xl; }
    
    &l-xs { margin-left: @elder-spacing-xs; }
    &l-sm { margin-left: @elder-spacing-sm; }
    &l-base { margin-left: @elder-spacing-base; }
    &l-lg { margin-left: @elder-spacing-lg; }
    &l-xl { margin-left: @elder-spacing-xl; }
    
    &r-xs { margin-right: @elder-spacing-xs; }
    &r-sm { margin-right: @elder-spacing-sm; }
    &r-base { margin-right: @elder-spacing-base; }
    &r-lg { margin-right: @elder-spacing-lg; }
    &r-xl { margin-right: @elder-spacing-xl; }
  }
  
  &-p {
    &-xs { padding: @elder-spacing-xs; }
    &-sm { padding: @elder-spacing-sm; }
    &-base { padding: @elder-spacing-base; }
    &-lg { padding: @elder-spacing-lg; }
    &-xl { padding: @elder-spacing-xl; }
    &-2xl { padding: @elder-spacing-2xl; }
    
    &t-xs { padding-top: @elder-spacing-xs; }
    &t-sm { padding-top: @elder-spacing-sm; }
    &t-base { padding-top: @elder-spacing-base; }
    &t-lg { padding-top: @elder-spacing-lg; }
    &t-xl { padding-top: @elder-spacing-xl; }
    
    &b-xs { padding-bottom: @elder-spacing-xs; }
    &b-sm { padding-bottom: @elder-spacing-sm; }
    &b-base { padding-bottom: @elder-spacing-base; }
    &b-lg { padding-bottom: @elder-spacing-lg; }
    &b-xl { padding-bottom: @elder-spacing-xl; }
    
    &l-xs { padding-left: @elder-spacing-xs; }
    &l-sm { padding-left: @elder-spacing-sm; }
    &l-base { padding-left: @elder-spacing-base; }
    &l-lg { padding-left: @elder-spacing-lg; }
    &l-xl { padding-left: @elder-spacing-xl; }
    
    &r-xs { padding-right: @elder-spacing-xs; }
    &r-sm { padding-right: @elder-spacing-sm; }
    &r-base { padding-right: @elder-spacing-base; }
    &r-lg { padding-right: @elder-spacing-lg; }
    &r-xl { padding-right: @elder-spacing-xl; }
  }
  
  // 布局辅助类
  &-flex {
    display: flex;
    
    &-center {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    &-between {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    &-column {
      display: flex;
      flex-direction: column;
    }
    
    &-wrap {
      flex-wrap: wrap;
    }
  }
  
  // 可访问性辅助类
  &-focus {
    &:focus,
    &:focus-visible {
      outline: 3px solid #1677ff;
      outline-offset: 2px;
    }
  }
  
  // 高对比度辅助类
  &-high-contrast {
    color: #000000 !important;
    background-color: #ffffff !important;
    border: 2px solid #000000 !important;
  }
  
  // 触摸友好的点击区域
  &-touch-target {
    min-width: 44px;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}

// 适老化按钮增强
.elder-button {
  min-height: 48px;
  padding: @elder-spacing-base @elder-spacing-xl;
  font-size: @elder-font-size-base;
  font-weight: 500;
  border-radius: @elder-radius-base;
  border: 2px solid transparent;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
  
  &:focus,
  &:focus-visible {
    outline: 3px solid #1677ff;
    outline-offset: 2px;
  }
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &-large {
    min-height: 56px;
    padding: @elder-spacing-lg @elder-spacing-2xl;
    font-size: @elder-font-size-lg;
  }
  
  &-primary {
    background-color: #1677ff;
    color: #ffffff;
    border-color: #1677ff;
    
    &:hover {
      background-color: #0958d9;
      border-color: #0958d9;
    }
    
    &:active {
      background-color: #003eb3;
      border-color: #003eb3;
    }
  }
  
  &-success {
    background-color: #52c41a;
    color: #ffffff;
    border-color: #52c41a;
    
    &:hover {
      background-color: #389e0d;
      border-color: #389e0d;
    }
  }
  
  &-warning {
    background-color: #fa8c16;
    color: #ffffff;
    border-color: #fa8c16;
    
    &:hover {
      background-color: #d46b08;
      border-color: #d46b08;
    }
  }
  
  &-danger {
    background-color: #f5222d;
    color: #ffffff;
    border-color: #f5222d;
    
    &:hover {
      background-color: #cf1322;
      border-color: #cf1322;
    }
  }
  
  &-disabled {
    background-color: #f5f5f5;
    color: #bfbfbf;
    border-color: #d9d9d9;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
}

// 适老化输入框增强
.elder-input {
  font-size: @elder-font-size-base;
  line-height: @elder-line-height-normal;
  padding: @elder-spacing-base;
  border: 2px solid #d9d9d9;
  border-radius: @elder-radius-base;
  transition: all 0.3s ease;
  
  &:focus {
    border-color: #1677ff;
    box-shadow: 0 0 0 3px rgba(22, 119, 255, 0.1);
    outline: none;
  }
  
  &::placeholder {
    color: #8c8c8c;
    font-size: @elder-font-size-base;
  }
  
  &-large {
    font-size: @elder-font-size-lg;
    padding: @elder-spacing-lg;
    min-height: 56px;
  }
  
  &-error {
    border-color: #f5222d;
    
    &:focus {
      border-color: #f5222d;
      box-shadow: 0 0 0 3px rgba(245, 34, 45, 0.1);
    }
  }
}

// 适老化卡片增强
.elder-card {
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: @elder-radius-lg;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  &-header {
    padding: @elder-spacing-lg;
    border-bottom: 1px solid #f0f0f0;
    font-size: @elder-font-size-lg;
    font-weight: 600;
    color: #262626;
  }
  
  &-body {
    padding: @elder-spacing-lg;
    font-size: @elder-font-size-base;
    line-height: @elder-line-height-relaxed;
  }
  
  &-actions {
    padding: @elder-spacing-base @elder-spacing-lg;
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: @elder-spacing-base;
    justify-content: flex-end;
  }
}

// 适老化对话框增强
.elder-modal {
  .ant-modal-content {
    border-radius: @elder-radius-lg;
  }
  
  .ant-modal-header {
    padding: @elder-spacing-xl;
    border-bottom: 1px solid #f0f0f0;
    
    .ant-modal-title {
      font-size: @elder-font-size-xl;
      font-weight: 600;
      color: #262626;
    }
  }
  
  .ant-modal-body {
    padding: @elder-spacing-xl;
    font-size: @elder-font-size-base;
    line-height: @elder-line-height-relaxed;
  }
  
  .ant-modal-footer {
    padding: @elder-spacing-lg @elder-spacing-xl;
    border-top: 1px solid #f0f0f0;
    
    .ant-btn {
      min-height: 48px;
      padding: @elder-spacing-base @elder-spacing-xl;
      font-size: @elder-font-size-base;
      margin-left: @elder-spacing-base;
    }
  }
}

// 响应式适老化
@media (max-width: 768px) {
  .elder {
    &-text {
      font-size: @elder-font-size-lg;
      
      &-xs { font-size: @elder-font-size-sm; }
      &-sm { font-size: @elder-font-size-base; }
      &-lg { font-size: @elder-font-size-xl; }
      &-xl { font-size: @elder-font-size-2xl; }
    }
  }
  
  .elder-button {
    min-height: 52px;
    padding: @elder-spacing-lg @elder-spacing-2xl;
    font-size: @elder-font-size-lg;
    
    &-large {
      min-height: 60px;
      font-size: @elder-font-size-xl;
    }
  }
  
  .elder-input {
    font-size: @elder-font-size-lg;
    padding: @elder-spacing-lg;
    min-height: 52px;
    
    &-large {
      font-size: @elder-font-size-xl;
      min-height: 60px;
    }
  }
} 