/* 收银页面样式 */
.cashier-page {
  height: 100vh;
  background: #FAFAFA;
  
  /* 布局结构 */
  .ant-layout-content {
    padding: 0;
    background: #FAFAFA;
  }
  
  /* 主容器 */
  .cashier-content {
    height: calc(100vh - 180px); // 总高度减去状态栏和操作栏
    overflow: hidden;
    
    .main-container {
      height: 100%;
      display: flex;
      gap: 2px;
      background: #FAFAFA;
      
      /* 左侧订单区域 (30%) */
      .order-section-container {
        width: 30%;
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }
      
      /* 中间AI识别区域 (40%) */
      .ai-section-container {
        width: 40%;
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }
      
      /* 右侧菜品区域 (30%) */
      .menu-section-container {
        width: 30%;
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }
    }
  }
}

/* 适老化设计优化 */
.elder-cashier-layout {
  /* 大字体模式 */
  &.elder-large-font {
    .main-container {
      .order-section-container,
      .ai-section-container,
      .menu-section-container {
        font-size: 20px;
      }
    }
  }
  
  /* 高对比度模式 */
  &.elder-high-contrast {
    .main-container {
      .order-section-container,
      .ai-section-container,
      .menu-section-container {
        border: 3px solid #1976D2;
        background: #FFFFFF;
      }
    }
  }
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .cashier-page {
    .main-container {
      gap: 1px;
      
      .order-section-container {
        width: 28%;
      }
      
      .ai-section-container {
        width: 44%;
      }
      
      .menu-section-container {
        width: 28%;
      }
    }
  }
}

@media (max-width: 768px) {
  .cashier-page {
    .main-container {
      flex-direction: column;
      
      .order-section-container,
      .ai-section-container,
      .menu-section-container {
        width: 100%;
        height: 33.33%;
      }
    }
  }
} 