{"version": 3, "file": "static/css/main.c52367a0.css", "mappings": "AAAA,KAEE,YAAa,CACb,eAAgB,CAFhB,eAGF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YACE,wBAAyB,CAEzB,UAAY,CADZ,YAEF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAGA,YACE,4BACF,CAEA,oBAGE,sBAAwB,CADxB,kBAAoB,CAEpB,yBAA2B,CAH3B,mBAIF,CC7CA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,UAOE,kCAAmC,CACnC,iCAAkC,CAFlC,wBAAyB,CADzB,aAAc,CAHd,uEAAkF,CAClF,cAAe,CACf,eAKF,CAEA,gBAVE,WAYF,CAGA,gBACE,wBACF,CAEA,SAGE,yBACF,CAOA,gCAEE,wBAA0B,CAD1B,yBAEF,CAEA,WACE,wBACF,CAEA,uBACE,wBAA0B,CAC1B,yBACF,CAEA,UAEE,4BAA8B,CAD9B,wCAEF,CAEA,WACE,wBACF,CAEA,iBACE,wBAA0B,CAC1B,yBACF,CAGA,eACE,oBAAwB,CACxB,uBAA2B,CAC3B,iBAAqB,CACrB,mBACF,CAEA,wBACE,+BAAoC,CACpC,oBACF,CAEA,gCACE,+BAAoC,CACpC,oBACF,CAGA,YACE,wBACF,CAYA,oEACE,wBAA0B,CAC1B,yBACF,CAGA,oBAEE,WAAY,CADZ,UAEF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,SACE,4BACF,CAEA,kBACE,GAAO,SAAY,CACnB,GAAK,SAAY,CACnB,CAEA,UACE,8BACF,CAEA,mBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,OACE,2BACF,CAEA,iBACE,GAAK,kBAAqB,CAC1B,IAAM,qBAAwB,CAC9B,GAAO,kBAAqB,CAC9B,CAGA,aACE,UACE,sBACF,CAEA,YACE,uBACF,CAEA,KAGE,+BAAoC,CADpC,oBAAyB,CADzB,wBAGF,CACF,CAGA,0BACE,KACE,cACF,CAOA,oBAEE,wBAA0B,CAD1B,yBAEF,CACF,CAEA,0BACE,KACE,cACF,CAEA,SAEE,wBAA0B,CAD1B,yBAEF,CACF,CAEA,yBACE,KACE,cACF,CAOA,oBAEE,wBAA0B,CAD1B,yBAEF,CACF", "sources": ["App.css", "index.css"], "sourcesContent": [".App {\n  text-align: left;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  padding: 20px;\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 应用布局样式 */\n.ant-layout {\n  background: #FAFAFA !important;\n}\n\n.ant-layout-content {\n  padding: 0 !important;\n  margin: 0 !important;\n  height: 100vh !important;\n  overflow: hidden !important;\n} ", "/* 全局样式重置 */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nhtml, body {\n  height: 100%;\n  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;\n  font-size: 18px;\n  line-height: 1.5;\n  color: #212121;\n  background-color: #FAFAFA;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n#root {\n  height: 100%;\n}\n\n/* 适老化设计 - 确保最小字体大小 */\n.ant-typography {\n  font-size: 18px !important;\n}\n\n.ant-btn {\n  min-height: 48px !important;\n  font-size: 18px !important;\n  font-weight: 500 !important;\n}\n\n.ant-input {\n  min-height: 48px !important;\n  font-size: 18px !important;\n}\n\n.ant-select {\n  min-height: 48px !important;\n  font-size: 18px !important;\n}\n\n.ant-table {\n  font-size: 16px !important;\n}\n\n.ant-table-thead > tr > th {\n  font-size: 18px !important;\n  font-weight: 600 !important;\n}\n\n.ant-card {\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;\n  border-radius: 12px !important;\n}\n\n.ant-modal {\n  font-size: 18px !important;\n}\n\n.ant-modal-title {\n  font-size: 24px !important;\n  font-weight: 600 !important;\n}\n\n/* 高对比度模式 */\n.high-contrast {\n  --primary-color: #000000;\n  --background-color: #FFFFFF;\n  --text-color: #000000;\n  --border-color: #000000;\n}\n\n.high-contrast .ant-btn {\n  border: 2px solid #000000 !important;\n  color: #000000 !important;\n}\n\n.high-contrast .ant-btn-primary {\n  background-color: #000000 !important;\n  color: #FFFFFF !important;\n}\n\n/* 大字体模式 */\n.large-font {\n  font-size: 22px !important;\n}\n\n.large-font .ant-btn {\n  font-size: 22px !important;\n  min-height: 56px !important;\n}\n\n.large-font .ant-input {\n  font-size: 22px !important;\n  min-height: 56px !important;\n}\n\n.large-font .ant-select {\n  font-size: 22px !important;\n  min-height: 56px !important;\n}\n\n/* 滚动条样式 */\n::-webkit-scrollbar {\n  width: 12px;\n  height: 12px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 6px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 6px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a1a1a1;\n}\n\n/* 动画效果 */\n.fade-in {\n  animation: fadeIn 0.3s ease-in;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n.slide-in {\n  animation: slideIn 0.3s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n.pulse {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n}\n\n/* 打印样式 */\n@media print {\n  .no-print {\n    display: none !important;\n  }\n  \n  .print-only {\n    display: block !important;\n  }\n  \n  body {\n    font-size: 12px !important;\n    color: #000000 !important;\n    background-color: #FFFFFF !important;\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 1366px) {\n  body {\n    font-size: 16px;\n  }\n  \n  .ant-btn {\n    min-height: 44px !important;\n    font-size: 16px !important;\n  }\n  \n  .ant-input {\n    min-height: 44px !important;\n    font-size: 16px !important;\n  }\n}\n\n@media (max-width: 1024px) {\n  body {\n    font-size: 18px;\n  }\n  \n  .ant-btn {\n    min-height: 48px !important;\n    font-size: 18px !important;\n  }\n}\n\n@media (max-width: 768px) {\n  body {\n    font-size: 20px;\n  }\n  \n  .ant-btn {\n    min-height: 52px !important;\n    font-size: 20px !important;\n  }\n  \n  .ant-input {\n    min-height: 52px !important;\n    font-size: 20px !important;\n  }\n} "], "names": [], "sourceRoot": ""}