<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>助老订餐收银系统 - 启动中</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', 'Arial', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100vh;
      color: white;
      overflow: hidden;
    }

    .splash-container {
      text-align: center;
      animation: fadeIn 0.8s ease-in-out;
    }

    .logo {
      font-size: 64px;
      margin-bottom: 20px;
      filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    }

    .app-name {
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 10px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    .app-description {
      font-size: 16px;
      opacity: 0.9;
      margin-bottom: 40px;
    }

    .loading-container {
      width: 200px;
      margin: 0 auto;
    }

    .loading-bar {
      width: 100%;
      height: 4px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 15px;
    }

    .loading-progress {
      height: 100%;
      background: linear-gradient(90deg, #fff, #f0f0f0);
      border-radius: 2px;
      animation: loading 2s ease-in-out infinite;
      width: 0%;
    }

    .loading-text {
      font-size: 14px;
      opacity: 0.8;
      animation: pulse 1.5s ease-in-out infinite;
    }

    .version {
      position: absolute;
      bottom: 20px;
      right: 20px;
      font-size: 12px;
      opacity: 0.7;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes loading {
      0% {
        width: 0%;
        transform: translateX(-100%);
      }
      50% {
        width: 100%;
        transform: translateX(0%);
      }
      100% {
        width: 100%;
        transform: translateX(100%);
      }
    }

    @keyframes pulse {
      0%, 100% {
        opacity: 0.8;
      }
      50% {
        opacity: 1;
      }
    }

    /* 适老化设计 */
    @media (min-width: 1200px) {
      .logo {
        font-size: 80px;
      }

      .app-name {
        font-size: 32px;
      }

      .app-description {
        font-size: 18px;
      }

      .loading-text {
        font-size: 16px;
      }
    }

    /* 高对比度模式 */
    @media (prefers-contrast: high) {
      body {
        background: #000;
        color: #fff;
      }

      .loading-bar {
        background: #333;
      }

      .loading-progress {
        background: #fff;
      }
    }
  </style>
</head>
<body>
  <div class="splash-container">
    <div class="logo">🍽️</div>
    <div class="app-name">助老订餐收银系统</div>
    <div class="app-description">专为老年人设计的智能收银系统</div>
    
    <div class="loading-container">
      <div class="loading-bar">
        <div class="loading-progress"></div>
      </div>
      <div class="loading-text">正在启动系统...</div>
    </div>
  </div>

  <div class="version">v1.0.0</div>

  <script>
    // 模拟加载进度
    let progress = 0;
    const progressBar = document.querySelector('.loading-progress');
    const loadingText = document.querySelector('.loading-text');
    
    const loadingSteps = [
      '正在启动系统...',
      '正在初始化界面...',
      '正在加载组件...',
      '正在连接服务...',
      '即将完成...'
    ];

    function updateProgress() {
      progress += Math.random() * 20;
      
      if (progress > 100) {
        progress = 100;
        loadingText.textContent = '启动完成';
        return;
      }

      progressBar.style.width = progress + '%';
      
      const stepIndex = Math.floor(progress / 20);
      if (stepIndex < loadingSteps.length) {
        loadingText.textContent = loadingSteps[stepIndex];
      }

      setTimeout(updateProgress, 200 + Math.random() * 300);
    }

    // 开始加载动画
    setTimeout(updateProgress, 500);

    // 防止选择文本
    document.addEventListener('selectstart', (e) => {
      e.preventDefault();
    });

    // 防止拖拽
    document.addEventListener('dragstart', (e) => {
      e.preventDefault();
    });
  </script>
</body>
</html> 