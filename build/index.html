<!doctype html><html lang="zh-CN"><head><meta charset="utf-8"/><link rel="icon" href="./favicon.ico"/><meta name="viewport" content="width=device-width,initial-scale=1"/><meta name="theme-color" content="#1976D2"/><meta name="description" content="助老订餐收银系统 - 专为老年人友好设计的智能收银系统"/><meta name="keywords" content="收银系统,助老,AI识别,智能点餐,适老化"/><meta name="author" content="助老收银团队"/><link rel="apple-touch-icon" href="./logo192.png"/><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="default"/><meta name="apple-mobile-web-app-title" content="助老收银"/><link rel="manifest" href="./manifest.json"/><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><meta name="robots" content="noindex, nofollow"/><meta name="format-detection" content="telephone=no"/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="renderer" content="webkit"/><title>助老订餐收银系统</title><style>.loading-container{position:fixed;top:0;left:0;width:100%;height:100%;background:linear-gradient(135deg,#1976d2,#1565c0);display:flex;align-items:center;justify-content:center;z-index:9999;color:#fff;font-family:'Microsoft YaHei',Arial,sans-serif}.loading-content{text-align:center;animation:fadeIn .8s ease-in}.loading-logo{font-size:72px;margin-bottom:24px;animation:pulse 2s infinite}.loading-title{font-size:36px;font-weight:700;margin-bottom:12px}.loading-subtitle{font-size:18px;opacity:.9;margin-bottom:32px}.loading-spinner{width:60px;height:60px;border:6px solid rgba(255,255,255,.3);border-radius:50%;border-top-color:#fff;animation:spin 1s ease-in-out infinite;margin:0 auto}@keyframes fadeIn{from{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes pulse{0%{transform:scale(1)}50%{transform:scale(1.1)}100%{transform:scale(1)}}@keyframes spin{to{transform:rotate(360deg)}}.loading-container.hidden{opacity:0;pointer-events:none;transition:opacity .5s ease-out}</style><script defer="defer" src="./static/js/main.59971946.js"></script><link href="./static/css/main.c52367a0.css" rel="stylesheet"></head><body><noscript>您需要启用 JavaScript 才能运行此应用程序。</noscript><div id="loading" class="loading-container"><div class="loading-content"><div class="loading-logo">🏪</div><div class="loading-title">助老订餐收银系统</div><div class="loading-subtitle">正在加载，请稍候...</div><div class="loading-spinner"></div></div></div><div id="root"></div><div id="browser-outdated" style="display:none"><div style="position:fixed;top:0;left:0;width:100%;height:100%;background:#f44336;color:#fff;display:flex;align-items:center;justify-content:center;font-family:'Microsoft YaHei',Arial,sans-serif;text-align:center;z-index:10000"><div><h1 style="font-size:36px;margin-bottom:24px">⚠️ 浏览器版本过低</h1><p style="font-size:20px;margin-bottom:16px">为了更好的使用体验，请升级您的浏览器</p><p style="font-size:16px;opacity:.9">推荐使用Chrome、Edge、Firefox等现代浏览器</p></div></div></div><script>"fetch"in window&&"Promise"in window&&"Map"in window&&"Set"in window&&"Symbol"in window?(window.addEventListener("load",(function(){setTimeout((function(){var e=document.getElementById("loading");e&&(e.classList.add("hidden"),setTimeout((function(){e.remove()}),500))}),1e3)})),window.addEventListener("error",(function(e){console.error("应用加载错误:",e.error)})),window.addEventListener("unhandledrejection",(function(e){console.error("未处理的Promise错误:",e.reason)}))):document.getElementById("browser-outdated").style.display="block"</script></body></html>