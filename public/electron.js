const { app, BrowserWindow, Menu, ipcMain, dialog, Tray, shell, session, nativeTheme } = require('electron');
const { autoUpdater } = require('electron-updater');
const path = require('path');
const isDev = require('electron-is-dev');
const { spawn } = require('child_process');

let mainWindow = null;
let tray = null;
let splashWindow = null;

// 配置自动更新
if (!isDev) {
  autoUpdater.checkForUpdatesAndNotify();
}

/**
 * 创建启动画面
 */
function createSplashWindow() {
  splashWindow = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    alwaysOnTop: true,
    transparent: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    }
  });

  splashWindow.loadFile(path.join(__dirname, 'splash.html'));

  splashWindow.on('closed', () => {
    splashWindow = null;
  });
}

/**
 * 创建主窗口
 */
function createWindow() {
  // 先显示启动画面
  createSplashWindow();

  // 创建主窗口
  mainWindow = new BrowserWindow({
    width: 1440,
    height: 900,
    minWidth: 1024,
    minHeight: 768,
    icon: path.join(__dirname, process.platform === 'win32' ? 'icon.ico' : 'icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
    show: false,
    backgroundColor: '#ffffff',
    vibrancy: process.platform === 'darwin' ? 'under-window' : null
  });

  // 设置窗口标题
  mainWindow.setTitle('助老订餐收银系统 v1.0.0');

  // 加载应用
  const startUrl = isDev 
    ? 'http://localhost:3000' 
    : `file://${path.join(__dirname, '../build/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // 窗口准备就绪后显示
  mainWindow.once('ready-to-show', () => {
    // 关闭启动画面
    if (splashWindow) {
      splashWindow.close();
    }
    
    mainWindow.show();
    mainWindow.focus();
    
    // 开发环境下打开DevTools
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // 窗口关闭事件
  mainWindow.on('close', (event) => {
    if (process.platform === 'darwin') {
      event.preventDefault();
      mainWindow.hide();
    } else {
      // Windows/Linux平台询问用户是否最小化到托盘
      const choice = dialog.showMessageBoxSync(mainWindow, {
        type: 'question',
        buttons: ['最小化到托盘', '退出程序'],
        defaultId: 0,
        title: '确认退出',
        message: '您希望最小化到系统托盘还是完全退出程序？'
      });
      
      if (choice === 0) {
        event.preventDefault();
        mainWindow.hide();
      }
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 窗口状态变化事件
  mainWindow.on('minimize', () => {
    if (process.platform === 'win32') {
      mainWindow.hide();
    }
  });

  // 拦截新窗口打开
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // 设置菜单和托盘
  createMenu();
  createTray();
  
  // 恢复窗口状态
  restoreWindowState();
}

/**
 * 创建系统托盘
 */
function createTray() {
  const trayIconPath = path.join(__dirname, process.platform === 'win32' ? 'tray-icon.ico' : 'tray-icon.png');
  
  tray = new Tray(trayIconPath);
  
  const contextMenu = Menu.buildFromTemplate([
    {
      label: '显示主窗口',
      click: () => {
        if (mainWindow) {
          if (mainWindow.isMinimized()) mainWindow.restore();
          mainWindow.show();
          mainWindow.focus();
        }
      }
    },
    { type: 'separator' },
    {
      label: '新建订单',
      accelerator: 'CmdOrCtrl+N',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.webContents.send('menu-new-order');
        }
      }
    },
    {
      label: '会员查询',
      accelerator: 'CmdOrCtrl+M',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.webContents.send('menu-member');
        }
      }
    },
    { type: 'separator' },
    {
      label: '系统设置',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.webContents.send('menu-settings');
        }
      }
    },
    { type: 'separator' },
    {
      label: '退出程序',
      click: () => {
        app.quit();
      }
    }
  ]);
  
  tray.setContextMenu(contextMenu);
  tray.setToolTip('助老订餐收银系统');
  
  // 双击托盘图标显示窗口
  tray.on('double-click', () => {
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide();
      } else {
        mainWindow.show();
        mainWindow.focus();
      }
    }
  });
}

/**
 * 创建菜单
 */
function createMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '新建订单',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-order');
          }
        },
        {
          label: '打印小票',
          accelerator: 'CmdOrCtrl+P',
          click: () => {
            mainWindow.webContents.send('menu-print');
          }
        },
        {
          label: '导出数据',
          accelerator: 'CmdOrCtrl+E',
          click: () => {
            mainWindow.webContents.send('menu-export');
          }
        },
        { type: 'separator' },
        {
          label: process.platform === 'darwin' ? '隐藏窗口' : '最小化到托盘',
          accelerator: process.platform === 'darwin' ? 'Cmd+H' : 'Ctrl+H',
          click: () => {
            mainWindow.hide();
          }
        },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: '订单',
      submenu: [
        {
          label: '订单查询',
          accelerator: 'CmdOrCtrl+F',
          click: () => {
            mainWindow.webContents.send('menu-search-order');
          }
        },
        {
          label: '退款处理',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.webContents.send('menu-refund');
          }
        },
        {
          label: '订单历史',
          accelerator: 'CmdOrCtrl+Shift+H',
          click: () => {
            mainWindow.webContents.send('menu-order-history');
          }
        }
      ]
    },
    {
      label: '会员',
      submenu: [
        {
          label: '会员查询',
          accelerator: 'CmdOrCtrl+M',
          click: () => {
            mainWindow.webContents.send('menu-member');
          }
        },
        {
          label: '会员充值',
          accelerator: 'CmdOrCtrl+T',
          click: () => {
            mainWindow.webContents.send('menu-topup');
          }
        },
        {
          label: '会员管理',
          accelerator: 'CmdOrCtrl+Shift+M',
          click: () => {
            mainWindow.webContents.send('menu-member-management');
          }
        }
      ]
    },
    {
      label: 'AI识别',
      submenu: [
        {
          label: '开始识别',
          accelerator: 'CmdOrCtrl+Space',
          click: () => {
            mainWindow.webContents.send('menu-start-recognition');
          }
        },
        {
          label: '停止识别',
          accelerator: 'CmdOrCtrl+Shift+Space',
          click: () => {
            mainWindow.webContents.send('menu-stop-recognition');
          }
        },
        {
          label: 'AI设置',
          click: () => {
            mainWindow.webContents.send('menu-ai-settings');
          }
        }
      ]
    },
    {
      label: '工具',
      submenu: [
        {
          label: '系统设置',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            mainWindow.webContents.send('menu-settings');
          }
        },
        {
          label: '数据备份',
          click: () => {
            mainWindow.webContents.send('menu-backup');
          }
        },
        {
          label: '数据恢复',
          click: () => {
            mainWindow.webContents.send('menu-restore');
          }
        },
        { type: 'separator' },
        {
          label: '重新加载',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.webContents.reload();
          }
        },
        {
          label: '开发者工具',
          accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
          click: () => {
            mainWindow.webContents.toggleDevTools();
          }
        }
      ]
    },
    {
      label: '窗口',
      submenu: [
        {
          label: '最小化',
          accelerator: 'CmdOrCtrl+M',
          click: () => {
            mainWindow.minimize();
          }
        },
        {
          label: '全屏',
          accelerator: process.platform === 'darwin' ? 'Ctrl+Cmd+F' : 'F11',
          click: () => {
            mainWindow.setFullScreen(!mainWindow.isFullScreen());
          }
        },
        {
          label: '置顶',
          type: 'checkbox',
          click: (menuItem) => {
            mainWindow.setAlwaysOnTop(menuItem.checked);
          }
        }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '用户手册',
          accelerator: 'F1',
          click: () => {
            mainWindow.webContents.send('menu-help');
          }
        },
        {
          label: '快捷键指南',
          click: () => {
            mainWindow.webContents.send('menu-shortcuts');
          }
        },
        {
          label: '检查更新',
          click: () => {
            if (!isDev) {
              autoUpdater.checkForUpdatesAndNotify();
            } else {
              dialog.showMessageBox(mainWindow, {
                type: 'info',
                title: '检查更新',
                message: '开发环境下无法检查更新'
              });
            }
          }
        },
        { type: 'separator' },
        {
          label: '关于',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于助老订餐收银系统',
              message: '助老订餐收银系统',
              detail: `版本: ${app.getVersion()}\n基于React + TypeScript + Electron\n专为老年人友好设计的智能收银系统\n\n系统信息:\n操作系统: ${process.platform}\nElectron版本: ${process.versions.electron}\nNode.js版本: ${process.versions.node}`
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

/**
 * 保存窗口状态
 */
function saveWindowState() {
  if (mainWindow) {
    const bounds = mainWindow.getBounds();
    const isMaximized = mainWindow.isMaximized();
    const isFullScreen = mainWindow.isFullScreen();
    
    // 在实际项目中，这里应该保存到配置文件
    console.log('保存窗口状态:', { bounds, isMaximized, isFullScreen });
  }
}

/**
 * 恢复窗口状态
 */
function restoreWindowState() {
  // 在实际项目中，这里应该从配置文件读取
  // 示例代码：
  // const savedState = loadWindowState();
  // if (savedState) {
  //   mainWindow.setBounds(savedState.bounds);
  //   if (savedState.isMaximized) mainWindow.maximize();
  //   if (savedState.isFullScreen) mainWindow.setFullScreen(true);
  // }
}

// App事件监听
app.whenReady().then(() => {
  // 设置安全策略
  session.defaultSession.webSecurity = true;
  
  // 禁用不安全的协议
  session.defaultSession.protocol.registerSchemesAsPrivileged([
    { scheme: 'app', privileges: { secure: true, standard: true } }
  ]);

  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    } else if (mainWindow) {
      mainWindow.show();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  saveWindowState();
});

// 自动更新事件
autoUpdater.on('checking-for-update', () => {
  console.log('正在检查更新...');
});

autoUpdater.on('update-available', () => {
  dialog.showMessageBox(mainWindow, {
    type: 'info',
    title: '更新可用',
    message: '发现新版本，正在后台下载...',
    buttons: ['确定']
  });
});

autoUpdater.on('update-not-available', () => {
  console.log('当前已是最新版本');
});

autoUpdater.on('error', (err) => {
  console.error('自动更新错误:', err);
});

autoUpdater.on('download-progress', (progressObj) => {
  const logMessage = `下载进度: ${progressObj.percent}%`;
  console.log(logMessage);
});

autoUpdater.on('update-downloaded', () => {
  dialog.showMessageBox(mainWindow, {
    type: 'info',
    title: '更新已下载',
    message: '更新已下载完成，重启应用后生效',
    buttons: ['立即重启', '稍后重启']
  }).then((result) => {
    if (result.response === 0) {
      autoUpdater.quitAndInstall();
    }
  });
});

// IPC通信处理
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-system-info', () => {
  return {
    platform: process.platform,
    arch: process.arch,
    version: process.version,
    electronVersion: process.versions.electron,
    chromeVersion: process.versions.chrome
  };
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('open-external', async (event, url) => {
  await shell.openExternal(url);
});

ipcMain.handle('show-item-in-folder', async (event, path) => {
  shell.showItemInFolder(path);
});

ipcMain.handle('set-window-always-on-top', (event, flag) => {
  mainWindow.setAlwaysOnTop(flag);
});

ipcMain.handle('minimize-to-tray', () => {
  mainWindow.hide();
});

ipcMain.handle('check-for-updates', () => {
  if (!isDev) {
    autoUpdater.checkForUpdatesAndNotify();
  }
});

// 打印功能
ipcMain.handle('print-receipt', async (event, options = {}) => {
  try {
    if (mainWindow) {
      await mainWindow.webContents.print(options);
      return { success: true };
    }
    return { success: false, error: '窗口不存在' };
  } catch (error) {
    console.error('打印失败:', error);
    return { success: false, error: error.message };
  }
});

// 文件操作
ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const fs = require('fs').promises;
    const data = await fs.readFile(filePath, 'utf8');
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('write-file', async (event, filePath, data) => {
  try {
    const fs = require('fs').promises;
    await fs.writeFile(filePath, data, 'utf8');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// 防止多开
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  dialog.showErrorBox('未捕获的异常', error.message);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// 优雅退出
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在优雅退出...');
  app.quit();
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在优雅退出...');
  app.quit();
}); 