<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#1976D2" />
    <meta name="description" content="助老订餐收银系统 - 专为老年人友好设计的智能收银系统" />
    <meta name="keywords" content="收银系统,助老,AI识别,智能点餐,适老化" />
    <meta name="author" content="助老收银团队" />
    
    <!-- 苹果设备配置 -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="助老收银" />
    
    <!-- PWA配置 -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 无障碍优化 -->
    <meta name="robots" content="noindex, nofollow" />
    <meta name="format-detection" content="telephone=no" />
    
    <!-- 性能优化 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="renderer" content="webkit" />
    
    <title>助老订餐收银系统</title>
    
    <!-- 内联关键CSS以提升首屏性能 -->
    <style>
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1976D2, #1565C0);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        font-family: 'Microsoft YaHei', Arial, sans-serif;
      }
      
      .loading-content {
        text-align: center;
        animation: fadeIn 0.8s ease-in;
      }
      
      .loading-logo {
        font-size: 72px;
        margin-bottom: 24px;
        animation: pulse 2s infinite;
      }
      
      .loading-title {
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 12px;
      }
      
      .loading-subtitle {
        font-size: 18px;
        opacity: 0.9;
        margin-bottom: 32px;
      }
      
      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 6px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
        margin: 0 auto;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      /* 隐藏加载画面 */
      .loading-container.hidden {
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.5s ease-out;
      }
    </style>
  </head>
  <body>
    <noscript>您需要启用 JavaScript 才能运行此应用程序。</noscript>
    
    <!-- 加载画面 -->
    <div id="loading" class="loading-container">
      <div class="loading-content">
        <div class="loading-logo">🏪</div>
        <div class="loading-title">助老订餐收银系统</div>
        <div class="loading-subtitle">正在加载，请稍候...</div>
        <div class="loading-spinner"></div>
      </div>
    </div>
    
    <!-- 应用根节点 -->
    <div id="root"></div>
    
    <!-- 浏览器不支持提示 -->
    <div id="browser-outdated" style="display: none;">
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f44336;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        text-align: center;
        z-index: 10000;
      ">
        <div>
          <h1 style="font-size: 36px; margin-bottom: 24px;">⚠️ 浏览器版本过低</h1>
          <p style="font-size: 20px; margin-bottom: 16px;">为了更好的使用体验，请升级您的浏览器</p>
          <p style="font-size: 16px; opacity: 0.9;">推荐使用Chrome、Edge、Firefox等现代浏览器</p>
        </div>
      </div>
    </div>
    
    <!-- 浏览器兼容性检测 -->
    <script>
      // 检测浏览器兼容性
      (function() {
        var isSupported = (
          'fetch' in window &&
          'Promise' in window &&
          'Map' in window &&
          'Set' in window &&
          'Symbol' in window
        );
        
        if (!isSupported) {
          document.getElementById('browser-outdated').style.display = 'block';
          return;
        }
        
        // 应用加载完成后隐藏加载画面
        window.addEventListener('load', function() {
          setTimeout(function() {
            var loading = document.getElementById('loading');
            if (loading) {
              loading.classList.add('hidden');
              setTimeout(function() {
                loading.remove();
              }, 500);
            }
          }, 1000);
        });
        
        // 错误处理
        window.addEventListener('error', function(e) {
          console.error('应用加载错误:', e.error);
        });
        
        // 未处理的Promise错误
        window.addEventListener('unhandledrejection', function(e) {
          console.error('未处理的Promise错误:', e.reason);
        });
      })();
    </script>
  </body>
</html> 